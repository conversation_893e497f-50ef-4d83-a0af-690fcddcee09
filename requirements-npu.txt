# ============================================================================
# aibi语音交互系统 - NPU环境依赖配置
# 版本: v2.0
# 适用于: 使用华为昇腾NPU进行AI模型推理的环境
# 昇腾版本: CANN 8.0+
# 更新日期: 2025-01-23
# ============================================================================

# ==================== 核心框架依赖 ====================
# PyTorch NPU版本 - 深度学习框架
# 注意: 需要根据实际昇腾CANN版本选择对应的torch_npu版本
torch==2.1.0
torch-npu>=2.1.0

# ONNX Runtime NPU版本 - 模型推理引擎
# 注意: 需要根据实际NPU环境安装对应版本
onnxruntime>=1.15.0

# ==================== 音频处理核心库 ====================
# 音频文件读写和处理
soundfile==0.12.1
librosa==0.10.2

# 音频设备接口
pyaudio==0.2.14

# 语音活动检测
webrtcvad==2.0.10

# ==================== 语音AI模型库 ====================
# FunASR语音识别框架
funasr>=1.0.0

# ModelScope模型管理
modelscope==1.20.0

# 文本前端处理
WeTextProcessing==1.0.3

# ==================== 基础工具库 ====================
# 数值计算
numpy>=1.21.0,<2.0.0
scipy>=1.7.0,<2.0.0

# 配置管理
PyYAML>=6.0,<7.0
omegaconf>=2.3.0,<3.0.0

# 网络请求
requests>=2.25.0,<3.0.0
httpx>=0.24.0,<1.0.0

# 进度条和日志
tqdm>=4.62.0,<5.0.0
colorama>=0.4.4,<1.0.0

# ==================== 数据处理库 ====================
# 数据分析
pandas>=1.3.0,<3.0.0

# 协议缓冲区
protobuf>=4.25,<5.0

# ==================== 开发工具库 ====================
# 类型检查
pydantic>=2.7.0,<3.0.0

# 网络工具
inflect>=7.3.1,<8.0.0

# 可视化（可选）
matplotlib>=3.7.5,<4.0.0

# ==================== 系统兼容性 ====================
# 跨平台兼容
pyworld>=0.3.4,<1.0.0

# 富文本输出
rich>=13.7.1,<14.0.0

# ==================== NPU特定依赖 ====================
# 昇腾NPU运行时库
# 注意: 以下依赖需要在昇腾环境中安装
# acl
# te
# topi
# hccl

# ==================== 安装说明 ====================
# NPU环境安装步骤:
# 1. 安装昇腾CANN开发套件
# 2. 安装torch_npu: pip install torch_npu
# 3. 配置环境变量: source /usr/local/Ascend/ascend-toolkit/set_env.sh
# 4. 安装其他依赖: pip install -r requirements-npu.txt