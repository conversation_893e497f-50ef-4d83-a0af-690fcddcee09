import sys
import os
# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from modules.tts import TTSProcessor
import yaml
import numpy as np

def test_tts_processor():
    # 加载配置
    config_path = os.path.join(os.path.dirname(__file__), '../config.yaml')
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建TTS处理器
    tts = TTSProcessor(config)
    
    # 测试语音合成
    test_text = "你好，这是一个TTS测试。"
    audio = tts.synthesize(test_text)
    
    assert audio is not None
    assert isinstance(audio, np.ndarray)
    assert audio.dtype == np.float32
    assert len(audio) > 0
    print(f"TTS测试成功：生成音频长度 {len(audio)} 样本")

if __name__ == "__main__":
    test_tts_processor()
    print("TTS模块测试通过！")
