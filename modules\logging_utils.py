import logging

def setup_logger(name, config):
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        config: 配置管理器对象
    """
    # 从配置中获取日志设置
    log_level = config.get("logging.level", "INFO")
    log_file = config.get("logging.log_file", "logs/aibi.log")
    console_enabled = config.get("logging.console_enabled", True)
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
    
    # 清除现有处理器
    logger.handlers.clear()
    
    # 添加文件处理器
    if config.get("logging.file_enabled", True):
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
        file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    # 添加控制台处理器
    if console_enabled:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
        console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    return logger
