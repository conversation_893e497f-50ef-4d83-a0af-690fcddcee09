class InterruptManager:
    def __init__(self, state_manager):
        self.state_manager = state_manager
        self.interrupted = False

    def check_for_interrupt(self, vad_result, tts_playing, asr_processing):
        # 仅在已唤醒状态下生效
        if self.state_manager.get_state() == "已唤醒":
            if vad_result and (tts_playing or asr_processing):
                self.interrupted = True
                # 这里可调用TTS/ASR/LLM的中断接口
                print("检测到插话，中断当前任务！")
                return True
        self.interrupted = False
        return False
