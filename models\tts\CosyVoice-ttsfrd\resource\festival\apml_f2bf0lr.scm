;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;                                                                       ;;
;;;                Centre for Speech Technology Research                  ;;
;;;                     University of Edinburgh, UK                       ;;
;;;                         Copyright (c) 2002                            ;;
;;;                        All Rights Reserved.                           ;;
;;;                                                                       ;;
;;;  Permission is hereby granted, free of charge, to use and distribute  ;;
;;;  this software and its documentation without restriction, including   ;;
;;;  without limitation the rights to use, copy, modify, merge, publish,  ;;
;;;  distribute, sublicense, and/or sell copies of this work, and to      ;;
;;;  permit persons to whom this work is furnished to do so, subject to   ;;
;;;  the following conditions:                                            ;;
;;;   1. The code must retain the above copyright notice, this list of    ;;
;;;      conditions and the following disclaimer.                         ;;
;;;   2. Any modifications must be clearly marked as such.                ;;
;;;   3. Original authors' names are not deleted.                         ;;
;;;   4. The authors' names are not used to endorse or promote products   ;;
;;;      derived from this software without specific prior written        ;;
;;;      permission.                                                      ;;
;;;                                                                       ;;
;;;  THE UNIVERSITY OF EDINBURGH AND THE CONTRIBUTORS TO THIS WORK        ;;
;;;  DISCLAIM ALL WARRANTIES WITH REGARD TO THIS SOFTWARE, INCLUDING      ;;
;;;  ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO EVENT   ;;
;;;  SHALL THE UNIVERSITY OF EDINBURGH NOR THE CONTRIBUTORS BE LIABLE     ;;
;;;  FOR ANY SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES    ;;
;;;  WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN   ;;
;;;  AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION,          ;;
;;;  ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF       ;;
;;;  THIS SOFTWARE.                                                       ;;
;;;                                                                       ;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;                         Author: Rob Clark
;;;                         Date:   July 2002
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;
;; APML.f0 trees.
;;
;;

(set! apml_f2b_f0_lr_start
'(
( Intercept                       163.9871			 )
( pp.lisp_apml_tgtype             -3.1750		(1) )
( p.lisp_apml_tgtype              5.0332		(1) )
( lisp_apml_tgtype                0.0000		(1) )
( n.lisp_apml_tgtype              17.7799		(1) )
( nn.lisp_apml_tgtype             13.6845		(1) )
( pp.lisp_apml_tgtype             0.0000		(2) )
( p.lisp_apml_tgtype              0.0000		(2) )
( lisp_apml_tgtype                0.0000		(2) )
( n.lisp_apml_tgtype              0.0000		(2) )
( nn.lisp_apml_tgtype             0.0000		(2) )
( pp.lisp_apml_tgtype             0.0000		(3) )
( p.lisp_apml_tgtype              0.0000		(3) )
( lisp_apml_tgtype                -9.7245		(3) )
( n.lisp_apml_tgtype              0.0000		(3) )
( nn.lisp_apml_tgtype             -2.4009		(3) )
( pp.lisp_apml_iecount            0.0000			 )
( p.lisp_apml_iecount             -0.4484			 )
( lisp_apml_iecount               0.0000			 )
( n.lisp_apml_iecount             -2.0165			 )
( nn.lisp_apml_iecount            0.0000			 )
( pp.tobi_accent                  0.0000		(H*) )
( p.tobi_accent                   11.1239		(H*) )
( tobi_accent                     21.5164		(H*) )
( n.tobi_accent                   -2.5990		(H*) )
( nn.tobi_accent                  -6.5307		(H*) )
( pp.tobi_accent                  0.0000		(L*) )
( p.tobi_accent                   -10.0000		(L*) )
( tobi_accent                     -5.0000		(L*) )
( n.tobi_accent                   -10.6798		(L*) )
( nn.tobi_accent                  -5.6561		(L*) )
( pp.tobi_accent                  5.3577		(L*+H) )
( p.tobi_accent                   60.0000		(L*+H) )
( tobi_accent                     -5.0000		(L*+H) )
( n.tobi_accent                   0.0000		(L*+H) )
( nn.tobi_accent                  0.0000		(L*+H) )
( pp.tobi_accent                  0.0000		(L+H*) )
( p.tobi_accent                   11.1200		(L+H*) )
( tobi_accent                     21.5200		(L+H*) )
( n.tobi_accent                   -2.6000		(L+H*) )
( nn.tobi_accent                  -6.5300		(L+H*) )
( pp.tobi_endtone                 0.0000		(L-L%) )
( p.tobi_endtone                  -0.6164		(L-L%) )
( tobi_endtone                    -50		        (L-L%) )
( n.tobi_endtone                  -10.8729		(L-L%) )
( nn.tobi_endtone                 -7.6522		(L-L%) )
( pp.tobi_endtone                 0.7583		(L-H%) )
( p.tobi_endtone                  0.0000		(L-H%) )
( tobi_endtone                    -20.0000		(L-H%) )
( n.tobi_endtone                  -11.8935		(L-H%) )
( nn.tobi_endtone                 -7.2012		(L-H%) )
( pp.tobi_endtone                 0.0000		(H-L%) )
( p.tobi_endtone                  0.0000		(H-L%) )
( tobi_endtone                    4.0790		(H-L%) )
( n.tobi_endtone                  -19.3463		(H-L%) )
( nn.tobi_endtone                 -29.3615		(H-L%) )
( pp.tobi_endtone                 0.0000		(H-H%) )
( p.tobi_endtone                  0.0000		(H-H%) )
( tobi_endtone                    0.0000		(H-H%) )
( n.tobi_endtone                  0.0000		(H-H%) )
( nn.tobi_endtone                 0.0000		(H-H%) )
( pp.tobi_endtone                 0.0000		(L-) )
( p.tobi_endtone                  -15.1702		(L-) )
( tobi_endtone                    0.0000		(L-) )
( n.tobi_endtone                  -14.5562		(L-) )
( nn.tobi_endtone                 0.0000		(L-) )
( pp.tobi_endtone                 -13.5046		(H-) )
( p.tobi_endtone                  0.0000		(H-) )
( tobi_endtone                    6.3377		(H-) )
( n.tobi_endtone                  -6.8631		(H-) )
( nn.tobi_endtone                 0.0000		(H-) )
( p.tobi_accent                   60.0000		(L+H*L-H%) )
( tobi_accent                     -60.0000		(L+H*L-H%) )
( n.tobi_accent                   0.0000		(L+H*L-H%) )
( pp.syl_break                    0.0000			 )
( p.syl_break                     0.0000			 )
( syl_break                       0.6417			 )
( n.syl_break                     1.3532			 )
( nn.syl_break                    1.0724			 )
( pp.stress                       0.0000			 )
( p.stress                        -0.6193			 )
( stress                          2.4121			 )
( n.stress                        0.0000			 )
( nn.stress                       2.5478			 )
( syl_in                          -1.4373			 )
( syl_out                         0.4181			 )
( ssyl_in                         0.0000			 )
( ssyl_out                        0.6125			 )
( asyl_in                         0.0000			 )
( asyl_out                        0.9906			 )
( last_accent                     0.0000			 )
( next_accent                     -0.3700			 )
( sub_phrases                     0.0000			 )
( lisp_l_spread                   -60.0000			 )
))

(set! apml_f2b_f0_lr_left
'(
( Intercept                       162.1173			 )
( pp.lisp_apml_tgtype             -1.5875		(1) )
( p.lisp_apml_tgtype              4.8101		(1) )
( lisp_apml_tgtype                12.8265		(1) )
( n.lisp_apml_tgtype              16.3027		(1) )
( nn.lisp_apml_tgtype             13.3225		(1) )
( pp.lisp_apml_tgtype             0.0000		(2) )
( p.lisp_apml_tgtype              1.7434		(2) )
( lisp_apml_tgtype                6.7783		(2) )
( n.lisp_apml_tgtype              0.6679		(2) )
( nn.lisp_apml_tgtype             0.0000		(2) )
( pp.lisp_apml_tgtype             1.6494		(3) )
( p.lisp_apml_tgtype              1.2861		(3) )
( lisp_apml_tgtype                -2.0724		(3) )
( n.lisp_apml_tgtype              0.0000		(3) )
( nn.lisp_apml_tgtype             -1.2004		(3) )
( pp.lisp_apml_iecount            0.0000			 )
( p.lisp_apml_iecount             -0.5857			 )
( lisp_apml_iecount               0.0000			 )
( n.lisp_apml_iecount             -2.3543			 )
( nn.lisp_apml_iecount            0.0000			 )
( pp.tobi_accent                  0.0000		(H*) )
( p.tobi_accent                   8.5867		(H*) )
( tobi_accent                     21.2169		(H*) )
( n.tobi_accent                   -1.2995		(H*) )
( nn.tobi_accent                  -6.5056		(H*) )
( pp.tobi_accent                  0.0000		(L*) )
( p.tobi_accent                   -7.5000		(L*) )
( tobi_accent                     -25.0000		(L*) )
( n.tobi_accent                   -8.3939		(L*) )
( nn.tobi_accent                  -4.5688		(L*) )
( pp.tobi_accent                  2.6789		(L*+H) )
( p.tobi_accent                   45.0000		(L*+H) )
( tobi_accent                     -17.5000		(L*+H) )
( n.tobi_accent                   -1.3600		(L*+H) )
( nn.tobi_accent                  0.0000		(L*+H) )
( pp.tobi_accent                  0.0000		(L+H*) )
( p.tobi_accent                   8.5850		(L+H*) )
( tobi_accent                     21.2200		(L+H*) )
( n.tobi_accent                   -1.3000		(L+H*) )
( nn.tobi_accent                  -6.5050		(L+H*) )
( pp.tobi_endtone                 1.8117		(L-L%) )
( p.tobi_endtone                  -0.1681		(L-L%) )
( tobi_endtone                    -70   		(L-L%) )
( n.tobi_endtone                  -8.9334		(L-L%) )
( nn.tobi_endtone                 -8.4034		(L-L%) )
( pp.tobi_endtone                 1.2099		(L-H%) )
( p.tobi_endtone                  1.1220		(L-H%) )
( tobi_endtone                    -10.0000		(L-H%) )
( n.tobi_endtone                  -5.9467		(L-H%) )
( nn.tobi_endtone                 -6.9072		(L-H%) )
( pp.tobi_endtone                 0.0000		(H-L%) )
( p.tobi_endtone                  0.0000		(H-L%) )
( tobi_endtone                    2.0395		(H-L%) )
( n.tobi_endtone                  -12.3940		(H-L%) )
( nn.tobi_endtone                 -24.2593		(H-L%) )
( pp.tobi_endtone                 0.0000		(H-H%) )
( p.tobi_endtone                  0.0000		(H-H%) )
( tobi_endtone                    0.0000		(H-H%) )
( n.tobi_endtone                  0.0000		(H-H%) )
( nn.tobi_endtone                 16.1076		(H-H%) )
( pp.tobi_endtone                 -1.8913		(L-) )
( p.tobi_endtone                  -15.5650		(L-) )
( tobi_endtone                    -18.3620		(L-) )
( n.tobi_endtone                  -9.8322		(L-) )
( nn.tobi_endtone                 -1.8182		(L-) )
( pp.tobi_endtone                 -13.4429		(H-) )
( p.tobi_endtone                  0.0000		(H-) )
( tobi_endtone                    1.9053		(H-) )
( n.tobi_endtone                  -3.4315		(H-) )
( nn.tobi_endtone                 0.0000		(H-) )
( p.tobi_accent                   0.0000		(L+H*L-H%) )
( tobi_accent                     10.0000		(L+H*L-H%) )
( n.tobi_accent                   0.0000		(L+H*L-H%) )
( pp.syl_break                    0.3501			 )
( p.syl_break                     -0.8121			 )
( syl_break                       0.3209			 )
( n.syl_break                     0.7486			 )
( nn.syl_break                    0.8182			 )
( pp.stress                       -0.9778			 )
( p.stress                        -0.3096			 )
( stress                          2.7752			 )
( n.stress                        0.9976			 )
( nn.stress                       2.7343			 )
( syl_in                          -1.9845			 )
( syl_out                         0.7142			 )
( ssyl_in                         1.0376			 )
( ssyl_out                        0.3062			 )
( asyl_in                         0.0000			 )
( asyl_out                        0.4953			 )
( last_accent                     0.0000			 )
( next_accent                     0.1084			 )
( sub_phrases                     0.0000			 )
( lisp_l_spread                   -60.0000			 )
))

(set! apml_f2b_f0_lr_mid
'(
( Intercept                       160.2474			 )
( pp.lisp_apml_tgtype             0.0000		(1) )
( p.lisp_apml_tgtype              4.5869		(1) )
( lisp_apml_tgtype                25.6530		(1) )
( n.lisp_apml_tgtype              14.8255		(1) )
( nn.lisp_apml_tgtype             12.9605		(1) )
( pp.lisp_apml_tgtype             0.0000		(2) )
( p.lisp_apml_tgtype              3.4867		(2) )
( lisp_apml_tgtype                13.5566		(2) )
( n.lisp_apml_tgtype              1.3359		(2) )
( nn.lisp_apml_tgtype             0.0000		(2) )
( pp.lisp_apml_tgtype             3.2989		(3) )
( p.lisp_apml_tgtype              2.5723		(3) )
( lisp_apml_tgtype                5.5798		(3) )
( n.lisp_apml_tgtype              0.0000		(3) )
( nn.lisp_apml_tgtype             0.0000		(3) )
( pp.lisp_apml_iecount            0.0000			 )
( p.lisp_apml_iecount             -0.7231			 )
( lisp_apml_iecount               0.0000			 )
( n.lisp_apml_iecount             -2.6922			 )
( nn.lisp_apml_iecount            0.0000			 )
( pp.tobi_accent                  0.0000		(H*) )
( p.tobi_accent                   6.0496		(H*) )
( tobi_accent                     20.9174		(H*) )
( n.tobi_accent                   0.0000		(H*) )
( nn.tobi_accent                  -6.4804		(H*) )
( pp.tobi_accent                  0.0000		(L*) )
( p.tobi_accent                   -5.0000		(L*) )
( tobi_accent                     -45.0000		(L*) )
( n.tobi_accent                   -6.1079		(L*) )
( nn.tobi_accent                  -3.4815		(L*) )
( pp.tobi_accent                  0.0000		(L*+H) )
( p.tobi_accent                   30.0000		(L*+H) )
( tobi_accent                     -30.0000		(L*+H) )
( n.tobi_accent                   -2.7200		(L*+H) )
( nn.tobi_accent                  0.0000		(L*+H) )
( pp.tobi_accent                  0.0000		(L+H*) )
( p.tobi_accent                   6.0500		(L+H*) )
( tobi_accent                     20.9200		(L+H*) )
( n.tobi_accent                   0.0000		(L+H*) )
( nn.tobi_accent                  -6.4800		(L+H*) )
( pp.tobi_endtone                 3.6235		(L-L%) )
( p.tobi_endtone                  0.2801		(L-L%) )
( tobi_endtone                    -80   		(L-L%) )
( n.tobi_endtone                  -6.9938		(L-L%) )
( nn.tobi_endtone                 -9.1546		(L-L%) )
( pp.tobi_endtone                 1.6616		(L-H%) )
( p.tobi_endtone                  2.2441		(L-H%) )
( tobi_endtone                    0.0000		(L-H%) )
( n.tobi_endtone                  0.0000		(L-H%) )
( nn.tobi_endtone                 -6.6132		(L-H%) )
( pp.tobi_endtone                 0.0000		(H-L%) )
( p.tobi_endtone                  0.0000		(H-L%) )
( tobi_endtone                    0.0000		(H-L%) )
( n.tobi_endtone                  -5.4416		(H-L%) )
( nn.tobi_endtone                 -19.1570		(H-L%) )
( pp.tobi_endtone                 0.0000		(H-H%) )
( p.tobi_endtone                  0.0000		(H-H%) )
( tobi_endtone                    0.0000		(H-H%) )
( n.tobi_endtone                  0.0000		(H-H%) )
( nn.tobi_endtone                 32.2151		(H-H%) )
( pp.tobi_endtone                 -3.7825		(L-) )
( p.tobi_endtone                  -15.9598		(L-) )
( tobi_endtone                    -36.7241		(L-) )
( n.tobi_endtone                  -5.1082		(L-) )
( nn.tobi_endtone                 -3.6363		(L-) )
( pp.tobi_endtone                 -13.3813		(H-) )
( p.tobi_endtone                  0.0000		(H-) )
( tobi_endtone                    -2.5270		(H-) )
( n.tobi_endtone                  0.0000		(H-) )
( nn.tobi_endtone                 0.0000		(H-) )
( p.tobi_accent                   0.0000		(L+H*L-H%) )
( tobi_accent                     40.0000		(L+H*L-H%) )
( n.tobi_accent                   0.0000		(L+H*L-H%) )
( pp.syl_break                    0.7003			 )
( p.syl_break                     -1.6241			 )
( syl_break                       0.0000			 )
( n.syl_break                     0.1439			 )
( nn.syl_break                    0.5640			 )
( pp.stress                       -1.9556			 )
( p.stress                        0.0000			 )
( stress                          3.1383			 )
( n.stress                        1.9952			 )
( nn.stress                       2.9208			 )
( syl_in                          -2.5317			 )
( syl_out                         1.0103			 )
( ssyl_in                         2.0751			 )
( ssyl_out                        0.0000			 )
( asyl_in                         0.0000			 )
( asyl_out                        0.0000			 )
( last_accent                     0.0000			 )
( next_accent                     0.5869			 )
( sub_phrases                     0.0000			 )
( lisp_l_spread                   -60.0000			 )
))

(set! apml_f2b_f0_lr_right
'(
( Intercept                       162.6687			 )
( pp.lisp_apml_tgtype             -4.0459		(1) )
( p.lisp_apml_tgtype              3.0601		(1) )
( lisp_apml_tgtype                27.8166		(1) )
( n.lisp_apml_tgtype              7.4127		(1) )
( nn.lisp_apml_tgtype             11.3458		(1) )
( pp.lisp_apml_tgtype             -3.8091		(2) )
( p.lisp_apml_tgtype              1.7434		(2) )
( lisp_apml_tgtype                17.1672		(2) )
( n.lisp_apml_tgtype              0.6679		(2) )
( nn.lisp_apml_tgtype             0.0000		(2) )
( pp.lisp_apml_tgtype             1.6494		(3) )
( p.lisp_apml_tgtype              1.2861		(3) )
( lisp_apml_tgtype                9.5674		(3) )
( n.lisp_apml_tgtype              -3.1085		(3) )
( nn.lisp_apml_tgtype             0.0000		(3) )
( pp.lisp_apml_iecount            0.0000			 )
( p.lisp_apml_iecount             -0.7829			 )
( lisp_apml_iecount               -0.5447			 )
( n.lisp_apml_iecount             -1.3461			 )
( nn.lisp_apml_iecount            -0.7178			 )
( pp.tobi_accent                  0.7904		(H*) )
( p.tobi_accent                   3.0248		(H*) )
( tobi_accent                     14.1116		(H*) )
( n.tobi_accent                   0.0000		(H*) )
( nn.tobi_accent                  -3.2402		(H*) )
( pp.tobi_accent                  0.0000		(L*) )
( p.tobi_accent                   -2.5000		(L*) )
( tobi_accent                     -32.5000		(L*) )
( n.tobi_accent                   -3.0539		(L*) )
( nn.tobi_accent                  -1.7408		(L*) )
( pp.tobi_accent                  0.0000		(L*+H) )
( p.tobi_accent                   17.5000		(L*+H) )
( tobi_accent                     -9.0000		(L*+H) )
( n.tobi_accent                   -2.8025		(L*+H) )
( nn.tobi_accent                  -0.5455		(L*+H) )
( pp.tobi_accent                  0.7900		(L+H*) )
( p.tobi_accent                   3.0250		(L+H*) )
( tobi_accent                     14.1150		(L+H*) )
( n.tobi_accent                   0.0000		(L+H*) )
( nn.tobi_accent                  -3.2400		(L+H*) )
( pp.tobi_endtone                 5.7534		(L-L%) )
( p.tobi_endtone                  0.1401		(L-L%) )
( tobi_endtone                    -65   		(L-L%) )
( n.tobi_endtone                  -11.1795		(L-L%) )
( nn.tobi_endtone                 -7.8158		(L-L%) )
( pp.tobi_endtone                 4.4276		(L-H%) )
( p.tobi_endtone                  1.1220		(L-H%) )
( tobi_endtone                    20.0000		(L-H%) )
( n.tobi_endtone                  -6.8995		(L-H%) )
( nn.tobi_endtone                 -6.1219		(L-H%) )
( pp.tobi_endtone                 2.4327		(H-L%) )
( p.tobi_endtone                  0.0000		(H-L%) )
( tobi_endtone                    -7.5781		(H-L%) )
( n.tobi_endtone                  -2.7208		(H-L%) )
( nn.tobi_endtone                 -14.4838		(H-L%) )
( pp.tobi_endtone                 0.0000		(H-H%) )
( p.tobi_endtone                  0.0000		(H-H%) )
( tobi_endtone                    0.0000		(H-H%) )
( n.tobi_endtone                  0.0000		(H-H%) )
( nn.tobi_endtone                 16.1076		(H-H%) )
( pp.tobi_endtone                 -1.8913		(L-) )
( p.tobi_endtone                  -15.5651		(L-) )
( tobi_endtone                    -40.2021		(L-) )
( n.tobi_endtone                  -2.5541		(L-) )
( nn.tobi_endtone                 -2.2224		(L-) )
( pp.tobi_endtone                 -6.6906		(H-) )
( p.tobi_endtone                  -3.5483		(H-) )
( tobi_endtone                    -1.2635		(H-) )
( n.tobi_endtone                  0.0000		(H-) )
( nn.tobi_endtone                 0.0000		(H-) )
( p.tobi_accent                   0.0000		(L+H*L-H%) )
( tobi_accent                     -40.0000		(L+H*L-H%) )
( n.tobi_accent                   0.0000		(L+H*L-H%) )
( pp.syl_break                    0.3501			 )
( p.syl_break                     -1.0003			 )
( syl_break                       -1.5536			 )
( n.syl_break                     0.0720			 )
( nn.syl_break                    0.5989			 )
( pp.stress                       -0.9778			 )
( p.stress                        -0.8046			 )
( stress                          1.2124			 )
( n.stress                        3.9715			 )
( nn.stress                       2.3914			 )
( syl_in                          -2.3468			 )
( syl_out                         0.9792			 )
( ssyl_in                         2.0463			 )
( ssyl_out                        0.0000			 )
( asyl_in                         -0.1460			 )
( asyl_out                        0.0000			 )
( last_accent                     -1.0992			 )
( next_accent                     0.2935			 )
( sub_phrases                     0.0000			 )
( lisp_l_spread                   -60.0000			 )
))

(set! apml_f2b_f0_lr_end
'(
( Intercept                       165.0901			 )
( pp.lisp_apml_tgtype             -8.0918		(1) )
( p.lisp_apml_tgtype              1.5332		(1) )
( lisp_apml_tgtype                29.9802		(1) )
( n.lisp_apml_tgtype              0.0000		(1) )
( nn.lisp_apml_tgtype             9.7312		(1) )
( pp.lisp_apml_tgtype             -7.6181		(2) )
( p.lisp_apml_tgtype              0.0000		(2) )
( lisp_apml_tgtype                20.7778		(2) )
( n.lisp_apml_tgtype              0.0000		(2) )
( nn.lisp_apml_tgtype             0.0000		(2) )
( pp.lisp_apml_tgtype             0.0000		(3) )
( p.lisp_apml_tgtype              0.0000		(3) )
( lisp_apml_tgtype                13.5550		(3) )
( n.lisp_apml_tgtype              -6.2170		(3) )
( nn.lisp_apml_tgtype             0.0000		(3) )
( pp.lisp_apml_iecount            0.0000			 )
( p.lisp_apml_iecount             -0.8428			 )
( lisp_apml_iecount               -1.0894			 )
( n.lisp_apml_iecount             0.0000			 )
( nn.lisp_apml_iecount            -1.4355			 )
( pp.tobi_accent                  1.5807		(H*) )
( p.tobi_accent                   0.0000		(H*) )
( tobi_accent                     7.3057		(H*) )
( n.tobi_accent                   0.0000		(H*) )
( nn.tobi_accent                  0.0000		(H*) )
( pp.tobi_accent                  0.0000		(L*) )
( p.tobi_accent                   0.0000		(L*) )
( tobi_accent                     -20.0000		(L*) )
( n.tobi_accent                   0.0000		(L*) )
( nn.tobi_accent                  0.0000		(L*) )
( pp.tobi_accent                  0.0000		(L*+H) )
( p.tobi_accent                   5.0000		(L*+H) )
( tobi_accent                     12.0000		(L*+H) )
( n.tobi_accent                   -2.8850		(L*+H) )
( nn.tobi_accent                  -1.0910		(L*+H) )
( pp.tobi_accent                  1.5800		(L+H*) )
( p.tobi_accent                   0.0000		(L+H*) )
( tobi_accent                     7.3100		(L+H*) )
( n.tobi_accent                   0.0000		(L+H*) )
( nn.tobi_accent                  0.0000		(L+H*) )
( pp.tobi_endtone                 7.8833		(L-L%) )
( p.tobi_endtone                  0.0000		(L-L%) )
( tobi_endtone                    -80    		(L-L%) )
( n.tobi_endtone                  -35   		(L-L%) )
( nn.tobi_endtone                 -6.4769		(L-L%) )
( pp.tobi_endtone                 7.1936		(L-H%) )
( p.tobi_endtone                  0.0000		(L-H%) )
( tobi_endtone                    40.0000		(L-H%) )
( n.tobi_endtone                  -13.7990		(L-H%) )
( nn.tobi_endtone                 -5.6305		(L-H%) )
( pp.tobi_endtone                 4.8654		(H-L%) )
( p.tobi_endtone                  0.0000		(H-L%) )
( tobi_endtone                    -15.1561		(H-L%) )
( n.tobi_endtone                  0.0000		(H-L%) )
( nn.tobi_endtone                 -9.8107		(H-L%) )
( pp.tobi_endtone                 0.0000		(H-H%) )
( p.tobi_endtone                  0.0000		(H-H%) )
( tobi_endtone                    0.0000		(H-H%) )
( n.tobi_endtone                  0.0000		(H-H%) )
( nn.tobi_endtone                 0.0000		(H-H%) )
( pp.tobi_endtone                 0.0000		(L-) )
( p.tobi_endtone                  -15.1705		(L-) )
( tobi_endtone                    -43.6801		(L-) )
( n.tobi_endtone                  0.0000		(L-) )
( nn.tobi_endtone                 -0.8085		(L-) )
( pp.tobi_endtone                 0.0000		(H-) )
( p.tobi_endtone                  -7.0967		(H-) )
( tobi_endtone                    0.0000		(H-) )
( n.tobi_endtone                  0.0000		(H-) )
( nn.tobi_endtone                 0.0000		(H-) )
( p.tobi_accent                   0.0000		(L+H*L-H%) )
( tobi_accent                     60.0000		(L+H*L-H%) )
( n.tobi_accent                   -60.0000		(L+H*L-H%) )
( pp.syl_break                    0.0000			 )
( p.syl_break                     -0.3765			 )
( syl_break                       -3.1072			 )
( n.syl_break                     0.0000			 )
( nn.syl_break                    0.6338			 )
( pp.stress                       0.0000			 )
( p.stress                        -1.6093			 )
( stress                          -0.7136			 )
( n.stress                        5.9479			 )
( nn.stress                       1.8619			 )
( syl_in                          -2.1619			 )
( syl_out                         0.9481			 )
( ssyl_in                         2.0175			 )
( ssyl_out                        0.0000			 )
( asyl_in                         -0.2919			 )
( asyl_out                        0.0000			 )
( last_accent                     -2.1984			 )
( next_accent                     0.0000			 )
( sub_phrases                     0.0000			 )
( lisp_l_spread                   -60.0000			 )
))

