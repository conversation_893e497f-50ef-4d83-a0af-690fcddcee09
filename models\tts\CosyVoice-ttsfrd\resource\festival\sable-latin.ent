<!-- 

NOTE: THIS IS A WORKING DOCUMENT. NOTHING IN HERE SHOULD BE
TAKEN AS FINAL.

This is an initial proposal for a Latin 1 and assorted
other characters entity set for SABLE. It is based on the 
equivalent set for HTML 3.

-->
<!-- Portions of this text are copyright ISO:

     (C) International Organization for Standardization 1986
     Permission to copy in any form is granted for use with
     conforming SGML systems and applications as defined in
     ISO 8879, provided this notice is included in all copies.
-->
<!--	Character entity set. Typical invocation:
	<!ENTITY % HTMLlat1 PUBLIC
	   "-//W3O//ENTITIES W3 Latin 1 for HTML//EN">
	%HTMLlat1;
-->
<!--	Modified for use in HTML
	$Id: sable-latin.ent,v 1.2 2001/04/04 13:12:35 awb Exp $ =

-->
<!--    Modified to add characters in the range &161; to &191; in
	the ISO Latin-1 character set, which could only be referred =

	to by numeric references. Entity names based on relevant entities in
        ISO 8879-1986//ENTITIES Numeric and Special Graphic//EN"
        Also added the standard lt gt amp entities from HTML 2.0
        <PERSON>, 13 March 1995
        =

        This covers all of Latin 1, but we are still unable to display a =

        Trade Mark (TM)
-->      

<!ENTITY AElig  "&#198;" > <!-- capital AE diphthong (ligature) -->
<!ENTITY Aacute "&#193;" > <!-- capital A, acute accent -->
<!ENTITY Acirc  "&#194;" > <!-- capital A, circumflex accent -->
<!ENTITY Agrave "&#192;" > <!-- capital A, grave accent -->
<!ENTITY Aring  "&#197;" > <!-- capital A, ring -->
<!ENTITY Atilde "&#195;" > <!-- capital A, tilde -->
<!ENTITY Auml   "&#196;" > <!-- capital A, dieresis or umlaut mark -->
<!ENTITY Ccedil "&#199;" > <!-- capital C, cedilla -->
<!ENTITY ETH    "&#208;" > <!-- capital Eth, Icelandic -->
<!ENTITY Eacute "&#201;" > <!-- capital E, acute accent -->
<!ENTITY Ecirc  "&#202;" > <!-- capital E, circumflex accent -->
<!ENTITY Egrave "&#200;" > <!-- capital E, grave accent -->
<!ENTITY Euml   "&#203;" > <!-- capital E, dieresis or umlaut mark -->
<!ENTITY Iacute "&#205;" > <!-- capital I, acute accent -->
<!ENTITY Icirc  "&#206;" > <!-- capital I, circumflex accent -->
<!ENTITY Igrave "&#204;" > <!-- capital I, grave accent -->
<!ENTITY Iuml   "&#207;" > <!-- capital I, dieresis or umlaut mark -->
<!ENTITY Ntilde "&#209;" > <!-- capital N, tilde -->
<!ENTITY Oacute "&#211;" > <!-- capital O, acute accent -->
<!ENTITY Ocirc  "&#212;" > <!-- capital O, circumflex accent -->
<!ENTITY Ograve "&#210;" > <!-- capital O, grave accent -->
<!ENTITY Oslash "&#216;" > <!-- capital O, slash -->
<!ENTITY Otilde "&#213;" > <!-- capital O, tilde -->
<!ENTITY Ouml   "&#214;" > <!-- capital O, dieresis or umlaut mark -->
<!ENTITY THORN  "&#222;" > <!-- capital THORN, Icelandic -->
<!ENTITY Uacute "&#218;" > <!-- capital U, acute accent -->
<!ENTITY Ucirc  "&#219;" > <!-- capital U, circumflex accent -->
<!ENTITY Ugrave "&#217;" > <!-- capital U, grave accent -->
<!ENTITY Uuml   "&#220;" > <!-- capital U, dieresis or umlaut mark -->
<!ENTITY Yacute "&#221;" > <!-- capital Y, acute accent -->
<!ENTITY aacute "&#225;" > <!-- small a, acute accent -->
<!ENTITY acirc  "&#226;" > <!-- small a, circumflex accent -->
<!ENTITY aelig  "&#230;" > <!-- small ae diphthong (ligature) -->
<!ENTITY agrave "&#224;" > <!-- small a, grave accent -->
<!ENTITY aring  "&#229;" > <!-- small a, ring -->
<!ENTITY atilde "&#227;" > <!-- small a, tilde -->
<!ENTITY auml   "&#228;" > <!-- small a, dieresis or umlaut mark -->
<!ENTITY ccedil "&#231;" > <!-- small c, cedilla -->
<!ENTITY eacute "&#233;" > <!-- small e, acute accent -->
<!ENTITY ecirc  "&#234;" > <!-- small e, circumflex accent -->
<!ENTITY egrave "&#232;" > <!-- small e, grave accent -->
<!ENTITY eth    "&#240;" > <!-- small eth, Icelandic -->
<!ENTITY euml   "&#235;" > <!-- small e, dieresis or umlaut mark -->
<!ENTITY iacute "&#237;" > <!-- small i, acute accent -->
<!ENTITY icirc  "&#238;" > <!-- small i, circumflex accent -->
<!ENTITY igrave "&#236;" > <!-- small i, grave accent -->
<!ENTITY iuml   "&#239;" > <!-- small i, dieresis or umlaut mark -->
<!ENTITY ntilde "&#241;" > <!-- small n, tilde -->
<!ENTITY oacute "&#243;" > <!-- small o, acute accent -->
<!ENTITY ocirc  "&#244;" > <!-- small o, circumflex accent -->
<!ENTITY ograve "&#242;" > <!-- small o, grave accent -->
<!ENTITY oslash "&#248;" > <!-- small o, slash -->
<!ENTITY otilde "&#245;" > <!-- small o, tilde -->
<!ENTITY ouml   "&#246;" > <!-- small o, dieresis or umlaut mark -->
<!ENTITY szlig  "&#223;" > <!-- small sharp s, German (sz ligature) -->
<!ENTITY thorn  "&#254;" > <!-- small thorn, Icelandic -->
<!ENTITY uacute "&#250;" > <!-- small u, acute accent -->
<!ENTITY ucirc  "&#251;" > <!-- small u, circumflex accent -->
<!ENTITY ugrave "&#249;" > <!-- small u, grave accent -->
<!ENTITY uuml   "&#252;" > <!-- small u, dieresis or umlaut mark -->
<!ENTITY yacute "&#253;" > <!-- small y, acute accent -->
<!ENTITY yuml   "&#255;" > <!-- small y, dieresis or umlaut mark -->
<!-- =

     Ones that aren't accented characters, and so not in ISO Added Latin =
1.

     umlaut. macron, acute, cedilla
     were not in ISO Numeric and Special Graphic
     either; I took their names from the numeric entity list in
     http://www.hpl.hp.co.uk/people/dsr/html/latin1.html =

     Chris Lilley, 13 March 1995  =

-->    

<!ENTITY iexcl   "&#161;" > <!-- inverted exclamation mark &161; -->
<!ENTITY cent    "&#162;" > <!-- cent sign &162; -->
<!ENTITY pound   "&#163;" > <!-- pound sterling sign &163; -->
<!ENTITY curren  "&#164;" > <!-- general currency sign &164; -->
<!ENTITY yen     "&#165;" > <!-- yen sign &165; -->
<!ENTITY brvbar  "&#166;" > <!-- broken (vertical) bar &166; -->
<!ENTITY sect    "&#167;" > <!-- section sign &167; -->
<!ENTITY umlaut  "&#168;" > <!-- umlaut (dieresis) &168; -->
<!ENTITY copy    "&#169;" > <!-- copyright sign &169; -->
<!ENTITY ordf    "&#170;" > <!-- ordinal indicator, feminine &170; -->
<!ENTITY laquo   "&#171;" > <!-- angle quotation mark, left &171; -->
<!ENTITY not     "&#172;" > <!-- not sign &172; -->
<!ENTITY shy     "&#173;" > <!-- soft hyphen &173;-->
<!ENTITY reg     "&#174;" > <!-- registered trademark &174; -->
<!ENTITY macron  "&#175;" > <!-- macron &175; -->
<!ENTITY deg     "&#176;" > <!-- degree sign &176; -->
<!ENTITY plusmn  "&#177;" > <!-- plus-or-minus sign &177; -->
<!ENTITY sup2    "&#178;" > <!-- superscript two &178; -->
<!ENTITY sup3    "&#179;" > <!-- superscript three &179; -->
<!ENTITY acute   "&#180;" > <!-- acute accent &180; -->
<!ENTITY micro   "&#181;" > <!-- micro sign &181; -->
<!ENTITY para    "&#182;" > <!-- pilcrow (paragraph sign) &182; -->
<!ENTITY middot  "&#183;" > <!-- middle dot (centred decimal point) &183; -->
<!ENTITY cedilla "&#184;" > <!-- cedilla accent &184; -->
<!ENTITY sup1    "&#185;" > <!-- superscript one -->
<!ENTITY ordm    "&#186;" > <!-- ordinal indicator, masculine -->
<!ENTITY raquo   "&#187;" > <!-- angle quotation mark, right -->
<!ENTITY frac14  "&#188;" > <!-- fraction one-quarter -->
<!ENTITY frac12  "&#189;" > <!-- fraction one-half -->
<!ENTITY frac34  "&#190;" > <!-- fraction three-quarters -->
<!ENTITY iquest  "&#191;" > <!-- inverted question mark -->
<!-- the odd ones tucked in amongst the accented letters -->
<!ENTITY times   "&#215;" > <!-- multiply sign -->
<!ENTITY divide  "&#247;" > <!-- divide sign -->

<!ENTITY amp "&#38;" > <!-- ampersand          -->
<!ENTITY gt "&#62;"  > <!-- greater than       -->
<!ENTITY lt "&#60;"  > <!-- less than          -->
<!ENTITY quot "&#34;" > <!-- double quote       -->

<!--
        Should the dollar sign and such like also be given entity names?
        There are suitable ones around. For example, people using =

        internationalised keyboards in various countries might not be =

        readily able to access some of these characters.
        =

        On such grounds, I suggest entity names for these:
        ! " # $ %  ' ( ) * + , - . / : ;  =3D  ? [ \ ] ^ _ ` { | } ~
        Lastly, what about &tab; for those whose editors convert all
        tabs to spaces?

-->
