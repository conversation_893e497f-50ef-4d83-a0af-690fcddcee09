import os
import numpy as np
import torch
import librosa
try:
    import onnxruntime
except ImportError:
    onnxruntime = None

def load_model_info(info_path):
    info = {}
    if info_path and os.path.exists(info_path):
        with open(info_path, "r", encoding="utf-8") as f:
            content = f.read()
            
        # 解析模型信息文件
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if not line or line.startswith('【') or line.startswith('-'):
                continue
                
            if '：' in line:
                k, v = line.split('：', 1)
                info[k.strip()] = v.strip()
            elif ':' in line:
                k, v = line.split(':', 1)
                info[k.strip()] = v.strip()
                
        # 提取关键信息
        for line in lines:
            if '输入shape：' in line:
                shape_str = line.split('：')[1].strip()
                info['输入shape：'] = shape_str
            elif '推荐阈值：' in line:
                threshold_str = line.split('：')[1].strip()
                info['推荐阈值：'] = threshold_str
            elif 'MFCC维数：' in line:
                mfcc_dim = line.split('：')[1].strip()
                info['MFCC维数'] = mfcc_dim
            elif '窗长（win_len_ms）：' in line:
                win_len = line.split('：')[1].strip()
                info['窗长（win_len_ms）'] = win_len
            elif '帧移（hop_len_ms）：' in line:
                hop_len = line.split('：')[1].strip()
                info['帧移（hop_len_ms）'] = hop_len
            elif '采样率：' in line:
                sr = line.split('：')[1].strip()
                info['采样率'] = sr
                
    return info

def extract_features(wav, info):
    # 解析采样率，支持"16kHz"格式
    sr_str = info.get("采样率", "16000")
    if "kHz" in sr_str:
        sr = int(sr_str.replace("kHz", "")) * 1000
    else:
        sr = int(sr_str)
    
    # 解析MFCC维数
    n_mfcc_str = info.get("MFCC维数", "40")
    n_mfcc = int(n_mfcc_str)
    
    # 解析窗长和帧移
    win_len_str = info.get("窗长（win_len_ms）", "25")
    win_len = int(win_len_str.replace("ms", "")) / 1000
    
    hop_len_str = info.get("帧移（hop_len_ms）", "10")
    hop_len = int(hop_len_str.replace("ms", "")) / 1000
    
    # 确保音频数据格式正确
    if wav.dtype != np.float32:
        wav = wav.astype(np.float32)
    
    # 音频预处理：归一化
    if np.max(np.abs(wav)) > 0:
        wav = wav / np.max(np.abs(wav))
    
    # 提取MFCC特征
    mfcc = librosa.feature.mfcc(y=wav, sr=sr, n_mfcc=n_mfcc,
                                n_fft=int(sr*win_len), hop_length=int(sr*hop_len))
    return mfcc.T  # shape: (time, n_mfcc)

class WakeWordDetector:
    def __init__(self, model_type, model_path, model_info_path=None, device="cpu"):
        self.model_type = model_type.lower()
        self.model_path = model_path
        self.device = device
        self.model_info = load_model_info(model_info_path)
        self.model = self._load_model()

    def _load_model(self):
        if self.model_type == "pt":
            model = torch.jit.load(self.model_path, map_location=self.device)
            model.eval()
            return model
        elif self.model_type == "onnx":
            if onnxruntime is None:
                raise ImportError("onnxruntime 未安装")
            return onnxruntime.InferenceSession(self.model_path)
        else:
            raise ValueError("不支持的模型类型: " + self.model_type)

    def detect(self, wav):
        # wav: 1D numpy array, float32
        # 确保音频数据格式正确
        if wav.dtype != np.float32:
            wav = wav.astype(np.float32)
        
        # 音频预处理：归一化
        if np.max(np.abs(wav)) > 0:
            wav = wav / np.max(np.abs(wav))
        
        # 提取MFCC特征
        features = extract_features(wav, self.model_info)
        
        # 解析输入形状，处理带注释的格式
        input_shape = self.model_info.get("输入shape：", "1 x 1 x 100 x 40")
        # 移除注释部分，只保留数字
        shape_str = input_shape.split('（')[0].strip()  # 移除"（batch, channel, time, feature）"
        # 解析形如 "1 x 1 x 100 x 40"
        shape_parts = shape_str.split('x')
        shape = tuple(int(x.strip()) for x in shape_parts)
        
        # 调整特征长度以匹配模型期望的输入
        expected_time_steps = shape[2]  # 通常是100
        actual_time_steps = features.shape[0]
        
        if actual_time_steps > expected_time_steps:
            # 如果特征太长，取中间部分
            start_idx = (actual_time_steps - expected_time_steps) // 2
            features = features[start_idx:start_idx + expected_time_steps]
        elif actual_time_steps < expected_time_steps:
            # 如果特征太短，用零填充
            padding = np.zeros((expected_time_steps - actual_time_steps, features.shape[1]), dtype=np.float32)
            features = np.vstack([features, padding])
        
        # 补零/截断到shape
        feat = np.zeros(shape, dtype=np.float32)
        t = min(features.shape[0], shape[2])
        f = min(features.shape[1], shape[3])
        feat[0, 0, :t, :f] = features[:t, :f]
        
        if self.model_type == "pt":
            with torch.no_grad():
                x = torch.tensor(feat, dtype=torch.float32).to(self.device)
                out = self.model(x)
                score = float(out.squeeze().item())
        elif self.model_type == "onnx":
            input_name = self.model.get_inputs()[0].name
            out = self.model.run(None, {input_name: feat})
            score = float(out[0].squeeze())
        else:
            raise ValueError("不支持的模型类型")
        
        threshold = float(self.model_info.get("推荐阈值：", 0.85))
        is_wakeup = score > threshold
        return {"is_wakeup": is_wakeup, "score": score}

if __name__ == "__main__":
    import soundfile as sf
    import yaml
    # 加载配置
    with open("config.yaml", "r", encoding="utf-8") as f:
        cfg = yaml.safe_load(f)
    detector = WakeWordDetector(
        model_type=cfg.get("kws_model_type", "onnx"),
        model_path=cfg.get("kws_model_path", "D:/Project/aibi/models/cnn/hey_aibi.onnx"),
        model_info_path=cfg.get("kws_model_info", "D:/Project/aibi/models/cnn/model_info.txt"),
        device="cpu"
    )
    # 读取测试音频
    wav, _ = sf.read("test.wav")
    result = detector.detect(wav)
    print(result)
