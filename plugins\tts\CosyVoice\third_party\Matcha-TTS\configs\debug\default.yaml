# @package _global_

# default debugging setup, runs 1 full epoch
# other debugging configs can inherit from this one

# overwrite task name so debugging logs are stored in separate folder
task_name: "debug"

# disable callbacks and loggers during debugging
# callbacks: null
# logger: null

extras:
  ignore_warnings: False
  enforce_tags: False

# sets level of all command line loggers to 'DEBUG'
# https://hydra.cc/docs/tutorials/basic/running_your_app/logging/
hydra:
  job_logging:
    root:
      level: DEBUG

  # use this to also set hydra loggers to 'DEBUG'
  # verbose: True

trainer:
  max_epochs: 1
  accelerator: cpu # debuggers don't like gpus
  devices: 1 # debuggers don't like multiprocessing
  detect_anomaly: true # raise exception if NaN or +/-inf is detected in any tensor

data:
  num_workers: 0 # debuggers don't like multiprocessing
  pin_memory: False # disable gpu memory pin
