#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - ASR模块
集成FunASR，使用SenseVoiceSmall模型进行语音识别
"""

import os
import sys
import time
import logging
import numpy as np
from typing import Dict, Any, Optional, List, Union
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    import funasr
    from funasr import AutoModel
    FUNASR_AVAILABLE = True
    logging.info(f"FunASR已安装，版本: {funasr.__version__}")
except ImportError:
    FUNASR_AVAILABLE = False
    logging.warning("FunASR未安装，ASR功能将不可用")

class ASRProcessor:
    """语音识别处理器 - 基于FunASR和SenseVoiceSmall模型"""
    
    def __init__(self, config):
        """初始化ASR处理器"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # ASR模型配置
        self.model_path = config.get("models.asr_model_path", "./models/asr")
        self.model_name = config.get("models.asr_model_name", "SenseVoice/SenseVoiceSmall")
        self.model_revision = config.get("models.asr_model_revision", "v1.0.0")
        self.asr_language = config.get("models.asr_language", "zh")  # 新增语言配置，默认为中文
        self.beam_size = config.get("models.asr.beam_size", 5)
        self.max_length = config.get("models.asr.max_length", 512)
        self.temperature = config.get("models.asr.temperature", 0.0)
        self.model_type = config.get("models.asr.model_type", "funasr")
        self.enable_streaming = config.get("models.asr.enable_streaming", True)
        self.recognition_timeout = config.get("models.asr.recognition_timeout", 3.0)
        
        # 音频配置
        self.sample_rate = config.get("audio.input.sample_rate", 16000)
        self.chunk_size = config.get("audio.input.chunk_size", 1024)
        
        # 硬件配置
        self.device = config.get("hardware.device", "auto")
        self.num_threads = config.get("hardware.num_threads", 4)
        
        # 性能配置
        self.enable_cache = config.get("hardware.enable_cache", True)
        self.enable_fp16 = config.get("hardware.enable_fp16", False)
        
        # 初始化模型
        self.model = None
        self.is_initialized = False
        self._init_model()
        
        # 流式识别状态
        self.streaming_mode = False
        self.audio_buffer = []
        self.last_recognition_time = 0
        
        self.logger.info("ASR处理器初始化完成")
    
    def _init_model(self):
        """初始化FunASR模型"""
        try:
            if not FUNASR_AVAILABLE:
                self.logger.error("FunASR未安装，无法初始化ASR模型")
                return
            
            # 检查模型路径
            if self.model_path and not os.path.exists(self.model_path):
                self.logger.warning(f"ASR模型路径不存在: {self.model_path}")
                self.logger.info("将使用FunASR默认SenseVoiceSmall模型")
                self.model_path = None
            
            # 初始化模型
            self.logger.info("正在加载ASR模型...")
            
            # 使用本地模型路径
            if self.model_path and os.path.exists(self.model_path):
                model_name = self.model_path
                self.logger.info(f"使用本地模型: {model_name}")
            else:
                model_name = self.model_name
                self.logger.info(f"使用远程模型: {model_name}")
            
            # 模型配置
            model_config = {
                "model": model_name,
                "model_revision": self.model_revision,
                "device": self.device,
                "num_threads": self.num_threads,
                "beam_size": self.beam_size,
                "max_length": self.max_length,
                "temperature": self.temperature,
                "enable_cache": self.enable_cache,
                "enable_fp16": self.enable_fp16
            }
            
            # 创建模型实例
            self.model = AutoModel(**model_config)
            
            self.is_initialized = True
            self.logger.info(f"ASR模型加载成功: {model_name}")
            
        except Exception as e:
            self.logger.error(f"ASR模型初始化失败: {e}")
            self.is_initialized = False
    
    def recognize(self, audio_data: np.ndarray) -> Optional[Dict[str, Any]]:
        """
        识别音频数据
        
        Args:
            audio_data: 音频数据，numpy数组，float32格式
            
        Returns:
            识别结果字典，包含text、confidence、timestamp等信息
        """
        try:
            if not self.is_initialized or self.model is None:
                self.logger.warning("ASR模型未初始化，返回模拟结果")
                return self._fallback_recognize(audio_data)
            
            # 音频预处理
            processed_audio = self._preprocess_audio(audio_data)
            
            # 检查音频质量
            if not self._check_audio_quality(processed_audio):
                self.logger.warning(f"音频质量不合格，长度: {len(processed_audio)}")
                return {
                    "text": "",
                    "confidence": 0.0,
                    "processing_time": 0.0,
                    "timestamp": time.time(),
                    "model": "SenseVoiceSmall",
                    "status": "bad_audio"
                }
            
            # 执行识别
            start_time = time.time()
            self.logger.info(f"开始ASR识别，音频长度: {len(processed_audio)}")
            
            # FunASR需要文件路径或音频数据
            import contextlib
            import sys
            class DummyFile(object):
                def write(self, x): pass
                def flush(self): pass
            # 屏蔽tqdm进度条输出
            with contextlib.redirect_stdout(DummyFile()), contextlib.redirect_stderr(DummyFile()):
                if isinstance(processed_audio, np.ndarray):
                    import tempfile
                    import soundfile as sf
                    temp_file = None
                    try:
                        temp_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
                        sf.write(temp_file.name, processed_audio, self.sample_rate)
                        temp_file.close()
                        result = self.model.generate(input=temp_file.name, language=self.asr_language)
                    finally:
                        if temp_file and os.path.exists(temp_file.name):
                            try:
                                os.unlink(temp_file.name)
                            except:
                                pass
                else:
                    result = self.model.generate(input=processed_audio, language=self.asr_language)
            
            processing_time = time.time() - start_time
            self.logger.info(f"ASR识别完成，耗时: {processing_time:.2f}s，结果: {result}")
            
            if result and len(result) > 0:
                # 解析识别结果
                recognition_result = result[0]  # 取第一个结果
                
                # 提取文本和置信度
                text = recognition_result.get("text", "").strip()
                # 简化输出，仅保留纯文本，去除标签
                import re
                text = re.sub(r'<\|.*?\|>', '', text).strip()
                
                if text:
                    # 输出格式：[user] : 识别文本
                    text = f"[user] : {text}"
                    return {
                        "text": text
                    }
                else:
                    self.logger.warning("ASR识别无结果或空文本")
                    return None
            else:
                self.logger.warning("ASR识别无结果或空文本")
                return {
                    "text": ""
                }
                
        except Exception as e:
            self.logger.error(f"ASR识别错误: {e}")
            return {
                "text": "",
                "confidence": 0.0,
                "error": str(e),
                "status": "error"
            }
    
    def start_streaming(self):
        """开始流式识别"""
        try:
            if not self.is_initialized:
                self.logger.error("ASR模型未初始化，无法开始流式识别")
                return False
            
            self.streaming_mode = True
            self.audio_buffer = []
            self.last_recognition_time = time.time()
            
            self.logger.info("开始流式ASR识别")
            return True
            
        except Exception as e:
            self.logger.error(f"启动流式识别失败: {e}")
            return False
    
    def stop_streaming(self):
        """停止流式识别"""
        try:
            self.streaming_mode = False
            self.audio_buffer = []
            
            self.logger.info("停止流式ASR识别")
            
        except Exception as e:
            self.logger.error(f"停止流式识别失败: {e}")
    
    def process_streaming_audio(self, audio_chunk: np.ndarray) -> Optional[Dict[str, Any]]:
        """
        处理流式音频数据
        
        Args:
            audio_chunk: 音频数据块
            
        Returns:
            识别结果，如果超时或检测到语音结束则返回结果
        """
        try:
            if not self.streaming_mode:
                return None
            
            # 添加到缓冲区
            self.audio_buffer.extend(audio_chunk)
            
            current_time = time.time()
            
            # 检查是否超时
            if current_time - self.last_recognition_time > self.recognition_timeout:
                if len(self.audio_buffer) > 0:
                    # 执行识别
                    audio_data = np.array(self.audio_buffer, dtype=np.float32)
                    result = self.recognize(audio_data)
                    
                    # 清空缓冲区
                    self.audio_buffer = []
                    self.last_recognition_time = current_time
                    
                    return result
            
            return None
            
        except Exception as e:
            self.logger.error(f"流式音频处理错误: {e}")
            return None
    
    def _preprocess_audio(self, audio_data: np.ndarray) -> np.ndarray:
        """
        音频预处理
        
        Args:
            audio_data: 原始音频数据
            
        Returns:
            预处理后的音频数据
        """
        try:
            # 确保数据类型为float32
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # 音频归一化
            if np.max(np.abs(audio_data)) > 0:
                audio_data = audio_data / np.max(np.abs(audio_data))
            
            # 重采样（如果需要）
            if len(audio_data) > 0:
                # 确保音频长度合适
                min_length = int(self.sample_rate * 0.1)  # 最少0.1秒
                max_length = int(self.sample_rate * 30)    # 最多30秒
                
                if len(audio_data) < min_length:
                    # 填充静音
                    padding = np.zeros(min_length - len(audio_data), dtype=np.float32)
                    audio_data = np.concatenate([audio_data, padding])
                elif len(audio_data) > max_length:
                    # 截断
                    audio_data = audio_data[:max_length]
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"音频预处理错误: {e}")
            return audio_data
    
    def _check_audio_quality(self, audio_data: np.ndarray) -> bool:
        """
        检查音频质量
        
        Args:
            audio_data: 音频数据
            
        Returns:
            音频质量是否合格
        """
        try:
            if len(audio_data) == 0:
                return False
            energy = np.mean(np.abs(audio_data))
            noise_floor = np.percentile(np.abs(audio_data), 10)
            signal_level = np.percentile(np.abs(audio_data), 90)
            snr = 20 * np.log10(signal_level / noise_floor) if noise_floor > 0 else 0
            # 极限放宽，允许所有音频通过
            min_energy = 0.0
            min_snr = 0.0
            self.logger.info(f"音频能量: {energy:.4f}, SNR: {snr:.2f}dB, 长度: {len(audio_data)}")
            return True
        except Exception as e:
            self.logger.error(f"音频质量检查错误: {e}")
            return False
    
    def _fallback_recognize(self, audio_data: np.ndarray) -> Optional[Dict[str, Any]]:
        """
        备用识别方法（当FunASR不可用时）
        
        Args:
            audio_data: 音频数据
            
        Returns:
            模拟识别结果
        """
        try:
            # 基于音频能量的简单判断
            energy = np.mean(np.abs(audio_data))
            
            if energy > 0.1:  # 有声音
                # 模拟识别结果
                text = "你好，我是艾比"
                confidence = min(float(energy * 10), 0.95)  # 基于能量计算置信度
                
                return {
                    "text": f"[user] : {text}",
                    "confidence": float(confidence),
                    "energy": float(energy),
                    "model": "fallback",
                    "status": "fallback"
                }
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"备用识别错误: {e}")
            return None
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "model_path": self.model_path,
            "model_revision": self.model_revision,
            "model_type": self.model_type,
            "is_initialized": self.is_initialized,
            "funasr_available": FUNASR_AVAILABLE,
            "device": self.device,
            "sample_rate": self.sample_rate,
            "beam_size": self.beam_size,
            "max_length": self.max_length,
            "temperature": self.temperature,
            "enable_streaming": self.enable_streaming,
            "recognition_timeout": self.recognition_timeout
        }
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            status = "healthy" if self.is_initialized else "unhealthy"
            
            return {
                "status": status,
                "model_loaded": self.is_initialized,
                "funasr_available": FUNASR_AVAILABLE,
                "streaming_mode": self.streaming_mode,
                "buffer_size": len(self.audio_buffer)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

def asr_infer(audio_segment):
    """
    兼容性函数：简单的ASR推理接口
    
    Args:
        audio_segment: 音频数据
        
    Returns:
        识别文本
    """
    try:
        # 创建临时配置
        config = {
            "models.asr_model_path": "./models/asr",
            "models.asr.beam_size": 5,
            "models.asr.max_length": 512,
            "models.asr.temperature": 0.0,
            "audio.input.sample_rate": 16000,
            "audio.input.chunk_size": 1024,
            "hardware.device": "auto",
            "hardware.num_threads": 4,
            "hardware.enable_cache": True,
            "hardware.enable_fp16": False
        }
        
        # 创建ASR处理器
        asr_processor = ASRProcessor(config)
        
        # 转换音频数据
        if isinstance(audio_segment, bytes):
            audio_data = np.frombuffer(audio_segment, dtype=np.float32)
        else:
            audio_data = np.array(audio_segment, dtype=np.float32)
        
        # 执行识别
        result = asr_processor.recognize(audio_data)
        
        if result and result.get("text"):
            return result["text"]
        else:
            return "识别失败"
            
    except Exception as e:
        print(f"ASR推理错误: {e}")
        return "识别错误"

if __name__ == "__main__":
    # 测试ASR模块
    print("测试ASR模块...")
    
    # 创建测试配置
    test_config = {
        "models.asr_model_path": "./models/asr",
        "models.asr.beam_size": 5,
        "models.asr.max_length": 512,
        "models.asr.temperature": 0.0,
        "audio.input.sample_rate": 16000,
        "audio.input.chunk_size": 1024,
        "hardware.device": "auto",
        "hardware.num_threads": 4,
        "hardware.enable_cache": True,
        "hardware.enable_fp16": False
    }
    
    # 创建ASR处理器
    asr_processor = ASRProcessor(test_config)
    
    # 打印模型信息
    model_info = asr_processor.get_model_info()
    print(f"模型信息: {model_info}")
    
    # 健康检查
    health = asr_processor.health_check()
    print(f"健康状态: {health}")
    
    # 测试识别（使用模拟音频）
    test_audio = np.random.randn(16000).astype(np.float32) * 0.1
    result = asr_processor.recognize(test_audio)
    print(f"测试识别结果: {result}")
