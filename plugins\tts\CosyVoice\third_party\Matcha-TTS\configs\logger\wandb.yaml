# https://wandb.ai

wandb:
  _target_: lightning.pytorch.loggers.wandb.WandbLogger
  # name: "" # name of the run (normally generated by wandb)
  save_dir: "${paths.output_dir}"
  offline: False
  id: null # pass correct id to resume experiment!
  anonymous: null # enable anonymous logging
  project: "lightning-hydra-template"
  log_model: False # upload lightning ckpts
  prefix: "" # a string to put at the beginning of metric keys
  # entity: "" # set to name of your wandb team
  group: ""
  tags: []
  job_type: ""
