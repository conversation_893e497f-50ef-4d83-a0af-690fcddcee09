 # aibi语音交互系统 架构设计说明

---

## 1. 架构设计目标与原则

- 实现端到端本地语音交互，支持PC/嵌入式多平台，适配CPU/NPU/GPU等多种硬件。
- 以FunASR为主线，集成唤醒、VAD、ASR、TTS、插话中断、自动休眠等完整链路。
- 采用流式、异步、事件驱动架构，确保低延迟、高鲁棒性。
- 各模块解耦，接口标准化，便于升级与扩展。
- 仅语音交互，LLM能力通过Dify服务器API对接。

---

## 2. 总体架构图

详见《架构图.md》，核心流程如下：

```mermaid
flowchart TD
    A[音频采集]
    B[FunASR前端<br>（降噪/VAD）]
    C[FunASR唤醒词检测]
    S{系统状态<br>（待唤醒/已唤醒）}
    D[FunASR语音识别]
    E[LLM服务器<br>（Dify）]
    F[FunASR TTS]
    G[音频播放]
    H[事件/状态管理<br>（含中断/超时/退出）]
    I[推理后端适配]
    J[日志监控]
    K[配置管理]
    L[健康检查]
    X[中断前置条件判定<br>（仅已唤醒状态）]
    T[超时/结束指令检测]

    %% 主流程
    A --> B
    B --> C
    C -- 唤醒成功 --> S
    S -- 已唤醒 --> D
    S -- 待唤醒 --> C
    D --> E
    E --> F
    F --> G

    %% 插话/中断流程（仅已唤醒状态）
    B -- 检测到新语音/插话 --> X
    X -- 系统已唤醒且满足前置条件 --> H
    H -- 触发中断事件 --> F
    H -- 触发中断事件 --> D
    H -- 触发中断事件 --> E
    H -- 触发中断事件 --> G
    X -- 未唤醒或不满足条件 --> B

    %% 自动退出唤醒
    S -- 已唤醒 --> T
    T -- 超时/结束指令检测到 --> H
    H -- 退出唤醒，回到待唤醒 --> S

    %% 基础服务
    subgraph 基础服务
        I
        J
        K
        L
    end

    B -.-> I
    C -.-> I
    D -.-> I
    F -.-> I

    B -.-> J
    C -.-> J
    D -.-> J
    F -.-> J

    H -.-> C
    H -.-> D
    H -.-> F

    K -.-> B
    K -.-> C
    K -.-> D
    K -.-> F

    L -.-> B
    L -.-> C
    L -.-> D
    L -.-> F
```

---

## 3. 核心模块设计要点与接口说明

- **音频采集**：高效分帧，支持多平台麦克风输入。
- **降噪/VAD**：流式降噪与端点检测，提升后续识别准确率。
- **唤醒词检测**：支持自定义唤醒词，低功耗监听。
- **状态管理/事件总线**：统一管理唤醒、插话、超时、退出等事件，协调各模块状态。
- **ASR/LLM/TTS**：流式识别、对话理解、语音合成，接口标准化，支持多语言。
- **插话/中断判定**：仅在已唤醒状态下，TTS/ASR/LLM处理中，VAD检测到新语音且满足前置条件时触发。
- **自动退出唤醒**：超时或检测到“结束/休眠”指令，自动回到待唤醒。
- **推理后端适配**：自动检测并优先使用GPU/NPU等加速。
- **日志与监控**：全链路日志、性能埋点、异常捕获。
- **配置管理**：集中管理参数，支持热加载。
- **健康检查/自恢复**：模块健康检测，异常自动重启。

---

## 4. 状态流转与事件驱动机制

- **待唤醒**：仅唤醒词检测，低功耗监听。
- **唤醒成功**：切换为已唤醒，激活ASR/LLM/TTS。
- **交互中**：支持插话中断，实时响应用户新输入。
- **自动退出唤醒**：超时或检测到“结束”指令，回到待唤醒。
- **事件总线**：所有状态切换、插话/中断、退出等均通过事件驱动，确保响应及时。

---

## 5. 插话/中断与自动退出唤醒的判定逻辑

- **插话/中断**：
  - 仅在已唤醒状态下生效。
  - TTS/ASR/LLM处理中，VAD检测到新语音，且判定为有效插话时，立即中断当前任务，转而识别新输入。
  - 前置条件可配置（如系统状态、语音有效性等）。
- **自动退出唤醒**：
  - 交互超时（如10秒无输入）或识别到“结束/休眠”指令，自动回到待唤醒。
  - 可通过TTS播报“已休眠”等提示。

---

## 6. 硬件适配与性能优化策略

- 自动检测可用硬件，优先使用GPU/NPU加速。
- 支持多格式模型（pt/onnx/trt等），便于不同硬件加载。
- 支持多线程/异步处理，提升并发与吞吐。
- 内存池/缓存复用，防止内存泄漏。
- 关键路径性能埋点，便于后期优化。

---

## 7. 可扩展性与维护性设计

- 所有模块均有标准接口，便于升级/替换。
- 预留接口，便于后续扩展（如多麦克风阵列、声源定位等）。
- 配置集中管理，支持热加载和动态调整。
- 日志分级，便于问题定位和维护。

---

## 8. 关键设计决策说明

- 采用FunASR全链路方案，极大简化开发和维护。
- 事件驱动+状态机设计，确保系统响应及时、逻辑清晰。
- 插话/中断机制严格依赖系统状态，防止误触发。
- 自动退出唤醒机制提升用户体验和资源利用率。
- 硬件适配层屏蔽底层差异，便于跨平台部署。

---
