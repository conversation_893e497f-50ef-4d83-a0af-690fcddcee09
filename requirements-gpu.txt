# ============================================================================
# aibi语音交互系统 - GPU环境依赖配置
# 版本: v2.0
# 适用于: 使用NVIDIA GPU进行AI模型推理的环境
# CUDA版本: 12.1+
# 更新日期: 2025-01-23
# ============================================================================

# ==================== 核心框架依赖 ====================
# PyTorch GPU版本 - 深度学习框架 (CUDA 12.1)
--extra-index-url https://download.pytorch.org/whl/cu121
torch==2.3.1+cu121
torchaudio==2.3.1+cu121

# ONNX Runtime GPU版本 - 模型推理引擎
--extra-index-url https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/onnxruntime-cuda-12/pypi/simple/
onnxruntime-gpu==1.18.0

# TensorRT支持 (Linux only)
tensorrt-cu12==10.0.1; sys_platform == 'linux'
tensorrt-cu12-bindings==10.0.1; sys_platform == 'linux'
tensorrt-cu12-libs==10.0.1; sys_platform == 'linux'

# ==================== 音频处理核心库 ====================
# 音频文件读写和处理
soundfile==0.12.1
librosa==0.10.2

# 音频设备接口
pyaudio==0.2.14

# 语音活动检测
webrtcvad==2.0.10

# ==================== 语音AI模型库 ====================
# FunASR语音识别框架
funasr>=1.0.0

# ModelScope模型管理
modelscope==1.20.0

# 文本前端处理
WeTextProcessing==1.0.3

# Diffusers (用于语音合成)
diffusers==0.29.0

# Lightning (训练框架)
lightning==2.2.4

# ==================== 基础工具库 ====================
# 数值计算
numpy>=1.21.0,<2.0.0
scipy>=1.7.0,<2.0.0

# 配置管理
PyYAML>=6.0,<7.0
omegaconf>=2.3.0,<3.0.0
hydra-core>=1.3.2,<2.0.0
HyperPyYAML>=1.2.2,<2.0.0

# 网络请求
requests>=2.25.0,<3.0.0
httpx>=0.24.0,<1.0.0

# 进度条和日志
tqdm>=4.62.0,<5.0.0
colorama>=0.4.4,<1.0.0

# ==================== 数据处理库 ====================
# 数据分析
pandas>=1.3.0,<3.0.0
pyarrow>=18.1.0,<19.0.0

# 协议缓冲区
protobuf>=4.25,<5.0

# 网络图处理
networkx>=3.1,<4.0

# ==================== 开发工具库 ====================
# 类型检查
pydantic>=2.7.0,<3.0.0

# 网络工具
inflect>=7.3.1,<8.0.0

# 可视化
matplotlib>=3.7.5,<4.0.0
tensorboard>=2.14.0,<3.0.0

# ==================== 系统兼容性 ====================
# 跨平台兼容
pyworld>=0.3.4,<1.0.0

# 富文本输出
rich>=13.7.1,<14.0.0

# 音频格式支持
conformer>=0.3.2,<1.0.0

# ==================== 高级功能库 ====================
# 深度学习优化 (Linux only)
deepspeed>=0.14.2; sys_platform == 'linux'

# 模型格式支持
onnx>=1.16.0,<2.0.0
transformers>=4.40.1,<5.0.0

# 下载工具
gdown>=5.1.0,<6.0.0
wget>=3.2,<4.0.0