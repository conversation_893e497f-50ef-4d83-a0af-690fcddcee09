"""
配置验证脚本
用于检查配置文件的完整性和正确性
"""

import os
import sys
import yaml
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.config_manager import ConfigManager


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置验证器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.errors: List[str] = []
        self.warnings: List[str] = []
        
        # 设置日志
        logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
        self.logger = logging.getLogger(__name__)
        
    def load_config(self) -> bool:
        """
        加载配置文件
        
        Returns:
            bool: 是否加载成功
        """
        try:
            if not os.path.exists(self.config_path):
                self.errors.append(f"配置文件不存在: {self.config_path}")
                return False
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
                
            if not self.config:
                self.errors.append("配置文件为空")
                return False
                
            return True
            
        except Exception as e:
            self.errors.append(f"加载配置文件失败: {e}")
            return False
    
    def validate_all(self) -> Tuple[bool, List[str], List[str]]:
        """
        验证所有配置
        
        Returns:
            Tuple[bool, List[str], List[str]]: (是否通过, 错误列表, 警告列表)
        """
        if not self.load_config():
            return False, self.errors, self.warnings
        
        # 验证各个配置节
        self._validate_basic_config()
        self._validate_audio_config()
        self._validate_models_config()
        self._validate_hardware_config()
        self._validate_llm_config()
        self._validate_interrupt_config()
        self._validate_logging_config()
        self._validate_monitoring_config()
        self._validate_language_config()
        self._validate_debug_config()
        self._validate_security_config()
        self._validate_extensions_config()
        
        # 检查路径是否存在
        self._validate_paths()
        
        # 检查配置一致性
        self._validate_consistency()
        
        return len(self.errors) == 0, self.errors, self.warnings
    
    def _validate_basic_config(self):
        """验证基础配置"""
        # 检查唤醒词
        wake_word = self.config.get('wake_word')
        if not wake_word:
            self.errors.append("缺少唤醒词配置")
        elif not isinstance(wake_word, str):
            self.errors.append("唤醒词必须是字符串")
        
        # 检查唤醒词阈值
        threshold = self.config.get('wake_word_threshold')
        if threshold is not None:
            if not isinstance(threshold, (int, float)):
                self.errors.append("唤醒词阈值必须是数字")
            elif not 0 <= threshold <= 1:
                self.errors.append("唤醒词阈值必须在0-1之间")
        
        # 检查交互超时
        timeout = self.config.get('interaction_timeout')
        if timeout is not None:
            if not isinstance(timeout, int):
                self.errors.append("交互超时必须是整数")
            elif timeout <= 0:
                self.errors.append("交互超时必须大于0")
    
    def _validate_audio_config(self):
        """验证音频配置"""
        audio_config = self.config.get('audio', {})
        if not audio_config:
            self.errors.append("缺少音频配置节")
            return
        
        # 验证输入配置
        input_config = audio_config.get('input', {})
        if not input_config:
            self.errors.append("缺少音频输入配置")
        else:
            self._validate_audio_input(input_config)
        
        # 验证输出配置
        output_config = audio_config.get('output', {})
        if not output_config:
            self.errors.append("缺少音频输出配置")
        else:
            self._validate_audio_output(output_config)
        
        # 验证VAD配置
        vad_config = audio_config.get('vad', {})
        if not vad_config:
            self.errors.append("缺少VAD配置")
        else:
            self._validate_vad_config(vad_config)
    
    def _validate_audio_input(self, input_config: Dict):
        """验证音频输入配置"""
        # 采样率
        sample_rate = input_config.get('sample_rate')
        if sample_rate is not None:
            if not isinstance(sample_rate, int):
                self.errors.append("采样率必须是整数")
            elif sample_rate not in [8000, 16000, 22050, 44100, 48000]:
                self.warnings.append(f"采样率 {sample_rate} 不是标准值")
        
        # 声道数
        channels = input_config.get('channels')
        if channels is not None:
            if not isinstance(channels, int):
                self.errors.append("声道数必须是整数")
            elif channels not in [1, 2]:
                self.errors.append("声道数必须是1或2")
        
        # 块大小
        chunk_size = input_config.get('chunk_size')
        if chunk_size is not None:
            if not isinstance(chunk_size, int):
                self.errors.append("音频块大小必须是整数")
            elif chunk_size <= 0:
                self.errors.append("音频块大小必须大于0")
    
    def _validate_audio_output(self, output_config: Dict):
        """验证音频输出配置"""
        # 采样率
        sample_rate = output_config.get('sample_rate')
        if sample_rate is not None:
            if not isinstance(sample_rate, int):
                self.errors.append("输出采样率必须是整数")
            elif sample_rate not in [8000, 16000, 22050, 44100, 48000]:
                self.warnings.append(f"输出采样率 {sample_rate} 不是标准值")
        
        # 声道数
        channels = output_config.get('channels')
        if channels is not None:
            if not isinstance(channels, int):
                self.errors.append("输出声道数必须是整数")
            elif channels not in [1, 2]:
                self.errors.append("输出声道数必须是1或2")
    
    def _validate_vad_config(self, vad_config: Dict):
        """验证VAD配置"""
        # 帧长度
        frame_duration = vad_config.get('frame_duration_ms')
        if frame_duration is not None:
            if not isinstance(frame_duration, (int, float)):
                self.errors.append("VAD帧长度必须是数字")
            elif frame_duration <= 0:
                self.errors.append("VAD帧长度必须大于0")
        
        # 阈值
        threshold = vad_config.get('threshold')
        if threshold is not None:
            if not isinstance(threshold, (int, float)):
                self.errors.append("VAD阈值必须是数字")
            elif not 0 <= threshold <= 1:
                self.errors.append("VAD阈值必须在0-1之间")
    
    def _validate_models_config(self):
        """验证模型配置"""
        models_config = self.config.get('models', {})
        if not models_config:
            self.errors.append("缺少模型配置节")
            return
        
        # 检查必需的模型路径
        required_paths = ['asr_model_path', 'kws_model_path', 'tts_model_path', 'vad_model_path']
        for path_key in required_paths:
            if path_key not in models_config:
                self.errors.append(f"缺少模型路径配置: {path_key}")
        
        # 验证ASR配置
        asr_config = models_config.get('asr', {})
        if asr_config:
            self._validate_asr_config(asr_config)
        
        # 验证KWS配置
        kws_config = models_config.get('kws', {})
        if kws_config:
            self._validate_kws_config(kws_config)
        
        # 验证TTS配置
        tts_config = models_config.get('tts', {})
        if tts_config:
            self._validate_tts_config(tts_config)
    
    def _validate_asr_config(self, asr_config: Dict):
        """验证ASR配置"""
        # 束搜索大小
        beam_size = asr_config.get('beam_size')
        if beam_size is not None:
            if not isinstance(beam_size, int):
                self.errors.append("ASR束搜索大小必须是整数")
            elif beam_size <= 0:
                self.errors.append("ASR束搜索大小必须大于0")
        
        # 最大长度
        max_length = asr_config.get('max_length')
        if max_length is not None:
            if not isinstance(max_length, int):
                self.errors.append("ASR最大长度必须是整数")
            elif max_length <= 0:
                self.errors.append("ASR最大长度必须大于0")
    
    def _validate_kws_config(self, kws_config: Dict):
        """验证KWS配置"""
        # 特征维度
        feature_dim = kws_config.get('feature_dim')
        if feature_dim is not None:
            if not isinstance(feature_dim, int):
                self.errors.append("KWS特征维度必须是整数")
            elif feature_dim <= 0:
                self.errors.append("KWS特征维度必须大于0")
        
        # 窗长
        window_size = kws_config.get('window_size_ms')
        if window_size is not None:
            if not isinstance(window_size, (int, float)):
                self.errors.append("KWS窗长必须是数字")
            elif window_size <= 0:
                self.errors.append("KWS窗长必须大于0")
    
    def _validate_tts_config(self, tts_config: Dict):
        """验证TTS配置"""
        # 语速
        speed = tts_config.get('speed')
        if speed is not None:
            if not isinstance(speed, (int, float)):
                self.errors.append("TTS语速必须是数字")
            elif not 0.5 <= speed <= 2.0:
                self.errors.append("TTS语速必须在0.5-2.0之间")
        
        # 音量
        volume = tts_config.get('volume')
        if volume is not None:
            if not isinstance(volume, (int, float)):
                self.errors.append("TTS音量必须是数字")
            elif not 0.0 <= volume <= 2.0:
                self.errors.append("TTS音量必须在0.0-2.0之间")
    
    def _validate_hardware_config(self):
        """验证硬件配置"""
        hardware_config = self.config.get('hardware', {})
        if not hardware_config:
            self.errors.append("缺少硬件配置节")
            return
        
        # 设备配置
        device = hardware_config.get('device')
        if device is not None:
            if not isinstance(device, str):
                self.errors.append("设备配置必须是字符串")
            elif device not in ['cpu', 'gpu', 'npu', 'auto']:
                self.errors.append("设备配置必须是cpu/gpu/npu/auto之一")
        
        # GPU内存比例
        gpu_memory = hardware_config.get('gpu_memory_fraction')
        if gpu_memory is not None:
            if not isinstance(gpu_memory, (int, float)):
                self.errors.append("GPU内存比例必须是数字")
            elif not 0 < gpu_memory <= 1:
                self.errors.append("GPU内存比例必须在0-1之间")
        
        # CPU线程数
        num_threads = hardware_config.get('num_threads')
        if num_threads is not None:
            if not isinstance(num_threads, int):
                self.errors.append("CPU线程数必须是整数")
            elif num_threads <= 0:
                self.errors.append("CPU线程数必须大于0")
    
    def _validate_llm_config(self):
        """验证LLM配置"""
        llm_config = self.config.get('llm', {})
        if not llm_config:
            self.warnings.append("缺少LLM配置节")
            return
        
        # API URL
        api_url = llm_config.get('dify_api_url')
        if api_url and not isinstance(api_url, str):
            self.errors.append("Dify API URL必须是字符串")
        
        # API Key
        api_key = llm_config.get('dify_api_key')
        if api_key and not isinstance(api_key, str):
            self.errors.append("Dify API Key必须是字符串")
        
        # 最大token数
        max_tokens = llm_config.get('max_tokens')
        if max_tokens is not None:
            if not isinstance(max_tokens, int):
                self.errors.append("最大token数必须是整数")
            elif max_tokens <= 0:
                self.errors.append("最大token数必须大于0")
        
        # 温度
        temperature = llm_config.get('temperature')
        if temperature is not None:
            if not isinstance(temperature, (int, float)):
                self.errors.append("温度必须是数字")
            elif not 0 <= temperature <= 2:
                self.errors.append("温度必须在0-2之间")
    
    def _validate_interrupt_config(self):
        """验证插话中断配置"""
        interrupt_config = self.config.get('interrupt', {})
        if not interrupt_config:
            self.warnings.append("缺少插话中断配置节")
            return
        
        # 启用状态
        enabled = interrupt_config.get('enabled')
        if enabled is not None and not isinstance(enabled, bool):
            self.errors.append("插话启用状态必须是布尔值")
        
        # 最小持续时间
        min_duration = interrupt_config.get('min_interrupt_duration_ms')
        if min_duration is not None:
            if not isinstance(min_duration, (int, float)):
                self.errors.append("最小插话持续时间必须是数字")
            elif min_duration <= 0:
                self.errors.append("最小插话持续时间必须大于0")
    
    def _validate_logging_config(self):
        """验证日志配置"""
        logging_config = self.config.get('logging', {})
        if not logging_config:
            self.warnings.append("缺少日志配置节")
            return
        
        # 日志级别
        level = logging_config.get('level')
        if level is not None:
            if not isinstance(level, str):
                self.errors.append("日志级别必须是字符串")
            elif level not in ['debug', 'info', 'warning', 'error']:
                self.errors.append("日志级别必须是debug/info/warning/error之一")
        
        # 文件日志
        file_enabled = logging_config.get('file_enabled')
        if file_enabled is not None and not isinstance(file_enabled, bool):
            self.errors.append("文件日志启用状态必须是布尔值")
    
    def _validate_monitoring_config(self):
        """验证监控配置"""
        monitoring_config = self.config.get('monitoring', {})
        if not monitoring_config:
            self.warnings.append("缺少监控配置节")
            return
        
        # 健康检查间隔
        health_interval = monitoring_config.get('health_check_interval')
        if health_interval is not None:
            if not isinstance(health_interval, (int, float)):
                self.errors.append("健康检查间隔必须是数字")
            elif health_interval <= 0:
                self.errors.append("健康检查间隔必须大于0")
        
        # 自动重启
        auto_restart = monitoring_config.get('enable_auto_restart')
        if auto_restart is not None and not isinstance(auto_restart, bool):
            self.errors.append("自动重启状态必须是布尔值")
    
    def _validate_language_config(self):
        """验证语言配置"""
        language_config = self.config.get('language', {})
        if not language_config:
            self.warnings.append("缺少语言配置节")
            return
        
        # 默认语言
        default = language_config.get('default')
        if default is not None:
            if not isinstance(default, str):
                self.errors.append("默认语言必须是字符串")
            elif default not in ['zh-CN', 'en-US', 'ja-JP']:
                self.warnings.append(f"默认语言 {default} 不在支持列表中")
    
    def _validate_debug_config(self):
        """验证调试配置"""
        debug_config = self.config.get('debug', {})
        if not debug_config:
            return
        
        # 调试模式
        enabled = debug_config.get('enabled')
        if enabled is not None and not isinstance(enabled, bool):
            self.errors.append("调试模式状态必须是布尔值")
        
        # 保存音频
        save_audio = debug_config.get('save_audio')
        if save_audio is not None and not isinstance(save_audio, bool):
            self.errors.append("保存音频状态必须是布尔值")
    
    def _validate_security_config(self):
        """验证安全配置"""
        security_config = self.config.get('security', {})
        if not security_config:
            return
        
        # API认证
        enable_auth = security_config.get('enable_api_auth')
        if enable_auth is not None and not isinstance(enable_auth, bool):
            self.errors.append("API认证状态必须是布尔值")
    
    def _validate_extensions_config(self):
        """验证扩展配置"""
        extensions_config = self.config.get('extensions', {})
        if not extensions_config:
            return
        
        # 插件启用
        plugins_enabled = extensions_config.get('plugins_enabled')
        if plugins_enabled is not None and not isinstance(plugins_enabled, bool):
            self.errors.append("插件启用状态必须是布尔值")
    
    def _validate_paths(self):
        """验证路径是否存在"""
        models_config = self.config.get('models', {})
        
        # 检查模型路径
        for path_key in ['asr_model_path', 'kws_model_path', 'tts_model_path', 'vad_model_path']:
            path = models_config.get(path_key)
            if path and not os.path.exists(path):
                self.warnings.append(f"模型路径不存在: {path}")
        
        # 检查日志路径
        logging_config = self.config.get('logging', {})
        log_file = logging_config.get('log_file')
        if log_file:
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                self.warnings.append(f"日志目录不存在: {log_dir}")
    
    def _validate_consistency(self):
        """验证配置一致性"""
        # 检查音频输入输出采样率是否一致
        audio_config = self.config.get('audio', {})
        input_config = audio_config.get('input', {})
        output_config = audio_config.get('output', {})
        
        input_sample_rate = input_config.get('sample_rate')
        output_sample_rate = output_config.get('sample_rate')
        
        if input_sample_rate and output_sample_rate and input_sample_rate != output_sample_rate:
            self.warnings.append("音频输入输出采样率不一致，可能导致音频问题")
        
        # 检查设备配置与硬件配置的一致性
        device = self.config.get('hardware', {}).get('device')
        if device == 'gpu':
            # 检查是否有GPU相关配置
            gpu_memory = self.config.get('hardware', {}).get('gpu_memory_fraction')
            if gpu_memory is None:
                self.warnings.append("使用GPU但未配置GPU内存比例")
    
    def print_report(self):
        """打印验证报告"""
        print("=" * 60)
        print("aibi语音交互系统 - 配置验证报告")
        print("=" * 60)
        
        if self.errors:
            print(f"\n❌ 发现 {len(self.errors)} 个错误:")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
        
        if self.warnings:
            print(f"\n⚠️  发现 {len(self.warnings)} 个警告:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
        
        if not self.errors and not self.warnings:
            print("\n✅ 配置文件验证通过，未发现错误或警告")
        elif not self.errors:
            print(f"\n✅ 配置文件验证通过，但有 {len(self.warnings)} 个警告")
        else:
            print(f"\n❌ 配置文件验证失败，发现 {len(self.errors)} 个错误")
        
        print("=" * 60)


def main():
    """主函数"""
    validator = ConfigValidator()
    success, errors, warnings = validator.validate_all()
    validator.print_report()
    
    if not success:
        sys.exit(1)
    elif warnings:
        print("\n建议修复警告以确保最佳性能")
    else:
        print("\n配置文件完全正确！")


if __name__ == "__main__":
    main() 