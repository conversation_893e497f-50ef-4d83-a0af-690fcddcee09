#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - TTS集成测试脚本
测试TTS模块与主程序的集成
"""

import os
import sys
import time
import threading
import queue
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from modules.config_manager import ConfigManager
from modules.tts import TTSProcessor
from modules.audio_output import AudioOutput
from modules.event_bus import EventBus
from modules.logging_utils import setup_logger

def test_tts_integration():
    """测试TTS模块集成"""
    print("🚀 aibi语音交互系统 - TTS集成测试")
    print("=" * 60)
    
    try:
        # 初始化配置
        config = ConfigManager()
        logger = setup_logger("tts_integration", config)
        
        # 初始化模块
        print("🔧 初始化模块...")
        tts_processor = TTSProcessor(config)
        audio_output = AudioOutput(config)
        event_bus = EventBus()
        
        # 检查TTS模块状态
        print(f"  TTS初始化状态: {'成功' if tts_processor.is_initialized else '失败'}")
        print(f"  TTS模型类型: {tts_processor.model_type}")
        print(f"  TTS健康状态: {'正常' if tts_processor.is_healthy() else '异常'}")
        
        # 测试文本列表
        test_texts = [
            "TTS模块集成测试开始。",
            "正在测试语音合成功能。",
            "集成测试即将完成。"
        ]
        
        # 创建TTS队列（模拟主程序的队列机制）
        tts_queue = queue.Queue()
        
        # 事件处理器
        def on_tts_completed(data):
            print(f"  ✅ TTS完成事件: {data.get('text', '')}")
        
        event_bus.subscribe("tts_completed", on_tts_completed)
        
        # TTS处理线程（模拟主程序的TTS处理循环）
        def tts_processing_loop():
            while True:
                try:
                    tts_task = tts_queue.get(timeout=1.0)
                    if tts_task is None:  # 退出信号
                        break
                    
                    print(f"  🎵 合成文本: {tts_task}")
                    audio_data = tts_processor.synthesize(tts_task)
                    
                    if audio_data is not None:
                        # 播放音频（如果有音频设备）
                        try:
                            audio_output.play(audio_data)
                            print(f"  🔊 播放完成")
                        except Exception as e:
                            print(f"  ⚠️ 播放失败: {e}")
                        
                        # 发布完成事件
                        event_bus.publish("tts_completed", {"text": tts_task})
                    else:
                        print(f"  ❌ 合成失败")
                        
                except queue.Empty:
                    continue
                except Exception as e:
                    print(f"  ❌ TTS处理异常: {e}")
        
        # 启动TTS处理线程
        print("\n🎵 启动TTS处理线程...")
        tts_thread = threading.Thread(target=tts_processing_loop, daemon=True)
        tts_thread.start()
        
        # 测试TTS合成
        print("\n📝 测试TTS合成...")
        for i, text in enumerate(test_texts):
            print(f"\n  测试 {i+1}/{len(test_texts)}: {text}")
            tts_queue.put(text)
            time.sleep(2)  # 等待处理完成
        
        # 等待所有任务完成
        print("\n⏳ 等待所有任务完成...")
        time.sleep(3)
        
        # 停止TTS线程
        tts_queue.put(None)  # 发送退出信号
        tts_thread.join(timeout=5)
        
        # 显示TTS统计信息
        print("\n📊 TTS统计信息:")
        model_info = tts_processor.get_model_info()
        for key, value in model_info.items():
            if key in ['synthesis_count', 'total_synthesis_time', 'avg_synthesis_time', 'last_synthesis_time']:
                print(f"  {key}: {value}")
        
        print("\n✅ TTS集成测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ TTS集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tts_event_integration():
    """测试TTS事件集成"""
    print("\n🔄 测试TTS事件集成...")
    
    try:
        config = ConfigManager()
        tts_processor = TTSProcessor(config)
        event_bus = EventBus()
        
        # 事件计数器
        event_count = 0
        
        def on_tts_event(data):
            nonlocal event_count
            event_count += 1
            print(f"  📨 收到TTS事件 {event_count}: {data}")
        
        # 订阅事件
        event_bus.subscribe("tts_completed", on_tts_event)
        event_bus.subscribe("tts_started", on_tts_event)
        event_bus.subscribe("tts_error", on_tts_event)
        
        # 模拟事件发布
        test_events = [
            ("tts_started", {"text": "开始合成"}),
            ("tts_completed", {"text": "合成完成", "duration": 1.5}),
            ("tts_error", {"text": "合成错误", "error": "模型未加载"})
        ]
        
        for event_type, event_data in test_events:
            print(f"  📤 发布事件: {event_type}")
            event_bus.publish(event_type, event_data)
            time.sleep(0.5)
        
        print(f"  📊 总共处理事件: {event_count}")
        print("  ✅ 事件集成测试完成")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 事件集成测试失败: {e}")
        return False

def test_tts_config_integration():
    """测试TTS配置集成"""
    print("\n⚙️ 测试TTS配置集成...")
    
    try:
        config = ConfigManager()
        
        # 测试配置读取
        tts_config = {
            "model_path": config.get("models.tts.model_path"),
            "speed": config.get("model_params.tts.speed"),
            "volume": config.get("model_params.tts.volume"),
            "pitch": config.get("model_params.tts.pitch"),
            "device": config.get("hardware.device"),
            "enable_fp16": config.get("hardware.enable_fp16")
        }
        
        print("  📋 TTS配置:")
        for key, value in tts_config.items():
            print(f"    {key}: {value}")
        
        # 测试配置应用
        tts_processor = TTSProcessor(config)
        model_info = tts_processor.get_model_info()
        
        print("  🔧 TTS模块配置:")
        for key in ['model_path', 'speed', 'volume', 'pitch', 'device', 'enable_fp16']:
            if key in model_info:
                print(f"    {key}: {model_info[key]}")
        
        print("  ✅ 配置集成测试完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 配置集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 开始TTS集成测试...")
    
    # 测试TTS模块集成
    test1_result = test_tts_integration()
    
    # 测试事件集成
    test2_result = test_tts_event_integration()
    
    # 测试配置集成
    test3_result = test_tts_config_integration()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print(f"  TTS模块集成: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"  事件集成: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"  配置集成: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    overall_success = test1_result and test2_result and test3_result
    print(f"\n🎯 总体结果: {'✅ 全部通过' if overall_success else '❌ 部分失败'}")
    
    if overall_success:
        print("\n🚀 TTS模块已成功集成到aibi语音交互系统！")
        print("💡 下一步:")
        print("  1. 下载CosyVoice模型: python tools/download_cosyvoice_models.py")
        print("  2. 测试完整系统: python main.py")
    else:
        print("\n🔧 需要修复的问题:")
        if not test1_result:
            print("  - TTS模块集成问题")
        if not test2_result:
            print("  - 事件系统集成问题")
        if not test3_result:
            print("  - 配置系统集成问题")

if __name__ == "__main__":
    main()
