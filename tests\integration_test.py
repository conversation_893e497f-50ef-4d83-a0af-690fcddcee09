#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - 全链路自动化集成测试（麦克风真实音频）
用户需根据提示对麦克风说唤醒词、指令、插话等，系统自动检测并断言。
"""
import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from main import AibiVoiceSystem

class TestEventCollector:
    """事件收集器，用于断言事件流"""
    def __init__(self):
        self.events = []
    def handler(self, data):
        self.events.append(data)
    def clear(self):
        self.events.clear()
    def last(self):
        return self.events[-1] if self.events else None
    def count(self):
        return len(self.events)

def wait_for(condition_fn, timeout=10, interval=0.1, desc=None):
    """等待某个条件成立"""
    waited = 0
    while waited < timeout:
        if condition_fn():
            return True
        time.sleep(interval)
        waited += interval
    if desc:
        raise AssertionError(f"超时: {desc}")
    return False

def test_wake_and_asr(system, collector):
    print("[TEST] 真实麦克风唤醒-交互流程，请对麦克风说出唤醒词...")
    collector.clear()
    # 等待唤醒事件
    wait_for(lambda: system.is_wake_state, timeout=15, desc="未检测到唤醒词")
    print("✅ 检测到唤醒，进入交互。请说出指令/内容...")
    # 等待ASR输出
    try:
        wait_for(lambda: any(e.get("text") for e in collector.events if isinstance(e, dict)), timeout=15, desc="ASR未输出文本")
    except AssertionError:
        print("ASR事件收集：", collector.events)
        raise
    print("✅ ASR输出文本，唤醒-交互流程通过")

def test_interrupt(system, collector):
    print("[TEST] 真实麦克风插话/中断流程，请在TTS/ASR播报时插话...")
    collector.clear()
    print("请先唤醒系统并进入交互，然后在TTS/ASR播报时插话...")
    # 等待插话事件（通过事件流断言）
    print("等待插话事件触发（请插话）...")
    # 这里假设插话会导致新的ASR文本输出
    wait_for(lambda: len(collector.events) >= 2, timeout=20, desc="未检测到插话/中断")
    print("✅ 插话/中断流程通过")

def test_auto_exit(system, collector):
    print("[TEST] 自动退出流程，请保持安静等待系统自动退出...")
    collector.clear()
    # 等待系统状态切回sleeping
    wait_for(lambda: system.state_manager.get_state() == "sleeping", timeout=20, desc="未自动退出到休眠状态")
    print("✅ 自动退出流程通过")

def test_error_recovery(system, collector):
    print("[TEST] 异常恢复流程（可选）...")
    collector.clear()
    print("可手动触发异常或直接跳过...")
    # 这里可选：手动触发异常
    # system.event_bus.publish("error", {"message": "test error"})
    # time.sleep(1)
    # assert system.is_running, "系统未自动恢复"
    print("✅ 异常恢复流程通过（如未测试可忽略）")

def main():
    print("🚦 aibi语音交互系统 - 全链路自动化集成测试（麦克风真实音频）")
    system = AibiVoiceSystem()
    collector = TestEventCollector()
    # 订阅ASR、TTS等关键事件
    system.event_bus.subscribe("asr_result", collector.handler)
    system.event_bus.subscribe("tts_completed", collector.handler)
    # 启动系统（后台线程）
    t = threading.Thread(target=system.start, daemon=True)
    t.start()
    time.sleep(2)
    try:
        test_wake_and_asr(system, collector)
        test_interrupt(system, collector)
        test_auto_exit(system, collector)
        test_error_recovery(system, collector)
        print("\n🎉 全链路自动化测试全部通过！")
    finally:
        system.stop()
        t.join(timeout=5)

if __name__ == "__main__":
    main()
