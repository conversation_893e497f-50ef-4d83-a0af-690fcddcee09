import numpy as np
import pyaudio
import soundfile as sf
from typing import Optional

class AudioOutput:
    """音频输出处理器"""
    
    def __init__(self, config):
        """初始化音频输出"""
        self.config = config
        self.sample_rate = config.get("audio.output.sample_rate", 16000)
        self.channels = config.get("audio.output.channels", 1)
        self.device_index = config.get("audio.output.device_index", 0)
        
        # 初始化PyAudio
        self.audio = pyaudio.PyAudio()
        self.stream = None
        
    def play(self, audio_data: np.ndarray) -> bool:
        """
        播放音频数据
        
        Args:
            audio_data: 音频数据，numpy数组
            
        Returns:
            是否播放成功
        """
        try:
            if audio_data is None or len(audio_data) == 0:
                print("音频数据为空，跳过播放")
                return False
                
            # 确保音频数据是float32格式
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
                
            # 确保是一维数组（单声道）
            if audio_data.ndim > 1:
                if audio_data.shape[1] > 1:
                    # 多声道转单声道
                    audio_data = audio_data.mean(axis=1)
                else:
                    # 如果是(N,1)形状，转为(N,)
                    audio_data = audio_data.flatten()
                    
            print(f"准备播放音频: 长度={len(audio_data)}, 数据类型={audio_data.dtype}, 形状={audio_data.shape}")
            
            # 转换为字节数据
            audio_bytes = audio_data.tobytes()
            
            # 关闭现有流（如果存在）
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            
            # 尝试不同的设备配置
            try:
                # 尝试使用默认设备
                self.stream = self.audio.open(
                    format=pyaudio.paFloat32,
                    channels=1,
                    rate=self.sample_rate,
                    output=True
                )
                print("✅ 使用默认音频设备创建流成功")
            except Exception as e1:
                try:
                    # 尝试使用指定设备
                    self.stream = self.audio.open(
                        format=pyaudio.paFloat32,
                        channels=1,
                        rate=self.sample_rate,
                        output=True,
                        output_device_index=self.device_index
                    )
                    print(f"✅ 使用设备{self.device_index}创建流成功")
                except Exception as e2:
                    print(f"❌ 创建音频流失败:")
                    print(f"  默认设备错误: {e1}")
                    print(f"  指定设备错误: {e2}")
                    return False
            
            # 播放音频
            self.stream.write(audio_bytes)
            print("✅ 音频播放完成")
            
            return True
            
        except Exception as e:
            print(f"❌ 音频播放失败: {e}")
            return False
    
    def play_file(self, file_path: str) -> bool:
        """
        播放音频文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            是否播放成功
        """
        try:
            # 读取音频文件
            audio_data, sample_rate = sf.read(file_path)
            
            # 如果是立体声，转换为单声道
            if len(audio_data.shape) > 1:
                audio_data = audio_data.mean(axis=1)
            
            # 确保音频数据是float32格式
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # 重新采样到目标采样率（如果需要）
            if sample_rate != self.sample_rate:
                # 简单的重采样：如果目标采样率更高，重复采样；如果更低，跳过采样
                if sample_rate > self.sample_rate:
                    # 降采样
                    step = sample_rate // self.sample_rate
                    audio_data = audio_data[::step]
                else:
                    # 升采样（简单重复）
                    ratio = self.sample_rate // sample_rate
                    audio_data = np.repeat(audio_data, ratio)
            
            # 确保音频数据长度合适
            if len(audio_data) > self.sample_rate * 10:  # 限制最长10秒
                audio_data = audio_data[:int(self.sample_rate * 10)]
            
            # 确保音频数据在有效范围内
            if np.max(np.abs(audio_data)) > 0:
                audio_data = audio_data / np.max(np.abs(audio_data)) * 0.8  # 归一化到0.8
            
            # 关闭现有流（如果存在）
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            
            # 创建新的音频流
            self.stream = self.audio.open(
                format=pyaudio.paFloat32,
                channels=1,
                rate=self.sample_rate,
                output=True
            )
            
            # 播放音频
            self.stream.write(audio_data.tobytes())
            
            return True
            
        except Exception as e:
            print(f"播放音频文件失败: {e}")
            return False
    
    def stop(self):
        """停止音频播放"""
        try:
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None
        except Exception as e:
            print(f"停止音频播放失败: {e}")
    
    def __del__(self):
        """析构函数"""
        self.stop()
        if hasattr(self, 'audio'):
            self.audio.terminate()

if __name__ == "__main__":
    # 测试音频播放功能
    print("AudioOutput 模块测试")
