#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - 主程序
实现端到端语音交互，包含唤醒词检测、ASR、LLM、TTS等完整流程
"""

import os
import sys
import time
import threading
import queue
import signal
import logging
import numpy as np
import random
from pathlib import Path
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from modules.config_manager import ConfigManager
from modules.audio_input import AudioInput
from modules.vad import VADProcessor
from modules.kws import WakeWordDetector
from modules.asr import ASRProcessor
from modules.llm_client import LLMClient
from modules.tts import TTSProcessor
from modules.audio_output import AudioOutput
from modules.state_manager import StateManager
from modules.event_bus import EventBus
from modules.interrupt import InterruptManager
from modules.auto_exit import AutoExitManager
from modules.hardware_adapter import HardwareAdapter
from modules.health_check import HealthChecker
from modules.logging_utils import setup_logger
from modules.denoise import DenoiseProcessor

class AibiVoiceSystem:
    """aibi语音交互系统主控制器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化语音交互系统"""
        self.config = ConfigManager(config_path)
        self.logger = setup_logger("aibi", self.config)
        
        # 系统状态
        self.is_running = False
        self.is_wake_state = False  # 是否处于已唤醒状态
        
        # 初始化各个模块
        self._init_modules()
        
        # 音频处理队列
        self.audio_queue = queue.Queue(maxsize=100)
        self.vad_queue = queue.Queue(maxsize=50)
        self.asr_queue = queue.Queue(maxsize=20)
        self.tts_queue = queue.Queue(maxsize=20)
        
        # 事件总线
        self.event_bus = EventBus()
        self._setup_event_handlers()
        
        # 状态管理器
        self.state_manager = StateManager()
        
        # 中断管理器
        self.interrupt_manager = InterruptManager(self.state_manager)
        
        # 自动退出管理器
        self.auto_exit_manager = AutoExitManager(self.state_manager)
        
        # 健康检查器
        self.health_checker = HealthChecker(self.config)
        
        # 处理线程
        self.audio_thread = None
        self.vad_thread = None
        self.kws_thread = None
        self.asr_thread = None
        self.tts_thread = None
        
        self.logger.info("aibi语音交互系统初始化完成")
    
    def _init_modules(self):
        """初始化各个功能模块"""
        try:
            # 硬件适配器
            self.hardware_adapter = HardwareAdapter(self.config)
            
            # 音频输入
            self.audio_input = AudioInput(
                samplerate=self.config.get("audio.input.sample_rate", 16000),
                channels=self.config.get("audio.input.channels", 1),
                blocksize=self.config.get("audio.input.chunk_size", 1024)
            )
            
            # VAD处理器
            self.vad_processor = VADProcessor(self.config)
            # 降噪处理器
            self.denoise_processor = DenoiseProcessor(self.config)
            
            # 唤醒词检测器
            self.kws_detector = WakeWordDetector(
                model_type=self.config.get("kws_model_type", "onnx"),
                model_path=self.config.get("kws_model_path", "./models/cnn/hey_aibi.onnx"),
                model_info_path=self.config.get("kws_model_info", "./models/cnn/model_info.txt"),
                device=self.hardware_adapter.get_device()
            )
            
            # ASR处理器
            self.asr_processor = ASRProcessor(self.config)
            
            # LLM客户端
            self.llm_client = LLMClient(self.config)
            
            # TTS处理器
            self.tts_processor = TTSProcessor(self.config)
            
            # 音频输出
            self.audio_output = AudioOutput(self.config)
            
            self.logger.info("所有模块初始化成功")
            
        except Exception as e:
            self.logger.error(f"模块初始化失败: {e}")
            raise
    
    def _setup_event_handlers(self):
        """设置事件处理器"""
        # 唤醒事件
        self.event_bus.subscribe("wake_word_detected", self._on_wake_word_detected)
        
        # 语音识别事件
        self.event_bus.subscribe("asr_result", self._on_asr_result)
        
        # TTS完成事件
        self.event_bus.subscribe("tts_completed", self._on_tts_completed)
        
        # 中断事件
        self.event_bus.subscribe("interrupt_detected", self._on_interrupt_detected)
        
        # 自动退出事件
        self.event_bus.subscribe("auto_exit_triggered", self._on_auto_exit_triggered)
        
        # 错误事件
        self.event_bus.subscribe("error", self._on_error)
    
    def _on_wake_word_detected(self, data: Dict[str, Any]):
        """唤醒词检测事件处理"""
        self.logger.info(f"检测到唤醒词: {data}")
        self.is_wake_state = True
        self.state_manager.set_state("awake")
        
        # 播放唤醒提示音
        self._play_wake_sound()
        
        # 启动ASR处理
        self._start_asr_processing()
    
    def _on_asr_result(self, data: Dict[str, Any]):
        """语音识别结果事件处理，日志分级优化"""
        text = data.get("text", "")
        confidence = data.get("confidence", 0.0)
        debug_mode = self.config.get("debug.enabled", False)
        if not text or not text.strip():
            self.logger.info("ASR无有效文本，跳过LLM和TTS")
            return
        if debug_mode:
            self.logger.debug(f"语音识别结果: {text} (置信度: {confidence:.3f})")
        else:
            self.logger.info(f"语音识别结果: {text}")
        if self._is_exit_command(text):
            self.event_bus.publish("auto_exit_triggered", {"reason": "exit_command"})
            return
        self._process_with_llm(text)
    
    def _on_tts_completed(self, data: Dict[str, Any]):
        """TTS完成事件处理"""
        self.logger.info("TTS合成完成")
        # 可以在这里添加后续处理逻辑
    
    def _on_interrupt_detected(self, data: Dict[str, Any]):
        """中断检测事件处理，插话优先级"""
        self.logger.info("检测到用户插话/中断，立即中断TTS/ASR/LLM")
        self.audio_output.stop()
        self.asr_processor.stop_streaming()
        new_audio = data.get("audio")
        if new_audio is not None:
            self.asr_queue.put(new_audio)
        # 可重置状态/计时器等
    
    def _on_auto_exit_triggered(self, data: Dict[str, Any]):
        """自动退出事件处理"""
        reason = data.get("reason", "timeout")
        self.logger.info(f"触发自动退出: {reason}")
        self.is_wake_state = False
        self.state_manager.set_state("sleeping")
        self._play_sleep_sound()
        self._stop_asr_processing()
        # 可重置插话/计时器等
    
    def _on_error(self, data: Dict[str, Any]):
        """错误事件处理，异常恢复"""
        error_msg = data.get("message", "未知错误")
        self.logger.error(f"系统错误: {error_msg}")
        self.logger.info("尝试自动恢复系统...")
        self._stop_asr_processing()
        self._start_asr_processing()
    
    def _is_exit_command(self, text: str) -> bool:
        """检查是否为退出指令"""
        exit_commands = self.config.get("auto_exit_commands", ["结束", "停止", "休眠", "再见"])
        return any(cmd in text for cmd in exit_commands)
    
    def _play_wake_sound(self):
        """播放唤醒提示音"""
        try:
            self.logger.info("🔔 播放唤醒提示音")
            # 从assets/audio目录随机播放音频文件
            audio_files = [f for f in os.listdir("assets/audio") if f.endswith(".wav")]
            if audio_files:
                random_file = random.choice(audio_files)
                file_path = os.path.join("assets/audio", random_file)
                success = self.audio_output.play_file(file_path)
                if success:
                    self.logger.info(f"✅ 播放唤醒提示音: {random_file}")
                else:
                    self.logger.warning(f"⚠️ 播放唤醒提示音失败: {random_file}")
            else:
                self.logger.warning("⚠️ 未找到唤醒提示音频文件")
        except Exception as e:
            self.logger.error(f"❌ 播放唤醒提示音失败: {e}")
    
    def _play_sleep_sound(self):
        """播放休眠提示音"""
        try:
            self.logger.info("😴 播放休眠提示音")
            # 从assets/audio目录随机播放音频文件
            audio_files = [f for f in os.listdir("assets/audio") if f.endswith(".wav")]
            if audio_files:
                random_file = random.choice(audio_files)
                file_path = os.path.join("assets/audio", random_file)
                success = self.audio_output.play_file(file_path)
                if success:
                    self.logger.info(f"✅ 播放休眠提示音: {random_file}")
                else:
                    self.logger.warning(f"⚠️ 播放休眠提示音失败: {random_file}")
            else:
                self.logger.warning("⚠️ 未找到休眠提示音频文件")
        except Exception as e:
            self.logger.error(f"❌ 播放休眠提示音失败: {e}")
    
    def _start_asr_processing(self):
        """启动ASR处理"""
        self.logger.debug("启动ASR处理")
        # ASR处理逻辑将在音频处理线程中实现
    
    def _stop_asr_processing(self):
        """停止ASR处理"""
        self.logger.debug("停止ASR处理")
    
    def _process_with_llm(self, text: str):
        """使用LLM处理文本，支持同步/流式调用，便于后续扩展"""
        try:
            self.logger.debug(f"发送到LLM处理: {text}")
            # 调用LLM（同步阻塞，可扩展为流式/断句输出）
            response = self.llm_client.query_llm(text)
            if response:
                self._synthesize_and_play(response)
            else:
                self.logger.warning("LLM返回空响应")
        except Exception as e:
            self.logger.error(f"LLM处理失败: {e}")
    
    def _synthesize_and_play(self, text: str):
        """合成并播放语音"""
        try:
            self.logger.info(f"开始TTS合成: {text}")
            
            # 合成语音
            audio_data = self.tts_processor.synthesize(text)
            
            if audio_data is not None:
                # 播放语音
                self.audio_output.play(audio_data)
            else:
                self.logger.warning("TTS合成失败")
                
        except Exception as e:
            self.logger.error(f"TTS处理失败: {e}")
    
    def _audio_processing_loop(self):
        """音频处理主循环，唤醒和ASR转写buffer互不影响，日志分级优化"""
        self.logger.info("启动音频处理循环")
        kws_buffer = []  # 唤醒检测buffer
        asr_buffer = []  # ASR转写buffer
        sample_rate = self.config.get("audio.input.sample_rate", 16000)
        chunk_size = self.config.get("audio.input.chunk_size", 1024)
        kws_buffer_size = int(sample_rate * 1.5)  # 1.5秒缓冲区
        silence_threshold = 0.01
        silence_max_duration = 1.2
        last_sound_time = time.time()
        debug_mode = self.config.get("debug.enabled", False)
        self.is_wake_state = False
        while self.is_running:
            try:
                audio_chunk = self.audio_queue.get(timeout=0.1)
                # 先降噪
                audio_chunk = self.denoise_processor.process(audio_chunk)
                current_volume = np.max(np.abs(audio_chunk))
                now = time.time()
                if debug_mode and not self.is_wake_state:
                    self.logger.info("调试模式：自动触发唤醒事件")
                    self.event_bus.publish("wake_word_detected", {"is_wakeup": True, "score": 1.0})
                    self.is_wake_state = True
                if not self.is_wake_state:
                    kws_buffer.append(audio_chunk)
                    if len(kws_buffer) * chunk_size >= kws_buffer_size:
                        audio_array = np.concatenate(kws_buffer)
                        kws_result = self.kws_detector.detect(audio_array)
                        if debug_mode:
                            self.logger.debug(f"KWS检测: 能量={np.mean(np.abs(audio_array)):.4f}, 结果={kws_result}")
                        if kws_result.get("is_wakeup", False):
                            self.event_bus.publish("wake_word_detected", kws_result)
                            self.is_wake_state = True
                        kws_buffer = []
                else:
                    if current_volume > silence_threshold:
                        last_sound_time = now
                        asr_buffer.append(audio_chunk)
                    else:
                        asr_buffer.append(audio_chunk)
                        if now - last_sound_time >= silence_max_duration and asr_buffer:
                            audio = np.concatenate(asr_buffer)
                            # VAD判定
                            vad_result = self.vad_processor.process(audio)
                            has_speech = vad_result.get("has_speech")
                            
                            # VAD判定日志（仅debug模式显示详细信息）
                            if debug_mode:
                                energy = vad_result.get("energy", "N/A")
                                speech_prob = vad_result.get("speech_prob", "N/A") 
                                self.logger.debug(f"VAD判定: has_speech={has_speech}, energy={energy}, speech_prob={speech_prob}")
                            
                            if has_speech:
                                self.logger.info("🎤 检测到语音，发送到ASR处理")
                                self.asr_queue.put(audio)
                            else:
                                if debug_mode:
                                    self.logger.debug("🔇 VAD判定为非语音，丢弃音频片段")
                            asr_buffer = []
                            last_sound_time = now
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"音频处理错误: {e}")
    
    def _audio_input_loop(self):
        """音频输入循环"""
        self.logger.info("启动音频输入循环")
        
        try:
            # 使用PyAudio进行音频输入
            import pyaudio
            
            # 音频参数
            sample_rate = self.config.get("audio.input.sample_rate", 16000)
            chunk_size = self.config.get("audio.input.chunk_size", 1024)
            channels = self.config.get("audio.input.channels", 1)
            
            # 初始化PyAudio
            audio = pyaudio.PyAudio()
            
            # 打开音频流
            stream = audio.open(
                format=pyaudio.paFloat32,
                channels=channels,
                rate=sample_rate,
                input=True,
                frames_per_buffer=chunk_size
            )
            
            self.logger.info("✅ 音频输入启动成功")
            
            # 音频处理循环
            while self.is_running:
                try:
                    # 读取音频数据
                    audio_data = stream.read(chunk_size, exception_on_overflow=False)
                    audio_array = np.frombuffer(audio_data, dtype=np.float32)
                    
                    # 将音频数据放入队列
                    self.audio_queue.put(audio_array)
                    
                except Exception as e:
                    self.logger.error(f"音频输入错误: {e}")
                    break
            
            # 清理资源
            stream.stop_stream()
            stream.close()
            audio.terminate()
            
        except Exception as e:
            self.logger.error(f"音频输入启动失败: {e}")
    
    def _asr_processing_loop(self):
        """ASR处理循环，输出格式同步自动化测试，仅输出[user] : 文本，日志分级优化"""
        self.logger.info("启动ASR处理循环")
        debug_mode = self.config.get("debug.enabled", False)
        while self.is_running:
            try:
                audio_data = self.asr_queue.get(timeout=0.1)
                asr_result = self.asr_processor.recognize(audio_data)
                if not isinstance(asr_result, dict):
                    asr_result = {"text": ""}
                text = asr_result.get("text", "")
                if text:
                    print(text)
                if debug_mode:
                    self.logger.debug(f"ASR事件发布: {asr_result}")
                self.event_bus.publish("asr_result", asr_result)
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"ASR处理错误: {e}")
    
    def _tts_processing_loop(self):
        """TTS处理循环，标准化事件发布"""
        self.logger.info("启动TTS处理循环")
        while self.is_running:
            try:
                tts_task = self.tts_queue.get(timeout=0.1)
                audio_data = self.tts_processor.synthesize(tts_task)
                if audio_data is not None:
                    self.audio_output.play(audio_data)
                    self.event_bus.publish("tts_completed", {"text": tts_task})
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"TTS处理错误: {e}")
    
    def _health_check_loop(self):
        """健康检查循环"""
        self.logger.info("启动健康检查循环")
        
        while self.is_running:
            try:
                # 执行健康检查
                health_status = self.health_checker.check_all_modules()
                
                if not health_status.get("overall_healthy", True):
                    self.logger.warning("检测到模块健康问题")
                    # 这里可以添加自动恢复逻辑
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                self.logger.error(f"健康检查错误: {e}")
    
    def start(self):
        """启动语音交互系统"""
        self.logger.info("启动aibi语音交互系统")
        
        if self.is_running:
            self.logger.warning("系统已在运行中")
            return
        
        self.is_running = True
        
        try:
            # 启动各个处理线程
            self.audio_thread = threading.Thread(target=self._audio_input_loop, daemon=True)
            self.vad_thread = threading.Thread(target=self._audio_processing_loop, daemon=True)
            self.asr_thread = threading.Thread(target=self._asr_processing_loop, daemon=True)
            self.tts_thread = threading.Thread(target=self._tts_processing_loop, daemon=True)
            self.health_thread = threading.Thread(target=self._health_check_loop, daemon=True)
            
            self.audio_thread.start()
            self.vad_thread.start()
            self.asr_thread.start()
            self.tts_thread.start()
            self.health_thread.start()
            
            self.logger.info("所有处理线程已启动")
            
            # 主循环
            while self.is_running:
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在关闭系统...")
        except Exception as e:
            self.logger.error(f"系统运行错误: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止语音交互系统"""
        self.logger.info("停止aibi语音交互系统")
        
        self.is_running = False
        
        # 等待线程结束
        if self.audio_thread:
            self.audio_thread.join(timeout=5)
        if self.vad_thread:
            self.vad_thread.join(timeout=5)
        if self.asr_thread:
            self.asr_thread.join(timeout=5)
        if self.tts_thread:
            self.tts_thread.join(timeout=5)
        if self.health_thread:
            self.health_thread.join(timeout=5)
        
        self.logger.info("系统已停止")

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n收到退出信号，正在关闭系统...")
    sys.exit(0)

def main():
    """主函数"""
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("🚀 aibi语音交互系统")
    print("=" * 40)
    
    try:
        # 创建并启动系统
        system = AibiVoiceSystem()
        system.start()
        
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 