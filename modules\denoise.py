import numpy as np
from typing import Optional

class DenoiseProcessor:
    """
    流式降噪处理器，支持FunASR官方降噪模型
    """
    def __init__(self, config):
        self.config = config
        self.enable = config.get("denoise.enable", True)
        self.model_path = config.get("denoise.model_path", "./models/denoise/denoiser.onnx")
        self.logger = None
        try:
            import funasr
            self.funasr = funasr
            self.denoise_model = funasr.AutoModel(model=self.model_path)
            self.logger = self.logger or print
            self.logger(f"降噪模型加载成功: {self.model_path}")
            self.use_denoise = True
        except Exception as e:
            self.logger = self.logger or print
            self.logger(f"降噪模型加载失败，降级为直通: {e}")
            self.use_denoise = False

    def process(self, audio_data: np.ndarray) -> np.ndarray:
        """
        对输入音频进行降噪处理
        """
        try:
            if self.enable and self.use_denoise:
                result = self.denoise_model.generate(input=audio_data)
                denoised = result.get('denoised_audio', audio_data)
                return denoised
            else:
                return audio_data
        except Exception as e:
            if self.logger:
                self.logger(f"降噪处理异常: {e}")
            return audio_data 