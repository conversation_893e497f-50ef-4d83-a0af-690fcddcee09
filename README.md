# aibi语音交互系统

## 项目简介

aibi语音交互系统是一套端到端本地部署的智能语音交互解决方案，适用于PC和嵌入式平台。系统以FunASR为主线，集成唤醒词检测、端点检测、降噪、语音识别、TTS合成、插话中断、自动休眠等功能，支持多硬件（CPU/NPU/GPU）自动适配。LLM能力通过Dify服务器对接，系统专为低延迟、高鲁棒性、易扩展设计，适合智能助手、机器人等实时语音场景。

---

## 开发进度

### ✅ 已完成模块（95%）
- **核心功能**：音频采集、VAD端点检测、降噪处理、唤醒词检测、ASR语音识别（FunASR+SenseVoiceSmall）、TTS语音合成（CosyVoice2-0.5B）、LLM对接（Dify API）、音频播放
- **系统管理**：配置管理、日志监控、健康检查、硬件适配
- **交互控制**：插话/中断、自动退出唤醒、事件驱动架构
- **集成测试**：唤醒词功能验证、TTS模块集成、系统集成完成

### ⚠️ 需要完善模块（5%）
- **状态管理**：实现过于简单，需要完善状态机
- **事件总线**：功能基础，需要增强事件处理能力
- **插话中断**：逻辑简单，需要优化中断处理

### 📋 待添加模块
- 性能监控模块、缓存管理模块、插件管理模块、多语言管理模块

---

## 安装与部署

### 1. 环境准备
- Python >= 3.10
- 推荐使用conda环境

### 2. 安装依赖
- 请根据实际硬件环境选择 requirements-cpu.txt、requirements-gpu.txt 或 requirements-npu.txt 安装依赖：
```bash
conda create -n funasr-aibi python=3.10
conda activate funasr-aibi
pip install -r requirements-cpu.txt  # 或 requirements-gpu.txt / requirements-npu.txt
# 安装FunASR及相关模型
```

### 3. 配置参数
- 编辑`config.yaml`，设置唤醒词、超时时间、硬件后端、Dify API等参数

---

## 使用方法

### LLM对接说明
- 主程序自动通过事件驱动调用llm_client.py，支持Dify API同步/流式、重试、连接池、缓存、日志分级、按句断流等能力。
- 可扩展为流式TTS、多轮对话、界面分句显示等高级功能。

### TTS语音合成说明
- 系统集成了CosyVoice2-0.5B模型，支持高质量中文语音合成
- 自动下载模型文件，支持备用静音方案确保系统稳定性
- 支持实时语音合成和音频播放，延迟优化至实时交互水平

### 启动主程序
```bash
conda activate funasr-aibi
python main.py
```

### 查看日志
```bash
tail -f logs/aibi.log
```

### 单元测试
```bash
pytest tests/
```

### 性能测试
```bash
python tools/benchmark.py
```

### 配置验证
```bash
python tools/config_validator.py
```

### 配置使用示例
```bash
python examples/config_usage_example.py
```

### TTS模块测试
```bash
# 测试TTS模块基础功能
python tools/test_tts_module.py

# 测试TTS集成功能
python tools/test_tts_integration.py

# 下载CosyVoice模型（如需要）
python tools/download_cosyvoice_models.py
```

---

## 主要参数说明

### 基础配置
| 参数名         | 说明                         | 默认值         |
|----------------|------------------------------|----------------|
| wake_word      | 唤醒词                       | Hey,艾比       |
| wake_word_threshold | 唤醒词检测阈值 (0-1)      | 0.85           |
| interaction_timeout | 交互超时时间（秒）        | 10             |
| auto_exit_commands | 自动退出唤醒指令列表      | ["结束", "停止", "休眠", "再见"] |

### 音频配置
| 参数名         | 说明                         | 默认值         |
|----------------|------------------------------|----------------|
| audio.input.sample_rate | 音频输入采样率           | 16000          |
| audio.input.channels | 音频输入声道数             | 1              |
| audio.input.chunk_size | 音频块大小               | 1024           |
| audio.vad.threshold | VAD检测阈值 (0-1)         | 0.5            |
| audio.vad.frame_duration_ms | VAD帧长度(毫秒)    | 30             |

### 硬件配置
| 参数名         | 说明                         | 默认值         |
|----------------|------------------------------|----------------|
| hardware.device | 推理硬件（cpu/gpu/npu/auto） | auto           |
| hardware.gpu_memory_fraction | GPU内存使用比例    | 0.8            |
| hardware.num_threads | CPU线程数                | 4              |

### LLM配置
| 参数名         | 说明                         | 默认值         |
|----------------|------------------------------|----------------|
| llm.dify_api_url | Dify服务器API地址         | http://11.20.60.13/v1 |
| llm.dify_api_key | Dify API密钥              | app-sZWbbT6JuOnSMWRPa3LzWF4Z |
| llm.max_tokens | 最大输出token数            | 2048           |
| llm.temperature | 采样温度                   | 0.7            |

### 插话中断配置
| 参数名         | 说明                         | 默认值         |
|----------------|------------------------------|----------------|
| interrupt.enabled | 是否启用插话功能          | true           |
| interrupt.min_interrupt_duration_ms | 最小插话持续时间(毫秒) | 500 |
| interrupt.require_wake_state | 是否要求已唤醒状态    | true           |

### 日志配置
| 参数名         | 说明                         | 默认值         |
|----------------|------------------------------|----------------|
| logging.level  | 日志级别                     | info           |
| logging.file_enabled | 是否启用文件日志        | true           |
| logging.log_file | 日志文件路径               | ./logs/aibi.log |

---

## 目录结构

```
aibi/
├── main.py
├── config.yaml
├── requirements-cpu.txt
├── requirements-gpu.txt
├── requirements-npu.txt
├── modules/
│   ├── audio_input.py          # ✅ 音频采集
│   ├── vad.py                  # ✅ VAD端点检测（FSMN-VAD）
│   ├── denoise.py              # ✅ 降噪处理（FunASR）
│   ├── kws.py                  # ✅ 唤醒词检测
│   ├── asr.py                  # ✅ 语音识别（FunASR+SenseVoiceSmall）
│   ├── tts.py                  # ⚠️ 语音合成（需完善cosyvoice）
│   ├── llm_client.py           # ✅ LLM对接（Dify API）
│   ├── state_manager.py        # ⚠️ 状态管理（需完善）
│   ├── interrupt.py            # ⚠️ 插话中断（需完善）
│   ├── auto_exit.py            # ✅ 自动退出
│   ├── event_bus.py            # ⚠️ 事件总线（需完善）
│   ├── audio_output.py         # ✅ 音频播放
│   ├── hardware_adapter.py     # ✅ 硬件适配
│   ├── config_manager.py       # ✅ 配置管理
│   ├── health_check.py         # ✅ 健康检查
│   └── logging_utils.py        # ✅ 日志监控
├── tests/
├── tools/
├── logs/
├── README.md
├── 架构设计说明.md
├── 开发文档.md
└── 任务清单.md
```

---

## 相关文档

- [架构设计说明.md](./架构设计说明.md)：包含系统架构图、核心模块设计、状态流转、插话/中断与自动退出唤醒判定逻辑、硬件适配与性能优化等详细设计说明。
- [开发文档.md](./开发文档.md)：详细介绍系统开发流程、模块分工、参数配置、测试方法等，适合开发者查阅。
- [任务清单.md](./任务清单.md)：开发计划与任务进度追踪，便于团队协作和进度管理。

---

## 插件化Matcha-TTS（CosyVoice2）集成说明

- 本项目支持通过插件机制集成第三方TTS（如CosyVoice2/Matcha-TTS）。
- 请将Matcha-TTS源码放入 plugins/matcha_tts 目录（或自定义插件目录），系统自动通过插件加载。
- 插件目录结构示例：
  ```
  plugins/
    matcha_tts/
      matcha/
        models/
          matcha_tts.py
        ...
      ...
  ```
- 插件加载后，TTS模块会自动import插件内的MatchaTTS类进行本地推理。
- 依赖安装：请根据硬件环境选择 requirements-cpu.txt、requirements-gpu.txt、requirements-npu.txt 安装依赖。

---

## 常见问题与反馈

- 所有代码/配置变更请记录在《修改问题反馈记录.md》
- 用户反馈的问题请记录在《待修复问题汇总.md》，修复后由用户确认

---

## 维护与扩展建议

- 所有模块均有标准接口，便于升级/替换
- 支持多线程/异步处理，提升并发与吞吐
- 预留接口，便于后续扩展（如多麦克风阵列、声源定位等）
- 定期进行压力测试和健康检查，保证系统长期稳定运行

---

如需进一步细化某一模块的开发细节、接口定义或示例代码，请随时告知！
如需先输出某模块详细设计，也请直接说明。 