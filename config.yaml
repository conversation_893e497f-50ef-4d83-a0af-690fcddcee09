# ============================================================================
# aibi语音交互系统配置文件
# 版本: v2.0
# 修改后请重启系统以生效
# ============================================================================

# ==================== 系统基础配置 ====================
system:
  name: "aibi"                                    # 系统名称
  version: "2.0.0"                               # 系统版本
  environment: "production"                       # 运行环境: development/production

  # 交互配置
  interaction:
    timeout: 10                                   # 交互超时时间(秒)
    auto_exit_commands: ["结束", "停止", "休眠", "再见"]  # 自动退出唤醒的指令
    max_retry_attempts: 3                         # 最大重试次数

# ==================== 音频处理配置 ====================
audio:
  # 音频输入设备配置
  input:
    sample_rate: 16000                            # 采样率(Hz)
    channels: 1                                   # 声道数(单声道)
    chunk_size: 1024                              # 音频块大小(采样点)
    device_index: 0                               # 麦克风设备索引(-1为自动选择)
    buffer_duration_ms: 100                       # 缓冲区时长(毫秒)

  # 音频输出设备配置
  output:
    sample_rate: 16000                            # 输出采样率(Hz)
    channels: 1                                   # 输出声道数(单声道)
    device_index: 0                               # 扬声器设备索引(-1为自动选择)
    volume: 1.0                                   # 输出音量(0.0-2.0)

  # 音频处理配置
  processing:
    enable_denoise: true                          # 是否启用降噪
    enable_vad: true                              # 是否启用语音活动检测
    enable_agc: false                             # 是否启用自动增益控制

# ==================== AI模型配置 ====================
models:
  # 模型存储基础路径
  base_path: "./models"                           # 所有模型的根目录
  plugins_path: "./plugins"                       # 插件模型路径

  # 唤醒词检测模型(KWS)
  wake_word:
    enabled: true                                 # 是否启用唤醒词检测
    model_path: "./models/cnn/hey_aibi.onnx"     # 模型文件路径
    model_type: "onnx"                           # 模型类型: onnx/pytorch
    model_info: "./models/cnn/model_info.txt"    # 模型信息文件
    threshold: 0.85                              # 检测阈值(0.0-1.0)
    timeout: 3.0                                 # 检测超时时间(秒)
    wake_word: "Hey,艾比"                        # 唤醒词文本

  # 语音活动检测模型(VAD)
  vad:
    enabled: true                                 # 是否启用VAD
    model_path: "./models/vad/damo/speech_fsmn_vad_zh-cn-16k-common-onnx"
    threshold: 0.002                             # VAD检测阈值
    frame_duration_ms: 30                        # 帧长度(毫秒)
    min_speech_duration_ms: 250                  # 最小语音时长(毫秒)
    max_speech_duration_s: 30                    # 最大语音时长(秒)
    speech_pad_ms: 400                           # 语音前后填充(毫秒)

  # 语音降噪模型
  denoise:
    enabled: true                                 # 是否启用降噪
    model_path: "./models/denoise/damo/speech_frcrn_ans_cirm_16k"

  # 语音识别模型(ASR)
  asr:
    enabled: true                                 # 是否启用ASR
    model_path: "./models/asr"                   # 模型路径
    model_name: "SenseVoice/SenseVoiceSmall"     # 模型名称
    model_revision: "v1.0.0"                     # 模型版本
    language: "zh"                               # 识别语言
    beam_size: 5                                 # 束搜索大小
    max_length: 512                              # 最大识别长度
    temperature: 0.0                             # 采样温度
    enable_streaming: true                       # 是否启用流式识别
    recognition_timeout: 3.0                     # 识别超时时间(秒)

  # 语音合成模型(TTS)
  tts:
    enabled: true                                 # 是否启用TTS
    model_path: "./models/tts"                   # 模型路径
    model_type: "cosyvoice_300m"                 # 模型类型: cosyvoice_300m/cosyvoice2
    enable_zero_shot: true                       # 是否启用zero-shot合成
    default_speaker: "中文女"                     # 默认说话人
    speed: 1.0                                   # 语速(0.5-2.0)
    volume: 1.0                                  # 音量(0.0-2.0)
    pitch: 1.0                                   # 音调(0.5-2.0)
    enable_streaming: false                      # 是否启用流式合成
    synthesis_timeout: 10.0                      # 合成超时时间(秒)
    max_text_length: 500                         # 最大文本长度
  
# ==================== 硬件运行环境配置 ====================
hardware:
  # 计算设备配置
  compute:
    device: "auto"                                # 推理设备: cpu/gpu/npu/auto
    gpu_memory_fraction: 0.8                     # GPU内存使用比例(0.1-1.0)
    cpu_threads: 4                               # CPU线程数

  # 性能优化配置
  optimization:
    enable_fp16: false                           # 是否启用FP16半精度推理
    enable_int8: false                           # 是否启用INT8量化推理
    enable_model_cache: true                     # 是否启用模型缓存
    batch_size: 1                                # 批处理大小

  # 内存管理配置
  memory:
    max_memory_usage_gb: 8                       # 最大内存使用量(GB)
    enable_memory_pool: true                     # 是否启用内存池
    gc_threshold: 0.8                            # 垃圾回收阈值

# ==================== 大语言模型配置 ====================
llm:
  # 服务提供商配置
  provider:
    type: "dify"                                 # 服务类型: dify/openai/custom
    api_url: "http://11.20.60.13/v1"            # API服务地址
    api_key: "app-sZWbbT6JuOnSMWRPa3LzWF4Z"     # API密钥
    timeout: 30                                  # 请求超时时间(秒)

  # 生成参数配置
  generation:
    max_tokens: 2048                             # 最大输出token数
    temperature: 0.7                             # 采样温度(0.0-2.0)
    top_p: 0.9                                   # 核采样参数(0.0-1.0)
    frequency_penalty: 0.0                       # 频率惩罚(-2.0-2.0)
    presence_penalty: 0.0                        # 存在惩罚(-2.0-2.0)

  # 对话管理配置
  conversation:
    system_prompt: "你是一个智能语音助手，请用简洁友好的方式回答用户问题。"
    max_history: 10                              # 最大对话历史长度
    enable_context: true                         # 是否启用上下文记忆
    context_window: 4096                         # 上下文窗口大小

# ==================== 语音中断处理配置 ====================
interrupt:
  # 中断检测配置
  detection:
    enabled: true                                # 是否启用语音中断功能
    min_duration_ms: 500                        # 最小中断持续时间(毫秒)
    threshold: 0.6                               # 中断检测阈值(0.0-1.0)

  # 中断触发条件
  conditions:
    require_wake_state: true                     # 是否要求系统处于已唤醒状态
    require_processing: true                     # 是否要求正在处理语音/TTS时才能中断

  # 中断响应配置
  response:
    immediate_response: true                     # 是否立即响应中断
    cancel_current_task: true                    # 是否取消当前任务
    fade_out_duration_ms: 200                   # 音频淡出时长(毫秒)

# ==================== 日志记录配置 ====================
logging:
  # 全局日志级别
  global:
    level: "info"                                # 日志级别: debug/info/warning/error/critical
    timezone: "Asia/Shanghai"                    # 时区设置

  # 控制台日志配置
  console:
    enabled: true                                # 是否启用控制台输出
    level: "info"                                # 控制台日志级别
    format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
    color_enabled: true                          # 是否启用彩色输出

  # 文件日志配置
  file:
    enabled: true                                # 是否启用文件日志
    path: "./logs/aibi.log"                     # 日志文件路径
    level: "debug"                               # 文件日志级别
    max_size_mb: 100                             # 单个日志文件最大大小(MB)
    backup_count: 5                              # 保留的日志文件数量
    format: "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(message)s"

  # 性能日志配置
  performance:
    enabled: true                                # 是否启用性能日志
    log_latency: true                            # 是否记录延迟信息
    log_memory_usage: false                      # 是否记录内存使用
    log_model_inference: false                   # 是否记录模型推理时间

# ==================== 系统监控配置 ====================
monitoring:
  # 健康检查配置
  health_check:
    enabled: true                                # 是否启用健康检查
    interval_seconds: 30                         # 检查间隔(秒)
    enable_auto_restart: true                    # 是否启用自动重启
    max_restart_attempts: 3                      # 最大重启尝试次数

  # 性能监控配置
  performance:
    enabled: true                                # 是否启用性能监控
    interval_seconds: 10                         # 监控间隔(秒)

  # 资源监控配置
  resources:
    monitor_cpu: true                            # 是否监控CPU使用率
    monitor_memory: true                         # 是否监控内存使用率
    monitor_gpu: true                            # 是否监控GPU使用率
    monitor_disk: false                          # 是否监控磁盘使用率

# ==================== 多语言支持配置 ====================
language:
  # 默认语言设置
  default: "zh-CN"                               # 默认语言代码

  # 支持的语言列表
  supported_languages:
    - code: "zh-CN"                              # 简体中文
      name: "简体中文"
      wake_word: "Hey,艾比"
    - code: "en-US"                              # 美式英语
      name: "English (US)"
      wake_word: "Hey, aibi"
    - code: "ja-JP"                              # 日语
      name: "日本語"
      wake_word: "Hey, aibi"

# ==================== 开发调试配置 ====================
debug:
  # 调试模式开关
  enabled: false                                 # 是否启用调试模式

  # 音频调试配置
  audio:
    save_enabled: false                          # 是否保存调试音频
    save_path: "./debug/audio"                   # 音频保存路径
    save_format: "wav"                           # 保存格式: wav/mp3

  # 日志调试配置
  logging:
    verbose_enabled: false                       # 是否启用详细日志
    log_audio_levels: false                      # 是否记录音频电平
    log_model_outputs: false                     # 是否记录模型输出
    log_timing: false                            # 是否记录时序信息

# ==================== 安全与隐私配置 ====================
security:
  # API安全配置
  api:
    enable_authentication: false                 # 是否启用API认证
    api_key: ""                                  # API密钥
    rate_limit_enabled: false                    # 是否启用速率限制

  # 数据安全配置
  data:
    encrypt_audio: false                         # 是否加密音频数据
    secure_logging: false                        # 是否安全记录日志(不记录敏感信息)
    auto_delete_audio: true                      # 是否自动删除临时音频文件
    retention_days: 7                            # 数据保留天数

# ==================== 插件扩展配置 ====================
plugins:
  # 插件系统配置
  system:
    enabled: true                                # 是否启用插件系统
    plugins_path: "./plugins"                    # 插件根目录
    auto_load: true                              # 是否自动加载插件

  # TTS插件配置
  tts:
    cosyvoice_path: "./plugins/tts/CosyVoice"   # CosyVoice插件路径

  # 自定义功能配置
  custom:
    functions: []                                # 自定义功能列表
    hooks: []                                    # 自定义钩子列表

