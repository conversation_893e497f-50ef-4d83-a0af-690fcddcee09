#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - SenseVoiceSmall模型下载脚本
使用ModelScope下载SenseVoiceSmall模型到modules/asr目录
"""

import os
import sys
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def download_sensevoice_model():
    """下载SenseVoiceSmall模型"""
    print("🚀 开始下载SenseVoiceSmall模型...")
    
    try:
        # 导入ModelScope
        from modelscope import snapshot_download
        
        # 模型信息
        model_name = "iic/SenseVoiceSmall"
        model_revision = "v1.0.0"
        target_dir = "./models/asr"
        
        print(f"📋 模型信息:")
        print(f"  模型名称: {model_name}")
        print(f"  模型版本: {model_revision}")
        print(f"  目标目录: {target_dir}")
        
        # 确保目标目录存在
        os.makedirs(target_dir, exist_ok=True)
        
        # 下载模型
        print(f"\n📥 正在下载模型...")
        model_dir = snapshot_download(
            model_name,
            cache_dir=target_dir
        )
        
        print(f"✅ 模型下载完成!")
        print(f"  模型路径: {model_dir}")
        
        # 检查下载的文件
        if os.path.exists(model_dir):
            print(f"\n📁 模型文件列表:")
            for root, dirs, files in os.walk(model_dir):
                level = root.replace(model_dir, '').count(os.sep)
                indent = ' ' * 2 * level
                print(f"{indent}{os.path.basename(root)}/")
                subindent = ' ' * 2 * (level + 1)
                for file in files:
                    print(f"{subindent}{file}")
        
        # 创建模型信息文件
        model_info_file = os.path.join(target_dir, "model_info.txt")
        with open(model_info_file, "w", encoding="utf-8") as f:
            f.write(f"模型名称: {model_name}\n")
            f.write(f"模型版本: {model_revision}\n")
            f.write(f"模型路径: {model_dir}\n")
            f.write(f"模型类型: SenseVoiceSmall\n")
            f.write(f"支持语言: 中文\n")
            f.write(f"采样率: 16000Hz\n")
            f.write(f"音频格式: float32\n")
            f.write(f"最大长度: 30秒\n")
        
        print(f"\n📝 模型信息文件已创建: {model_info_file}")
        
        # 更新配置文件
        update_config_file(model_dir)
        
        print(f"\n🎉 SenseVoiceSmall模型下载完成!")
        print(f"  模型已保存到: {model_dir}")
        print(f"  可在ASR模块中使用")
        
        return True
        
    except ImportError:
        print("❌ 错误: 未安装ModelScope")
        print("请运行: pip install modelscope")
        return False
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def update_config_file(model_dir):
    """更新配置文件中的模型路径"""
    try:
        config_file = "config.yaml"
        if os.path.exists(config_file):
            print(f"\n🔧 更新配置文件: {config_file}")
            
            # 读取配置文件
            with open(config_file, "r", encoding="utf-8") as f:
                content = f.read()
            
            # 更新ASR模型路径
            if "asr_model_path:" in content:
                # 替换现有的模型路径
                content = content.replace(
                    "asr_model_path: \"./models/asr\"",
                    f"asr_model_path: \"{model_dir}\""
                )
            else:
                # 在models部分添加模型路径
                if "models:" in content:
                    content = content.replace(
                        "models:",
                        f"models:\n  asr_model_path: \"{model_dir}\""
                    )
            
            # 写回配置文件
            with open(config_file, "w", encoding="utf-8") as f:
                f.write(content)
            
            print(f"✅ 配置文件已更新")
        
    except Exception as e:
        print(f"⚠️ 配置文件更新失败: {e}")

def verify_model_files(model_dir):
    """验证模型文件"""
    print(f"\n🔍 验证模型文件...")
    
    required_files = [
        "config.json",
        "pytorch_model.bin",
        "tokenizer.json",
        "tokenizer_config.json"
    ]
    
    missing_files = []
    for file in required_files:
        file_path = os.path.join(model_dir, file)
        if os.path.exists(file_path):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (缺失)")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ 警告: 以下文件缺失:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    else:
        print(f"\n✅ 所有必需文件都存在")
        return True

def test_model_loading():
    """测试模型加载"""
    print(f"\n🧪 测试模型加载...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        # 获取模型路径
        config_file = "config.yaml"
        model_dir = "./modules/asr"  # 默认路径
        
        if os.path.exists(config_file):
            import yaml
            with open(config_file, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
                if "models" in config and "asr_model_path" in config["models"]:
                    model_dir = config["models"]["asr_model_path"]
        
        # 检查模型目录是否存在
        if not os.path.exists(model_dir):
            print(f"❌ 模型目录不存在: {model_dir}")
            return False
        
        # 尝试加载tokenizer
        print(f"  正在加载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_dir)
        print(f"  ✅ tokenizer加载成功")
        
        # 尝试加载模型
        print(f"  正在加载模型...")
        model = AutoModelForCausalLM.from_pretrained(
            model_dir,
            device_map="auto",
            torch_dtype="auto"
        )
        print(f"  ✅ 模型加载成功")
        
        print(f"\n✅ 模型加载测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 模型加载测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 aibi语音交互系统 - SenseVoiceSmall模型下载")
    print("=" * 60)
    
    # 检查ModelScope是否安装
    try:
        import modelscope
        print(f"✅ ModelScope已安装: {modelscope.__version__}")
    except ImportError:
        print("❌ ModelScope未安装")
        print("请先安装ModelScope:")
        print("  pip install modelscope")
        return
    
    # 下载模型
    success = download_sensevoice_model()
    
    if success:
        # 验证模型文件
        model_dir = "./modules/asr"
        verify_model_files(model_dir)
        
        # 测试模型加载
        test_model_loading()
        
        print(f"\n🎯 模型下载和验证完成!")
        print(f"现在可以在ASR模块中使用SenseVoiceSmall模型了")
    else:
        print(f"\n❌ 模型下载失败，请检查网络连接和ModelScope安装")

if __name__ == "__main__":
    main() 