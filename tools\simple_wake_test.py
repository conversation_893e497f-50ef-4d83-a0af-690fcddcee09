#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - 简化唤醒词测试
用于快速验证唤醒词检测功能
"""

import os
import sys
import time
import numpy as np
import pyaudio
import queue
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from modules.kws import WakeWordDetector

def simple_wake_test():
    """简化的唤醒词测试"""
    print("🚀 aibi语音交互系统 - 简化唤醒词测试")
    print("=" * 40)
    
    try:
        # 加载检测器
        print("正在加载唤醒词检测器...")
        detector = WakeWordDetector(
            model_type="onnx",
            model_path="./models/cnn/hey_aibi.onnx",
            model_info_path="./models/cnn/model_info.txt",
            device="cpu"
        )
        print("✅ 检测器加载成功")
        
        # 设置音频参数
        sample_rate = 16000
        chunk_size = 1024
        channels = 1
        format_type = pyaudio.paFloat32
        
        # 初始化音频
        audio = pyaudio.PyAudio()
        audio_queue = queue.Queue()
        is_recording = False
        
        def audio_callback(in_data, frame_count, time_info, status):
            if is_recording:
                audio_data = np.frombuffer(in_data, dtype=np.float32)
                audio_queue.put(audio_data)
            return (None, pyaudio.paContinue)
        
        # 启动音频流
        stream = audio.open(
            format=format_type,
            channels=channels,
            rate=sample_rate,
            input=True,
            frames_per_buffer=chunk_size,
            stream_callback=audio_callback
        )
        stream.start_stream()
        print("✅ 音频流启动成功")
        
        # 开始测试
        print(f"\n🔍 开始唤醒词检测测试")
        print("请说出唤醒词 'Hey, 艾比' 进行测试...")
        print("按 Ctrl+C 结束测试\n")
        
        is_recording = True
        audio_buffer = []
        detection_count = 0
        test_duration = 20  # 20秒测试
        start_time = time.time()
        
        try:
            while time.time() - start_time < test_duration:
                try:
                    # 获取音频数据
                    audio_data = audio_queue.get(timeout=0.1)
                    audio_buffer.extend(audio_data)
                    
                    # 当缓冲区达到1.5秒时进行检测
                    if len(audio_buffer) >= sample_rate * 1.5:
                        # 转换为numpy数组
                        audio_array = np.array(audio_buffer, dtype=np.float32)
                        
                        # 进行唤醒词检测
                        result = detector.detect(audio_array)
                        
                        # 输出检测结果
                        timestamp = time.time() - start_time
                        if result['is_wakeup']:
                            print(f"[{timestamp:.1f}s] 🔔 检测到唤醒词! 分数: {result['score']:.3f}")
                            detection_count += 1
                        else:
                            print(f"[{timestamp:.1f}s] 👂 监听中... 分数: {result['score']:.3f}")
                        
                        # 清空缓冲区，保留最后0.3秒
                        keep_samples = int(sample_rate * 0.3)
                        audio_buffer = audio_buffer[-keep_samples:]
                        
                except queue.Empty:
                    continue
                    
        except KeyboardInterrupt:
            print("\n⏹️ 测试被用户中断")
        
        finally:
            is_recording = False
            stream.stop_stream()
            stream.close()
            audio.terminate()
        
        # 输出测试结果
        print(f"\n📊 测试结果:")
        print(f"检测到唤醒词: {detection_count} 次")
        
        if detection_count > 0:
            print("🎉 唤醒词检测功能正常工作!")
        else:
            print("⚠️ 未检测到唤醒词")
            print("建议:")
            print("  - 确保麦克风正常工作")
            print("  - 清晰说出 'Hey, 艾比'")
            print("  - 检查环境噪音")
            print("  - 可能需要调整模型阈值")
        
        return detection_count > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_threshold_adjustment():
    """测试不同阈值的效果"""
    print("\n🔍 测试不同阈值的效果...")
    
    try:
        detector = WakeWordDetector(
            model_type="onnx",
            model_path="./models/cnn/hey_aibi.onnx",
            model_info_path="./models/cnn/model_info.txt",
            device="cpu"
        )
        
        # 创建测试音频（简单的正弦波）
        sample_rate = 16000
        duration = 2.0
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        test_audio = 0.3 * np.sin(2 * np.pi * 440 * t) + 0.2 * np.sin(2 * np.pi * 880 * t)
        test_audio = test_audio.astype(np.float32)
        
        # 测试不同阈值
        thresholds = [0.5, 0.6, 0.7, 0.8, 0.85, 0.9, 0.95]
        
        print("不同阈值下的检测结果:")
        for threshold in thresholds:
            # 临时修改阈值
            original_threshold = detector.model_info.get("推荐阈值：", "0.85")
            detector.model_info["推荐阈值："] = str(threshold)
            
            result = detector.detect(test_audio)
            status = "🔔 唤醒" if result['is_wakeup'] else "👂 未唤醒"
            print(f"  阈值 {threshold}: {status} (分数: {result['score']:.3f})")
            
            # 恢复原阈值
            detector.model_info["推荐阈值："] = original_threshold
        
    except Exception as e:
        print(f"❌ 阈值测试失败: {e}")

if __name__ == "__main__":
    # 运行简化测试
    success = simple_wake_test()
    
    # 如果简化测试失败，进行阈值测试
    if not success:
        test_threshold_adjustment() 