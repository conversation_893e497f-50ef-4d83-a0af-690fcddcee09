模型名称：hey_aibi.onnx
模型类型：唤醒词检测（Hey, 艾比）
导出格式：ONNX
导出时间：2025-07-14

【模型输入要求】
- 采样率：16kHz
- 输入特征：MFCC（梅尔频率倒谱系数）
- MFCC维数：40
- 窗长（win_len_ms）：25ms
- 帧移（hop_len_ms）：10ms
- 输入shape：1 x 1 x 100 x 40（batch, channel, time, feature）
- 输入数据类型：float32

【模型输出】
- 输出shape：1 x 1
- 输出类型：float32
- 输出含义：唤醒概率（0~1，越接近1越可能为唤醒词）

【唤醒判决建议】
- 推荐阈值：0.85
- 输出大于阈值时判定为“唤醒”

【注意事项】
- 输入特征需与训练时一致，否则推理结果不可靠。
- 推荐使用ONNX Runtime进行推理。
- 如需详细特征提取流程，请联系模型提供方。 