#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - 唤醒词功能验证脚本
用于验证唤醒词检测功能的正确性和性能
"""

import os
import sys
import time
import yaml
import numpy as np
import soundfile as sf
import pyaudio
import threading
import queue
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from modules.kws import WakeWordDetector
from modules.config_manager import ConfigManager

class WakeWordTester:
    """唤醒词功能验证器"""
    
    def __init__(self, config_path="config.yaml"):
        """初始化验证器"""
        self.config = ConfigManager(config_path)
        self.detector = None
        self.audio_queue = queue.Queue()
        self.is_recording = False
        self.test_results = []
        
        # 音频参数
        self.sample_rate = 16000
        self.chunk_size = 1024
        self.channels = 1
        self.format = pyaudio.paFloat32
        
        # 初始化音频
        self.audio = pyaudio.PyAudio()
        
    def load_detector(self):
        """加载唤醒词检测器"""
        try:
            # 从配置文件获取模型路径
            model_type = self.config.get("kws_model_type", "onnx")
            model_path = self.config.get("kws_model_path", "./models/cnn/hey_aibi.onnx")
            model_info_path = self.config.get("kws_model_info", "./models/cnn/model_info.txt")
            
            print(f"正在加载唤醒词检测器...")
            print(f"模型类型: {model_type}")
            print(f"模型路径: {model_path}")
            print(f"模型信息: {model_info_path}")
            
            self.detector = WakeWordDetector(
                model_type=model_type,
                model_path=model_path,
                model_info_path=model_info_path,
                device="cpu"
            )
            
            print("✅ 唤醒词检测器加载成功")
            return True
            
        except Exception as e:
            print(f"❌ 唤醒词检测器加载失败: {e}")
            return False
    
    def audio_callback(self, in_data, frame_count, time_info, status):
        """音频回调函数"""
        if self.is_recording:
            audio_data = np.frombuffer(in_data, dtype=np.float32)
            self.audio_queue.put(audio_data)
        return (None, pyaudio.paContinue)
    
    def start_audio_stream(self):
        """启动音频流"""
        try:
            self.stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size,
                stream_callback=self.audio_callback
            )
            self.stream.start_stream()
            print("✅ 音频流启动成功")
            return True
        except Exception as e:
            print(f"❌ 音频流启动失败: {e}")
            return False
    
    def stop_audio_stream(self):
        """停止音频流"""
        if hasattr(self, 'stream'):
            self.stream.stop_stream()
            self.stream.close()
        self.audio.terminate()
        print("✅ 音频流已停止")
    
    def test_wake_word_detection(self, duration=10):
        """测试唤醒词检测功能"""
        print(f"\n🔍 开始唤醒词检测测试 (持续{duration}秒)")
        print("请说出唤醒词 'Hey, 艾比' 进行测试...")
        print("按 Ctrl+C 可提前结束测试\n")
        
        self.is_recording = True
        start_time = time.time()
        audio_buffer = []
        detection_count = 0
        
        try:
            while time.time() - start_time < duration:
                try:
                    # 获取音频数据
                    audio_data = self.audio_queue.get(timeout=0.1)
                    audio_buffer.extend(audio_data)
                    
                    # 当缓冲区达到一定长度时进行检测
                    if len(audio_buffer) >= self.sample_rate * 2:  # 2秒音频
                        # 转换为numpy数组
                        audio_array = np.array(audio_buffer, dtype=np.float32)
                        
                        # 进行唤醒词检测
                        result = self.detector.detect(audio_array)
                        
                        # 记录检测结果
                        timestamp = time.time() - start_time
                        test_result = {
                            'timestamp': timestamp,
                            'score': result['score'],
                            'is_wakeup': result['is_wakeup'],
                            'audio_length': len(audio_array) / self.sample_rate
                        }
                        self.test_results.append(test_result)
                        
                        # 输出检测结果
                        status = "🔔 检测到唤醒词!" if result['is_wakeup'] else "👂 监听中..."
                        print(f"[{timestamp:.1f}s] {status} 分数: {result['score']:.3f}")
                        
                        if result['is_wakeup']:
                            detection_count += 1
                        
                        # 清空缓冲区，保留最后0.5秒
                        keep_samples = int(self.sample_rate * 0.5)
                        audio_buffer = audio_buffer[-keep_samples:]
                        
                except queue.Empty:
                    continue
                    
        except KeyboardInterrupt:
            print("\n⏹️ 测试被用户中断")
        
        finally:
            self.is_recording = False
        
        return detection_count
    
    def test_with_audio_file(self, audio_file_path):
        """使用音频文件测试唤醒词检测"""
        print(f"\n🔍 使用音频文件测试: {audio_file_path}")
        
        try:
            # 读取音频文件
            audio_data, sr = sf.read(audio_file_path)
            
            # 重采样到16kHz
            if sr != self.sample_rate:
                import librosa
                audio_data = librosa.resample(audio_data, orig_sr=sr, target_sr=self.sample_rate)
            
            # 确保是单声道
            if len(audio_data.shape) > 1:
                audio_data = audio_data[:, 0]
            
            # 转换为float32
            audio_data = audio_data.astype(np.float32)
            
            # 进行检测
            result = self.detector.detect(audio_data)
            
            print(f"检测结果:")
            print(f"  唤醒词检测: {'是' if result['is_wakeup'] else '否'}")
            print(f"  置信度分数: {result['score']:.3f}")
            print(f"  音频长度: {len(audio_data) / self.sample_rate:.2f}秒")
            
            return result
            
        except Exception as e:
            print(f"❌ 音频文件测试失败: {e}")
            return None
    
    def analyze_test_results(self):
        """分析测试结果"""
        if not self.test_results:
            print("❌ 没有测试结果可分析")
            return
        
        print(f"\n📊 测试结果分析:")
        print(f"总检测次数: {len(self.test_results)}")
        
        # 统计唤醒词检测次数
        wakeup_detections = [r for r in self.test_results if r['is_wakeup']]
        print(f"唤醒词检测次数: {len(wakeup_detections)}")
        
        if wakeup_detections:
            scores = [r['score'] for r in wakeup_detections]
            print(f"唤醒词检测分数范围: {min(scores):.3f} - {max(scores):.3f}")
            print(f"平均唤醒词检测分数: {np.mean(scores):.3f}")
        
        # 统计所有检测的分数分布
        all_scores = [r['score'] for r in self.test_results]
        print(f"所有检测分数范围: {min(all_scores):.3f} - {max(all_scores):.3f}")
        print(f"平均检测分数: {np.mean(all_scores):.3f}")
        
        # 计算检测频率
        if len(self.test_results) > 1:
            time_span = self.test_results[-1]['timestamp'] - self.test_results[0]['timestamp']
            detection_rate = len(self.test_results) / time_span
            print(f"检测频率: {detection_rate:.2f} 次/秒")
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 aibi语音交互系统 - 唤醒词功能验证")
        print("=" * 50)
        
        # 1. 加载检测器
        if not self.load_detector():
            return False
        
        # 2. 启动音频流
        if not self.start_audio_stream():
            return False
        
        try:
            # 3. 实时检测测试
            detection_count = self.test_wake_word_detection(duration=15)
            
            # 4. 分析结果
            self.analyze_test_results()
            
            # 5. 输出总结
            print(f"\n✅ 测试完成!")
            print(f"检测到唤醒词 {detection_count} 次")
            
            if detection_count > 0:
                print("🎉 唤醒词检测功能正常工作!")
            else:
                print("⚠️ 未检测到唤醒词，请检查:")
                print("  - 麦克风是否正常工作")
                print("  - 是否说出了正确的唤醒词 'Hey, 艾比'")
                print("  - 环境噪音是否过大")
            
        finally:
            self.stop_audio_stream()
        
        return True

def main():
    """主函数"""
    tester = WakeWordTester()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--file" and len(sys.argv) > 2:
            # 使用音频文件测试
            if not tester.load_detector():
                return
            tester.test_with_audio_file(sys.argv[2])
        else:
            print("用法:")
            print("  python wake_word_test.py                    # 实时测试")
            print("  python wake_word_test.py --file audio.wav  # 文件测试")
            return
    else:
        # 实时测试
        tester.run_comprehensive_test()

if __name__ == "__main__":
    main() 