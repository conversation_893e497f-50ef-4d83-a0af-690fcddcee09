import requests
import json
import logging
import threading
import time
import re
from typing import Generator, Optional, Dict, Any
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class LLMClient:
    """LLM客户端，支持Dify API同步/流式调用、重试、连接池、缓存、日志分级、按句断流"""
    def __init__(self, config):
        self.api_url = config.get("llm.dify_api_url")
        self.api_key = config.get("llm.dify_api_key")
        self.max_tokens = config.get("llm.max_tokens", 2048)
        self.temperature = config.get("llm.temperature", 0.7)
        self.session = requests.Session()
        retries = Retry(total=3, backoff_factor=0.5, status_forcelist=[502, 503, 504])
        self.session.mount('http://', HTTPAdapter(max_retries=retries))
        self.session.mount('https://', HTTPAdapter(max_retries=retries))
        self.logger = logging.getLogger("llm_client")
        self.cache = {}

    def _cache_key(self, prompt, history=None):
        return json.dumps({"prompt": prompt, "history": history}, ensure_ascii=False)

    def query_llm(self, prompt: str, history: Optional[list] = None, use_cache=True) -> str:
        """
        同步调用Dify LLM，带缓存和重试
        """
        cache_key = self._cache_key(prompt, history)
        if use_cache and cache_key in self.cache:
            self.logger.debug("LLM缓存命中")
            return self.cache[cache_key]
        payload = {
            "inputs": {"k": 2},  # 修正为k为数值
            "query": prompt,
            "response_mode": "blocking",
            "user": "user"
        }
        if history:
            payload["conversation_id"] = history  # 视Dify实际API而定
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        try:
            resp = self.session.post(
                f"{self.api_url}/chat-messages",
                headers=headers,
                json=payload,
                timeout=30
            )
            resp.raise_for_status()
            data = resp.json()
            answer = data.get("answer") or data.get("data", {}).get("answer")
            if use_cache:
                self.cache[cache_key] = answer
            return answer
        except Exception as e:
            error_text = ""
            try:
                error_text = resp.text
            except Exception:
                pass
            self.logger.error(f"LLM请求失败: {e}, 返回内容: {error_text}")
            return "抱歉，LLM服务暂时不可用。"

    def generate_stream(self, prompt: str, user: str = "user") -> Generator[str, None, None]:
        """
        流式调用Dify LLM，yield每个chunk
        """
        payload = {
            "inputs": {"k": 2},  # 修正为k为数值
            "query": prompt,
            "response_mode": "streaming",
            "user": user
        }
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        try:
            resp = self.session.post(
                f"{self.api_url}/chat-messages",
                headers=headers,
                json=payload,
                stream=True,
                timeout=60
            )
            if resp.status_code != 200:
                self.logger.error(f"流式LLM请求失败: {resp.text}")
                yield "抱歉，LLM服务暂时不可用。"
                return
            for line in resp.iter_lines():
                if line:
                    try:
                        line_str = line.decode('utf-8')
                        if line_str.startswith('data: '):
                            line_str = line_str[6:]
                        data = json.loads(line_str)
                        if data.get('event') == 'message' and 'answer' in data:
                            yield data['answer']
                        elif data.get('event') == 'message_end':
                            break
                    except Exception as e:
                        self.logger.debug(f"解析流式响应出错: {e}, 内容: {line}")
        except Exception as e:
            error_text = ""
            try:
                error_text = resp.text
            except Exception:
                pass
            self.logger.error(f"流式LLM请求异常: {e}, 返回内容: {error_text}")
            yield "抱歉，LLM服务暂时不可用。"

    def stream_and_sentence_output(self, prompt: str, user: str = "user"):
        """
        流式输出，自动按中英文句子断句
        """
        buffer = ""
        sentence_end_pattern = re.compile(r'([。！？；.!?;])')
        for chunk in self.generate_stream(prompt, user):
            buffer += chunk
            sentences = sentence_end_pattern.split(buffer)
            for i in range(0, len(sentences)-1, 2):
                sentence = sentences[i] + sentences[i+1]
                if sentence.strip():
                    yield sentence.strip()
            if len(sentences) % 2 == 1:
                buffer = sentences[-1]
            else:
                buffer = ""
        if buffer.strip():
            yield buffer.strip()
