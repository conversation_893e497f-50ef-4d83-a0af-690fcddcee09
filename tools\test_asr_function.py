#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - ASR功能验证脚本
测试ASR模块的实际识别效果
"""

import os
import sys
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from modules.config_manager import ConfigManager
from modules.asr import ASRProcessor

def test_asr_function():
    """测试ASR功能"""
    print("🔍 开始ASR功能验证...")
    
    # 加载配置
    config = ConfigManager("config.yaml")
    
    # 创建ASR处理器
    asr_processor = ASRProcessor(config.config)
    
    # 打印模型信息
    model_info = asr_processor.get_model_info()
    print(f"📋 模型信息:")
    for key, value in model_info.items():
        print(f"  {key}: {value}")
    
    # 健康检查
    health = asr_processor.health_check()
    print(f"🏥 健康状态: {health['status']}")
    
    # 测试音频质量检查
    print("\n🎵 测试音频质量检查...")
    
    # 高质量音频
    good_audio = np.random.randn(16000).astype(np.float32) * 0.5
    quality_good = asr_processor._check_audio_quality(good_audio)
    print(f"  高质量音频检查结果: {quality_good}")
    
    # 低质量音频
    poor_audio = np.random.randn(16000).astype(np.float32) * 0.001
    quality_poor = asr_processor._check_audio_quality(poor_audio)
    print(f"  低质量音频检查结果: {quality_poor}")
    
    # 测试音频预处理
    print("\n🔧 测试音频预处理...")
    
    test_audio = np.random.randn(8000).astype(np.float32)
    processed_audio = asr_processor._preprocess_audio(test_audio)
    print(f"  原始音频长度: {len(test_audio)}")
    print(f"  处理后音频长度: {len(processed_audio)}")
    print(f"  处理后音频类型: {processed_audio.dtype}")
    
    # 测试识别功能
    print("\n🎯 测试语音识别...")
    
    # 使用高质量音频进行识别
    if quality_good:
        print("  使用高质量音频进行识别测试...")
        result = asr_processor.recognize(good_audio)
        
        if result:
            print(f"  ✅ 识别成功!")
            print(f"    文本: {result.get('text', 'N/A')}")
            print(f"    置信度: {result.get('confidence', 0.0):.3f}")
            print(f"    处理时间: {result.get('processing_time', 0.0):.3f}s")
            print(f"    模型: {result.get('model', 'N/A')}")
            print(f"    状态: {result.get('status', 'N/A')}")
        else:
            print("  ❌ 识别失败或无结果")
    else:
        print("  ⚠️ 音频质量检查未通过，跳过识别测试")
    
    # 测试流式识别
    print("\n🔄 测试流式识别...")
    
    success = asr_processor.start_streaming()
    if success:
        print("  ✅ 流式识别启动成功")
        
        # 模拟流式音频数据
        for i in range(5):
            audio_chunk = np.random.randn(1024).astype(np.float32) * 0.3
            result = asr_processor.process_streaming_audio(audio_chunk)
            
            if result:
                print(f"  📝 流式识别结果: {result.get('text', 'N/A')}")
                break
            else:
                print(f"  ⏳ 流式处理中... ({i+1}/5)")
        
        asr_processor.stop_streaming()
        print("  ✅ 流式识别已停止")
    else:
        print("  ❌ 流式识别启动失败")
    
    # 测试备用识别
    print("\n🛡️ 测试备用识别...")
    
    fallback_result = asr_processor._fallback_recognize(good_audio)
    if fallback_result:
        print(f"  ✅ 备用识别成功: {fallback_result.get('text', 'N/A')}")
        print(f"    置信度: {fallback_result.get('confidence', 0.0):.3f}")
        print(f"    模型: {fallback_result.get('model', 'N/A')}")
    else:
        print("  ❌ 备用识别失败")
    
    print("\n🎉 ASR功能验证完成!")
    
    # 总结
    print("\n📊 验证总结:")
    print(f"  - 模型初始化: {'✅' if model_info['is_initialized'] else '❌'}")
    print(f"  - FunASR可用: {'✅' if model_info['funasr_available'] else '❌'}")
    print(f"  - 音频质量检查: {'✅' if quality_good else '❌'}")
    print(f"  - 流式识别: {'✅' if success else '❌'}")
    print(f"  - 备用识别: {'✅' if fallback_result else '❌'}")

def test_asr_performance():
    """测试ASR性能"""
    print("\n⚡ 开始ASR性能测试...")
    
    # 加载配置
    config = ConfigManager("config.yaml")
    asr_processor = ASRProcessor(config.config)
    
    # 创建测试音频
    test_audio = np.random.randn(16000).astype(np.float32) * 0.5
    
    # 测试处理时间
    start_time = time.time()
    result = asr_processor.recognize(test_audio)
    processing_time = time.time() - start_time
    
    print(f"  📈 性能指标:")
    print(f"    处理时间: {processing_time:.3f}s")
    if result:
        print(f"    识别延迟: {result.get('processing_time', 0.0):.3f}s")
    
    # 测试内存使用（简单估算）
    audio_size_mb = len(test_audio) * 4 / (1024 * 1024)  # float32 = 4 bytes
    print(f"    音频大小: {audio_size_mb:.2f}MB")
    
    print("  ✅ 性能测试完成")

if __name__ == "__main__":
    print("🚀 aibi语音交互系统 - ASR功能验证")
    print("=" * 50)
    
    try:
        # 基本功能测试
        test_asr_function()
        
        # 性能测试
        test_asr_performance()
        
        print("\n🎯 所有测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc() 