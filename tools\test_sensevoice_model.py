#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - SenseVoiceSmall模型测试脚本
测试下载的SenseVoiceSmall模型是否能正常工作
"""

import os
import sys
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_sensevoice_model():
    """测试SenseVoiceSmall模型"""
    print("🧪 开始测试SenseVoiceSmall模型...")
    
    try:
        # 检查模型文件
        model_dir = "./models/asr"
        if not os.path.exists(model_dir):
            print(f"❌ 模型目录不存在: {model_dir}")
            return False
        
        print(f"✅ 模型目录存在: {model_dir}")
        
        # 检查关键文件
        required_files = [
            "model.pt",
            "configuration.json", 
            "tokens.json",
            "chn_jpn_yue_eng_ko_spectok.bpe.model"
        ]
        
        for file in required_files:
            file_path = os.path.join(model_dir, file)
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"  ✅ {file} ({size} bytes)")
            else:
                print(f"  ❌ {file} (缺失)")
                return False
        
        # 尝试导入FunASR
        try:
            from funasr import AutoModel
            print("✅ FunASR导入成功")
        except ImportError:
            print("❌ FunASR未安装")
            print("请安装FunASR: pip install funasr")
            return False
        
        # 测试模型加载
        print("\n📥 正在加载模型...")
        start_time = time.time()
        
        try:
            model = AutoModel(
                model=model_dir,
                device="cpu",  # 使用CPU进行测试
                model_revision=None
            )
            
            load_time = time.time() - start_time
            print(f"✅ 模型加载成功 (耗时: {load_time:.2f}s)")
            
            # 测试模型信息
            print(f"\n📋 模型信息:")
            print(f"  模型路径: {model_dir}")
            print(f"  模型类型: SenseVoiceSmall")
            print(f"  设备: CPU")
            print(f"  加载时间: {load_time:.2f}s")
            
            # 测试音频文件识别
            test_audio_files = [
                os.path.join(model_dir, "example", "zh.mp3"),
                os.path.join(model_dir, "example", "en.mp3")
            ]
            
            for audio_file in test_audio_files:
                if os.path.exists(audio_file):
                    print(f"\n🎵 测试音频文件: {os.path.basename(audio_file)}")
                    
                    try:
                        # 执行识别
                        start_time = time.time()
                        result = model.generate(input=audio_file)
                        processing_time = time.time() - start_time
                        
                        if result and len(result) > 0:
                            text = result[0].get("text", "").strip()
                            print(f"  ✅ 识别结果: '{text}'")
                            print(f"  ⏱️  处理时间: {processing_time:.2f}s")
                        else:
                            print(f"  ❌ 识别失败或无结果")
                            
                    except Exception as e:
                        print(f"  ❌ 识别错误: {e}")
                else:
                    print(f"⚠️  测试音频文件不存在: {audio_file}")
            
            print(f"\n🎉 SenseVoiceSmall模型测试完成!")
            print(f"模型可以正常使用")
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

def test_asr_module():
    """测试ASR模块"""
    print("\n🔧 测试ASR模块...")
    
    try:
        from modules.asr import ASRProcessor
        from modules.config_manager import ConfigManager
        
        # 加载配置
        config = ConfigManager("config.yaml")
        
        # 创建ASR处理器
        asr_processor = ASRProcessor(config)
        
        # 检查模型状态
        model_info = asr_processor.get_model_info()
        print(f"📋 ASR模块信息:")
        for key, value in model_info.items():
            print(f"  {key}: {value}")
        
        # 健康检查
        health_status = asr_processor.health_check()
        print(f"\n🏥 健康检查:")
        for key, value in health_status.items():
            print(f"  {key}: {value}")
        
        if health_status.get("status") == "healthy":
            print("✅ ASR模块健康状态良好")
            return True
        else:
            print("❌ ASR模块存在问题")
            return False
            
    except Exception as e:
        print(f"❌ ASR模块测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 aibi语音交互系统 - SenseVoiceSmall模型测试")
    print("=" * 60)
    
    # 测试模型
    model_test_success = test_sensevoice_model()
    
    # 测试ASR模块
    asr_test_success = test_asr_module()
    
    print(f"\n📊 测试结果汇总:")
    print(f"  SenseVoiceSmall模型测试: {'✅ 通过' if model_test_success else '❌ 失败'}")
    print(f"  ASR模块测试: {'✅ 通过' if asr_test_success else '❌ 失败'}")
    
    if model_test_success and asr_test_success:
        print(f"\n🎉 所有测试通过! SenseVoiceSmall模型已成功集成到ASR模块中")
        print(f"现在可以在aibi语音交互系统中使用SenseVoiceSmall进行语音识别了")
    else:
        print(f"\n⚠️  部分测试失败，请检查模型安装和配置")

if __name__ == "__main__":
    main() 