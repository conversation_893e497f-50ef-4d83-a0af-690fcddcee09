#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - 唤醒词检测调试脚本
用于调试模型输出和特征提取过程
"""

import os
import sys
import numpy as np
import soundfile as sf
import librosa
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from modules.kws import WakeWordDetector, extract_features, load_model_info

def debug_model_info():
    """调试模型信息加载"""
    print("🔍 调试模型信息加载...")
    
    model_info_path = "./models/cnn/model_info.txt"
    info = load_model_info(model_info_path)
    
    print("模型信息:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    return info

def debug_feature_extraction(audio_file=None):
    """调试特征提取过程"""
    print("\n🔍 调试特征提取过程...")
    
    # 如果没有提供音频文件，创建一个测试音频
    if audio_file is None:
        print("创建测试音频...")
        # 创建一个简单的正弦波作为测试音频
        sample_rate = 16000
        duration = 2.0  # 2秒
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        # 创建一个包含多个频率的测试信号
        test_audio = 0.3 * np.sin(2 * np.pi * 440 * t) + 0.2 * np.sin(2 * np.pi * 880 * t)
        test_audio = test_audio.astype(np.float32)
    else:
        print(f"加载音频文件: {audio_file}")
        test_audio, sr = sf.read(audio_file)
        if sr != 16000:
            test_audio = librosa.resample(test_audio, orig_sr=sr, target_sr=16000)
        if len(test_audio.shape) > 1:
            test_audio = test_audio[:, 0]
        test_audio = test_audio.astype(np.float32)
    
    print(f"音频长度: {len(test_audio) / 16000:.2f}秒")
    print(f"音频范围: {test_audio.min():.3f} - {test_audio.max():.3f}")
    
    # 加载模型信息
    model_info = load_model_info("./models/cnn/model_info.txt")
    
    # 提取特征
    features = extract_features(test_audio, model_info)
    
    print(f"特征形状: {features.shape}")
    print(f"特征范围: {features.min():.3f} - {features.max():.3f}")
    print(f"特征均值: {np.mean(features):.3f}")
    print(f"特征标准差: {np.std(features):.3f}")
    
    return features, model_info

def debug_model_inference():
    """调试模型推理过程"""
    print("\n🔍 调试模型推理过程...")
    
    try:
        # 加载检测器
        detector = WakeWordDetector(
            model_type="onnx",
            model_path="./models/cnn/hey_aibi.onnx",
            model_info_path="./models/cnn/model_info.txt",
            device="cpu"
        )
        
        # 创建测试音频
        sample_rate = 16000
        duration = 2.0
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        test_audio = 0.3 * np.sin(2 * np.pi * 440 * t) + 0.2 * np.sin(2 * np.pi * 880 * t)
        test_audio = test_audio.astype(np.float32)
        
        print("进行模型推理...")
        result = detector.detect(test_audio)
        
        print(f"推理结果:")
        print(f"  分数: {result['score']:.6f}")
        print(f"  是否唤醒: {result['is_wakeup']}")
        print(f"  阈值: {detector.model_info.get('推荐阈值：', 0.85)}")
        
        return result
        
    except Exception as e:
        print(f"❌ 模型推理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_onnx_model():
    """调试ONNX模型结构"""
    print("\n🔍 调试ONNX模型结构...")
    
    try:
        import onnxruntime as ort
        
        # 加载ONNX模型
        model_path = "./models/cnn/hey_aibi.onnx"
        session = ort.InferenceSession(model_path)
        
        print("ONNX模型信息:")
        print(f"  输入数量: {len(session.get_inputs())}")
        print(f"  输出数量: {len(session.get_outputs())}")
        
        for i, input_info in enumerate(session.get_inputs()):
            print(f"  输入 {i}: {input_info.name}, 形状: {input_info.shape}, 类型: {input_info.type}")
        
        for i, output_info in enumerate(session.get_outputs()):
            print(f"  输出 {i}: {output_info.name}, 形状: {output_info.shape}, 类型: {output_info.type}")
        
        # 测试推理
        input_name = session.get_inputs()[0].name
        input_shape = session.get_inputs()[0].shape
        
        print(f"\n测试推理:")
        print(f"  输入名称: {input_name}")
        print(f"  输入形状: {input_shape}")
        
        # 创建测试输入
        test_input = np.random.randn(*input_shape).astype(np.float32)
        
        # 运行推理
        outputs = session.run(None, {input_name: test_input})
        
        print(f"  输出形状: {[out.shape for out in outputs]}")
        print(f"  输出范围: {[f'{out.min():.3f} - {out.max():.3f}' for out in outputs]}")
        
        return session
        
    except Exception as e:
        print(f"❌ ONNX模型调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_with_real_audio():
    """使用真实音频测试"""
    print("\n🔍 使用真实音频测试...")
    
    # 检查是否有测试音频文件
    test_files = [
        "./test_audio.wav",
        "./test.wav", 
        "./audio_test.wav"
    ]
    
    audio_file = None
    for file in test_files:
        if os.path.exists(file):
            audio_file = file
            break
    
    if audio_file:
        print(f"找到测试音频文件: {audio_file}")
        
        try:
            detector = WakeWordDetector(
                model_type="onnx",
                model_path="./models/cnn/hey_aibi.onnx",
                model_info_path="./models/cnn/model_info.txt",
                device="cpu"
            )
            
            # 读取音频
            audio_data, sr = sf.read(audio_file)
            if sr != 16000:
                audio_data = librosa.resample(audio_data, orig_sr=sr, target_sr=16000)
            if len(audio_data.shape) > 1:
                audio_data = audio_data[:, 0]
            audio_data = audio_data.astype(np.float32)
            
            print(f"音频信息:")
            print(f"  长度: {len(audio_data) / 16000:.2f}秒")
            print(f"  范围: {audio_data.min():.3f} - {audio_data.max():.3f}")
            
            # 进行检测
            result = detector.detect(audio_data)
            
            print(f"检测结果:")
            print(f"  分数: {result['score']:.6f}")
            print(f"  是否唤醒: {result['is_wakeup']}")
            
        except Exception as e:
            print(f"❌ 真实音频测试失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("未找到测试音频文件，跳过真实音频测试")

def main():
    """主函数"""
    print("🚀 aibi语音交互系统 - 唤醒词检测调试")
    print("=" * 50)
    
    # 1. 调试模型信息
    model_info = debug_model_info()
    
    # 2. 调试特征提取
    features, _ = debug_feature_extraction()
    
    # 3. 调试ONNX模型结构
    session = debug_onnx_model()
    
    # 4. 调试模型推理
    result = debug_model_inference()
    
    # 5. 使用真实音频测试
    test_with_real_audio()
    
    print("\n✅ 调试完成!")
    print("\n建议:")
    if result and result['score'] < 0:
        print("  - 模型输出为负值，可能需要调整阈值或检查模型训练")
    if session:
        print("  - ONNX模型结构正常")
    print("  - 可以尝试使用真实的唤醒词音频进行测试")

if __name__ == "__main__":
    main() 