#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - 验证模型文件位置
验证SenseVoiceSmall模型文件是否在正确位置
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def verify_model_location():
    """验证模型文件位置"""
    print("🔍 验证SenseVoiceSmall模型文件位置...")
    
    # 检查模型目录
    model_dir = "./models/asr"
    if not os.path.exists(model_dir):
        print(f"❌ 模型目录不存在: {model_dir}")
        return False
    
    print(f"✅ 模型目录存在: {model_dir}")
    
    # 检查关键文件
    required_files = [
        "model.pt",
        "configuration.json", 
        "tokens.json",
        "chn_jpn_yue_eng_ko_spectok.bpe.model",
        "config.yaml"
    ]
    
    all_files_exist = True
    for file in required_files:
        file_path = os.path.join(model_dir, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✅ {file} ({size} bytes)")
        else:
            print(f"  ❌ {file} (缺失)")
            all_files_exist = False
    
    # 检查示例音频文件
    example_dir = os.path.join(model_dir, "example")
    if os.path.exists(example_dir):
        print(f"\n📁 示例音频文件:")
        audio_files = [f for f in os.listdir(example_dir) if f.endswith(('.mp3', '.wav'))]
        for audio_file in audio_files:
            file_path = os.path.join(example_dir, audio_file)
            size = os.path.getsize(file_path)
            print(f"  ✅ {audio_file} ({size} bytes)")
    else:
        print(f"  ⚠️  示例目录不存在: {example_dir}")
    
    # 检查配置文件中的路径
    config_file = "config.yaml"
    if os.path.exists(config_file):
        print(f"\n📋 配置文件检查:")
        try:
            import yaml
            with open(config_file, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
            
            asr_model_path = config.get("models", {}).get("asr_model_path", "")
            print(f"  配置文件中的ASR模型路径: {asr_model_path}")
            
            if asr_model_path == "./modules/asr":
                print("  ✅ 配置文件中的路径正确")
            else:
                print("  ⚠️  配置文件中的路径可能需要更新")
                
        except Exception as e:
            print(f"  ❌ 读取配置文件失败: {e}")
    
    if all_files_exist:
        print(f"\n🎉 模型文件位置验证通过!")
        print(f"SenseVoiceSmall模型已正确放置在 {model_dir} 目录中")
        return True
    else:
        print(f"\n❌ 模型文件位置验证失败!")
        print(f"请检查模型文件是否完整")
        return False

def main():
    """主函数"""
    print("🚀 aibi语音交互系统 - 模型文件位置验证")
    print("=" * 60)
    
    success = verify_model_location()
    
    if success:
        print(f"\n✅ 验证完成! 模型文件位置正确")
        print(f"现在可以继续安装FunASR并测试模型功能")
    else:
        print(f"\n❌ 验证失败! 请检查模型文件")

if __name__ == "__main__":
    main() 