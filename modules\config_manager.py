"""
配置管理模块
负责加载、验证和管理系统配置文件
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.logger = logging.getLogger(__name__)
        
        # 加载配置文件
        self.load_config()
        
    def load_config(self) -> bool:
        """
        加载配置文件
        
        Returns:
            bool: 是否加载成功
        """
        try:
            if not os.path.exists(self.config_path):
                self.logger.error(f"配置文件不存在: {self.config_path}")
                return False
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
                
            # 验证配置
            if self.validate_config():
                self.logger.info("配置文件加载成功")
                return True
            else:
                self.logger.error("配置文件验证失败")
                return False
                
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return False
    
    def validate_config(self) -> bool:
        """
        验证配置文件
        
        Returns:
            bool: 验证是否通过
        """
        try:
            required_sections = [
                'audio', 'models', 'hardware', 'llm', 
                'interrupt', 'logging', 'monitoring'
            ]
            
            for section in required_sections:
                if section not in self.config:
                    self.logger.error(f"缺少必需的配置节: {section}")
                    return False
            
            # 验证音频配置
            audio_config = self.config.get('audio', {})
            if not self._validate_audio_config(audio_config):
                return False
                
            # 验证模型配置
            models_config = self.config.get('models', {})
            if not self._validate_models_config(models_config):
                return False
                
            # 验证硬件配置
            hardware_config = self.config.get('hardware', {})
            if not self._validate_hardware_config(hardware_config):
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"验证配置文件失败: {e}")
            return False
    
    def _validate_audio_config(self, audio_config: Dict) -> bool:
        """验证音频配置"""
        try:
            # 检查输入配置
            input_config = audio_config.get('input', {})
            if not input_config:
                self.logger.error("缺少音频输入配置")
                return False
                
            # 检查输出配置
            output_config = audio_config.get('output', {})
            if not output_config:
                self.logger.error("缺少音频输出配置")
                return False
                
            # 检查VAD配置
            vad_config = audio_config.get('vad', {})
            if not vad_config:
                self.logger.error("缺少VAD配置")
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"验证音频配置失败: {e}")
            return False
    
    def _validate_models_config(self, models_config: Dict) -> bool:
        """验证模型配置"""
        try:
            # 检查模型路径
            required_paths = ['asr_model_path', 'kws_model_path', 'tts_model_path', 'vad_model_path']
            for path_key in required_paths:
                if path_key not in models_config:
                    self.logger.error(f"缺少模型路径配置: {path_key}")
                    return False
                    
            return True
            
        except Exception as e:
            self.logger.error(f"验证模型配置失败: {e}")
            return False
    
    def _validate_hardware_config(self, hardware_config: Dict) -> bool:
        """验证硬件配置"""
        try:
            # 检查设备配置
            if 'device' not in hardware_config:
                self.logger.error("缺少设备配置")
                return False
                
            device = hardware_config.get('device')
            if device not in ['cpu', 'gpu', 'npu', 'auto']:
                self.logger.error(f"无效的设备配置: {device}")
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"验证硬件配置失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        try:
            keys = key.split('.')
            value = self.config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
                    
            return value
            
        except Exception as e:
            self.logger.error(f"获取配置失败 {key}: {e}")
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
            
        Returns:
            bool: 是否设置成功
        """
        try:
            keys = key.split('.')
            config = self.config
            
            # 遍历到最后一个键的父级
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
                
            # 设置值
            config[keys[-1]] = value
            return True
            
        except Exception as e:
            self.logger.error(f"设置配置失败 {key}: {e}")
            return False
    
    def save_config(self) -> bool:
        """
        保存配置到文件
        
        Returns:
            bool: 是否保存成功
        """
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            self.logger.info("配置文件保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def reload_config(self) -> bool:
        """
        重新加载配置文件
        
        Returns:
            bool: 是否重新加载成功
        """
        return self.load_config()
    
    def get_audio_config(self) -> Dict[str, Any]:
        """获取音频配置"""
        return self.config.get('audio', {})
    
    def get_models_config(self) -> Dict[str, Any]:
        """获取模型配置"""
        return self.config.get('models', {})
    
    def get_hardware_config(self) -> Dict[str, Any]:
        """获取硬件配置"""
        return self.config.get('hardware', {})
    
    def get_llm_config(self) -> Dict[str, Any]:
        """获取LLM配置"""
        return self.config.get('llm', {})
    
    def get_interrupt_config(self) -> Dict[str, Any]:
        """获取插话中断配置"""
        return self.config.get('interrupt', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.config.get('logging', {})
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return self.config.get('monitoring', {})
    
    def get_debug_config(self) -> Dict[str, Any]:
        """获取调试配置"""
        return self.config.get('debug', {})
    
    def is_debug_enabled(self) -> bool:
        """检查是否启用调试模式"""
        return self.get('debug.enabled', False)
    
    def get_wake_word(self) -> str:
        """获取唤醒词"""
        return self.get('wake_word', 'Hey,艾比')
    
    def get_wake_word_threshold(self) -> float:
        """获取唤醒词检测阈值"""
        return self.get('wake_word_threshold', 0.85)
    
    def get_interaction_timeout(self) -> int:
        """获取交互超时时间"""
        return self.get('interaction_timeout', 10)
    
    def get_device(self) -> str:
        """获取推理设备"""
        return self.get('hardware.device', 'auto')
    
    def get_language(self) -> str:
        """获取默认语言"""
        return self.get('language.default', 'zh-CN')


# 全局配置管理器实例
config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """
    获取全局配置管理器实例
    
    Returns:
        ConfigManager: 配置管理器实例
    """
    global config_manager
    if config_manager is None:
        config_manager = ConfigManager()
    return config_manager


def get_config(key: str, default: Any = None) -> Any:
    """
    获取配置值的便捷函数
    
    Args:
        key: 配置键
        default: 默认值
        
    Returns:
        Any: 配置值
    """
    return get_config_manager().get(key, default)


def set_config(key: str, value: Any) -> bool:
    """
    设置配置值的便捷函数
    
    Args:
        key: 配置键
        value: 配置值
        
    Returns:
        bool: 是否设置成功
    """
    return get_config_manager().set(key, value)


def reload_config() -> bool:
    """
    重新加载配置的便捷函数
    
    Returns:
        bool: 是否重新加载成功
    """
    return get_config_manager().reload_config()


if __name__ == "__main__":
    # 测试配置管理器
    config_mgr = ConfigManager()
    print("配置加载成功:", config_mgr.load_config())
    print("唤醒词:", config_mgr.get_wake_word())
    print("设备:", config_mgr.get_device())
    print("音频配置:", config_mgr.get_audio_config()) 