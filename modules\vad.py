import numpy as np
from typing import Dict, Any

class VADProcessor:
    """
    语音活动检测处理器，支持FSMN-VAD模型和能量检测双模式
    """
    def __init__(self, config):
        self.config = config
        self.threshold = config.get("vad.threshold", 0.5)
        self.frame_duration_ms = config.get("vad.frame_duration_ms", 30)
        self.min_speech_duration_ms = config.get("vad.min_speech_duration_ms", 250)
        self.model_path = config.get("vad.model_path", "./models/vad/fsmn-vad.onnx")
        self.enable_vad = config.get("vad.enable", True)
        self.logger = None
        try:
            import funasr
            self.funasr = funasr
            # 尝试多种模型加载方式
            try:
                # 方式1: 直接使用本地路径
                self.vad_model = funasr.AutoModel(model=self.model_path, vad_model="fsmn-vad", vad_kwargs={"device": "cpu"})
                self.logger = self.logger or print
                self.logger(f"FSMN-VAD模型加载成功(方式1): {self.model_path}")
                self.use_fsmn_vad = True
            except Exception as e1:
                try:
                    # 方式2: 使用task参数
                    self.vad_model = funasr.AutoModel(model=self.model_path, task="vad")
                    self.logger = self.logger or print
                    self.logger(f"FSMN-VAD模型加载成功(方式2): {self.model_path}")
                    self.use_fsmn_vad = True
                except Exception as e2:
                    try:
                        # 方式3: 仅使用模型路径
                        self.vad_model = funasr.AutoModel(model=self.model_path)
                        self.logger = self.logger or print
                        self.logger(f"FSMN-VAD模型加载成功(方式3): {self.model_path}")
                        self.use_fsmn_vad = True
                    except Exception as e3:
                        # 所有方式都失败，记录详细错误信息
                        self.logger = self.logger or print
                        self.logger(f"FSMN-VAD模型加载失败，尝试了3种方式:")
                        self.logger(f"  方式1错误: {e1}")
                        self.logger(f"  方式2错误: {e2}")
                        self.logger(f"  方式3错误: {e3}")
                        self.logger("降级为能量检测")
                        self.use_fsmn_vad = False
        except Exception as e:
            self.logger = self.logger or print
            self.logger(f"FSMN-VAD模型加载失败，降级为能量检测: {e}")
            self.use_fsmn_vad = False

    def process(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """
        处理音频数据，检测语音活动
        """
        try:
            if self.enable_vad and self.use_fsmn_vad:
                # FSMN-VAD模型推理
                result = self.vad_model.generate(input=audio_data)
                speech_prob = result.get('speech_prob', 0.0)
                has_speech = speech_prob > self.threshold
                return {
                    "has_speech": has_speech,
                    "speech_prob": float(speech_prob),
                    "threshold": self.threshold,
                    "duration_ms": len(audio_data) / 16000 * 1000
                }
            else:
                # 兜底能量检测
                energy = np.mean(np.abs(audio_data))
                has_speech = energy > self.threshold
                return {
                    "has_speech": has_speech,
                    "energy": float(energy),
                    "threshold": self.threshold,
                    "duration_ms": len(audio_data) / 16000 * 1000
                }
        except Exception as e:
            return {
                "has_speech": False,
                "error": str(e)
            }

def vad_process(audio_frame):
    """
    简单模拟：假设音频非空即为语音段
    实际可集成funasr/fsmn-vad等模型
    """
    if audio_frame and len(audio_frame) > 0:
        return True
    return False

if __name__ == "__main__":
    print(vad_process(b"123"))  # True
    print(vad_process(b""))     # False
