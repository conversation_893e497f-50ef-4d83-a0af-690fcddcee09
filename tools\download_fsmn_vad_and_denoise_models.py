import os
from modelscope.hub.snapshot_download import snapshot_download

if __name__ == "__main__":
    # FSMN-VAD（ONNX）
    vad_dir = snapshot_download('damo/speech_fsmn_vad_zh-cn-16k-common-onnx', revision='v2.0.4', cache_dir='./models/vad')
    print(f"FSMN-VAD模型已下载到: {vad_dir}")
    # 降噪
    denoise_dir = snapshot_download('damo/speech_frcrn_ans_cirm_16k', revision='v1.0.2', cache_dir='./models/denoise')
    print(f"降噪模型已下载到: {denoise_dir}")
    print("全部模型下载完成！")