#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - ASR模块测试
测试FunASR集成和SenseVoiceSmall模型功能
"""

import os
import sys
import time
import numpy as np
import pytest
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from modules.asr import ASRProcessor

class TestASRProcessor:
    """ASR处理器测试类"""
    
    def setup_method(self):
        """测试前设置"""
        # 测试配置
        self.test_config = {
            "models.asr_model_path": "./models/asr",
            "models.asr.beam_size": 5,
            "models.asr.max_length": 512,
            "models.asr.temperature": 0.0,
            "audio.input.sample_rate": 16000,
            "audio.input.chunk_size": 1024,
            "hardware.device": "auto",
            "hardware.num_threads": 4,
            "hardware.enable_cache": True,
            "hardware.enable_fp16": False
        }
        
        # 创建ASR处理器
        self.asr_processor = ASRProcessor(self.test_config)
    
    def test_asr_processor_initialization(self):
        """测试ASR处理器初始化"""
        assert self.asr_processor is not None
        assert hasattr(self.asr_processor, 'config')
        assert hasattr(self.asr_processor, 'logger')
        
        # 检查配置参数
        assert self.asr_processor.beam_size == 5
        assert self.asr_processor.max_length == 512
        assert self.asr_processor.temperature == 0.0
        assert self.asr_processor.sample_rate == 16000
        assert self.asr_processor.chunk_size == 1024
    
    def test_model_info(self):
        """测试模型信息获取"""
        model_info = self.asr_processor.get_model_info()
        
        assert isinstance(model_info, dict)
        assert "model_name" in model_info
        assert "is_initialized" in model_info
        assert "funasr_available" in model_info
        assert "device" in model_info
        assert "sample_rate" in model_info
        
        assert model_info["model_name"] == "SenseVoiceSmall"
        assert model_info["sample_rate"] == 16000
        assert model_info["beam_size"] == 5
    
    def test_health_check(self):
        """测试健康检查"""
        health = self.asr_processor.health_check()
        
        assert isinstance(health, dict)
        assert "status" in health
        assert "model_loaded" in health
        assert "funasr_available" in health
        assert "streaming_mode" in health
        assert "buffer_size" in health
        
        # 检查状态值
        assert health["streaming_mode"] == False
        assert health["buffer_size"] == 0
    
    def test_audio_preprocessing(self):
        """测试音频预处理"""
        # 创建测试音频数据
        test_audio = np.random.randn(8000).astype(np.float32)
        
        # 测试预处理
        processed_audio = self.asr_processor._preprocess_audio(test_audio)
        
        assert processed_audio is not None
        assert isinstance(processed_audio, np.ndarray)
        assert processed_audio.dtype == np.float32
        
        # 检查音频长度
        min_length = int(16000 * 0.1)  # 最少0.1秒
        assert len(processed_audio) >= min_length
    
    def test_audio_quality_check(self):
        """测试音频质量检查"""
        # 测试高质量音频
        good_audio = np.random.randn(16000).astype(np.float32) * 0.5
        quality_good = self.asr_processor._check_audio_quality(good_audio)
        
        # 测试低质量音频（静音）
        bad_audio = np.random.randn(16000).astype(np.float32) * 0.001
        quality_bad = self.asr_processor._check_audio_quality(bad_audio)
        
        # 测试空音频
        empty_audio = np.array([], dtype=np.float32)
        quality_empty = self.asr_processor._check_audio_quality(empty_audio)
        
        assert isinstance(quality_good, bool)
        assert isinstance(quality_bad, bool)
        assert isinstance(quality_empty, bool)
        
        # 高质量音频应该通过检查
        assert quality_good == True
        # 低质量音频应该不通过检查
        assert quality_bad == False
        # 空音频应该不通过检查
        assert quality_empty == False
    
    def test_fallback_recognize(self):
        """测试备用识别功能"""
        # 创建测试音频数据
        test_audio = np.random.randn(16000).astype(np.float32) * 0.3
        
        # 测试备用识别
        result = self.asr_processor._fallback_recognize(test_audio)
        
        if result is not None:
            assert isinstance(result, dict)
            assert "text" in result
            assert "confidence" in result
            assert "energy" in result
            assert "model" in result
            assert "status" in result
            
            assert result["model"] == "fallback"
            assert result["status"] == "fallback"
            assert isinstance(result["confidence"], float)
            assert isinstance(result["energy"], float)
    
    def test_streaming_control(self):
        """测试流式识别控制"""
        # 测试开始流式识别
        success = self.asr_processor.start_streaming()
        assert isinstance(success, bool)
        
        # 检查流式状态
        health = self.asr_processor.health_check()
        assert health["streaming_mode"] == True
        
        # 测试停止流式识别
        self.asr_processor.stop_streaming()
        
        # 检查流式状态
        health = self.asr_processor.health_check()
        assert health["streaming_mode"] == False
        assert health["buffer_size"] == 0
    
    def test_streaming_audio_processing(self):
        """测试流式音频处理"""
        # 开始流式识别
        self.asr_processor.start_streaming()
        
        # 创建测试音频块
        audio_chunk = np.random.randn(1024).astype(np.float32) * 0.2
        
        # 测试流式处理
        result = self.asr_processor.process_streaming_audio(audio_chunk)
        
        # 由于超时机制，可能需要多次调用才能获得结果
        # 这里主要测试函数调用是否正常
        assert result is None or isinstance(result, dict)
        
        # 停止流式识别
        self.asr_processor.stop_streaming()
    
    def test_recognize_with_quality_audio(self):
        """测试高质量音频识别"""
        # 创建高质量测试音频
        good_audio = np.random.randn(16000).astype(np.float32) * 0.5
        
        # 执行识别
        result = self.asr_processor.recognize(good_audio)
        
        # 检查结果格式
        if result is not None:
            assert isinstance(result, dict)
            assert "text" in result
            assert "confidence" in result
            assert "processing_time" in result
            assert "timestamp" in result
            assert "model" in result
            assert "status" in result
            
            assert isinstance(result["confidence"], float)
            assert isinstance(result["processing_time"], float)
            assert isinstance(result["timestamp"], float)
    
    def test_recognize_with_poor_audio(self):
        """测试低质量音频识别"""
        # 创建低质量测试音频
        poor_audio = np.random.randn(16000).astype(np.float32) * 0.001
        
        # 执行识别
        result = self.asr_processor.recognize(poor_audio)
        
        # 低质量音频应该返回None
        assert result is None
    
    def test_recognize_with_empty_audio(self):
        """测试空音频识别"""
        # 创建空音频
        empty_audio = np.array([], dtype=np.float32)
        
        # 执行识别
        result = self.asr_processor.recognize(empty_audio)
        
        # 空音频应该返回None
        assert result is None
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效音频数据
        invalid_audio = np.array([], dtype=np.float32)
        
        # 执行识别
        result = self.asr_processor.recognize(invalid_audio)
        
        # 应该返回None或错误结果
        if result is not None:
            assert isinstance(result, dict)
            if "error" in result:
                assert result["status"] == "error"

def test_asr_infer_function():
    """测试asr_infer兼容性函数"""
    from modules.asr import asr_infer
    
    # 创建测试音频数据
    test_audio = np.random.randn(16000).astype(np.float32) * 0.3
    
    # 测试函数调用
    result = asr_infer(test_audio)
    
    # 检查返回结果
    assert isinstance(result, str)
    assert len(result) > 0

def test_asr_module_import():
    """测试ASR模块导入"""
    try:
        from modules.asr import ASRProcessor, asr_infer
        assert True
    except ImportError as e:
        pytest.fail(f"ASR模块导入失败: {e}")

if __name__ == "__main__":
    # 运行测试
    print("开始ASR模块测试...")
    
    # 创建测试实例
    test_config = {
        "models.asr_model_path": "./models/asr",
        "models.asr.beam_size": 5,
        "models.asr.max_length": 512,
        "models.asr.temperature": 0.0,
        "audio.input.sample_rate": 16000,
        "audio.input.chunk_size": 1024,
        "hardware.device": "auto",
        "hardware.num_threads": 4,
        "hardware.enable_cache": True,
        "hardware.enable_fp16": False
    }
    
    asr_processor = ASRProcessor(test_config)
    
    # 打印模型信息
    model_info = asr_processor.get_model_info()
    print(f"模型信息: {model_info}")
    
    # 健康检查
    health = asr_processor.health_check()
    print(f"健康状态: {health}")
    
    # 测试识别
    test_audio = np.random.randn(16000).astype(np.float32) * 0.3
    result = asr_processor.recognize(test_audio)
    print(f"识别结果: {result}")
    
    print("ASR模块测试完成")
