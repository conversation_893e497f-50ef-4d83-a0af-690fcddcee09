#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - TTS语音合成模块
支持CosyVoice2和CosyVoice模型
"""

import os
import sys
import logging
import numpy as np
import tempfile
import soundfile as sf
from typing import Optional, Dict, Any, List, Union
import time
import threading
import queue
import asyncio

class TTSProcessor:
    """
    语音合成处理器，支持CosyVoice2和CosyVoice模型
    """

    def __init__(self, config):
        """初始化TTS处理器"""
        self.config = config
        self.logger = logging.getLogger("tts")

        # 配置参数
        self.model_path = config.get("models.tts.model_path", "./models/tts")
        self.speed = config.get("model_params.tts.speed", 1.0)
        self.volume = config.get("model_params.tts.volume", 1.0)
        self.pitch = config.get("model_params.tts.pitch", 1.0)
        self.device = config.get("hardware.device", "auto")
        self.enable_fp16 = config.get("hardware.enable_fp16", False)

        # 模型相关
        self.cosyvoice = None
        self.cosyvoice2 = None
        self.is_initialized = False
        self.model_type = None  # "cosyvoice2" or "cosyvoice"
        self.sample_rate = 22050

        # 性能监控
        self.synthesis_count = 0
        self.total_synthesis_time = 0.0
        self.last_synthesis_time = 0.0

        # 初始化模型
        self._init_models()

    def _init_models(self):
        """初始化CosyVoice模型"""
        try:
            self.logger.info("开始初始化TTS模型...")

            # 检查模型路径
            if not os.path.exists(self.model_path):
                self.logger.error(f"TTS模型路径不存在: {self.model_path}")
                return False

            # 优先尝试CosyVoice2-0.5B（用户指定）
            if self._init_cosyvoice2():
                self.model_type = "cosyvoice2"
                self.is_initialized = True
                self.logger.info("CosyVoice2-0.5B模型初始化成功")
                return True

            # 备选：尝试初始化CosyVoice-300M
            if self._init_cosyvoice_300m():
                self.model_type = "cosyvoice_300m"
                self.is_initialized = True
                self.logger.info("CosyVoice-300M模型初始化成功")
                return True

            self.logger.error("所有TTS模型初始化失败")
            return False

        except Exception as e:
            self.logger.error(f"TTS模型初始化异常: {e}")
            return False

    def _init_cosyvoice_300m(self):
        """初始化CosyVoice-300M模型"""
        try:
            # 检查CosyVoice-300M模型文件
            cosyvoice_300m_path = os.path.join(self.model_path, "CosyVoice-300M")
            if not os.path.exists(cosyvoice_300m_path):
                self.logger.debug(f"CosyVoice-300M模型路径不存在: {cosyvoice_300m_path}")
                return False

            # 检查必要的模型文件
            required_files = ['cosyvoice.yaml', 'llm.pt', 'flow.pt', 'hift.pt', 'campplus.onnx']
            for file_name in required_files:
                file_path = os.path.join(cosyvoice_300m_path, file_name)
                if not os.path.exists(file_path):
                    self.logger.debug(f"缺少必要文件: {file_path}")
                    return False

            # 导入CosyVoice
            try:
                # 添加插件库路径
                cosyvoice_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "plugins", "tts", "CosyVoice")
                matcha_path = os.path.join(cosyvoice_path, "third_party", "Matcha-TTS")
                if os.path.exists(cosyvoice_path):
                    sys.path.insert(0, cosyvoice_path)
                if os.path.exists(matcha_path):
                    sys.path.insert(0, matcha_path)

                # 检查Matcha-TTS是否可用
                try:
                    import matcha
                    matcha_available = True
                    self.logger.info("Matcha-TTS可用，继续CosyVoice-300M初始化")
                except ImportError:
                    matcha_available = False
                    self.logger.warning("Matcha-TTS不可用，跳过CosyVoice-300M初始化")
                    return False

                from cosyvoice.cli.cosyvoice import CosyVoice
                from cosyvoice.utils.file_utils import load_wav

                # 初始化CosyVoice-300M模型
                self.cosyvoice_300m = CosyVoice(
                    cosyvoice_300m_path,
                    load_jit=False,
                    load_trt=False,
                    fp16=self.enable_fp16
                )

                self.sample_rate = getattr(self.cosyvoice_300m, 'sample_rate', 22050)
                self.logger.info(f"CosyVoice-300M模型加载成功，采样率: {self.sample_rate}")

                # 检查可用的说话人
                available_spks = self.cosyvoice_300m.list_available_spks()
                self.logger.info(f"可用说话人: {available_spks}")

                return True

            except ImportError as e:
                self.logger.debug(f"CosyVoice-300M导入失败: {e}")
                return False
            except Exception as e:
                self.logger.debug(f"CosyVoice-300M初始化失败: {e}")
                return False

        except Exception as e:
            self.logger.debug(f"CosyVoice-300M初始化异常: {e}")
            return False

    def _init_cosyvoice2(self):
        """初始化CosyVoice2模型"""
        try:
            # 检查CosyVoice2模型文件
            cosyvoice2_path = os.path.join(self.model_path, "CosyVoice2-0.5B")
            if not os.path.exists(cosyvoice2_path):
                self.logger.debug(f"CosyVoice2模型路径不存在: {cosyvoice2_path}")
                return False

            # 检查必要的模型文件
            required_files = ['cosyvoice2.yaml', 'llm.pt', 'flow.pt', 'hift.pt', 'campplus.onnx']
            for file_name in required_files:
                file_path = os.path.join(cosyvoice2_path, file_name)
                if not os.path.exists(file_path):
                    self.logger.debug(f"缺少必要文件: {file_path}")
                    return False

            # 导入CosyVoice2
            try:
                # 添加插件库路径
                cosyvoice_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "plugins", "tts", "CosyVoice")
                matcha_path = os.path.join(cosyvoice_path, "third_party", "Matcha-TTS")
                if os.path.exists(cosyvoice_path):
                    sys.path.insert(0, cosyvoice_path)
                if os.path.exists(matcha_path):
                    sys.path.insert(0, matcha_path)

                # 检查Matcha-TTS是否可用
                try:
                    import matcha
                    matcha_available = True
                    self.logger.debug("Matcha-TTS可用，继续CosyVoice2初始化")
                except ImportError:
                    matcha_available = False
                    self.logger.debug("Matcha-TTS不可用，跳过CosyVoice2初始化")
                    return False

                from cosyvoice.cli.cosyvoice import CosyVoice2
                from cosyvoice.utils.file_utils import load_wav

                # 初始化CosyVoice2模型
                self.cosyvoice2 = CosyVoice2(
                    cosyvoice2_path,
                    load_jit=False,
                    load_trt=False,
                    fp16=self.enable_fp16
                )

                self.sample_rate = getattr(self.cosyvoice2, 'sample_rate', 22050)
                self.logger.info(f"CosyVoice2模型加载成功，采样率: {self.sample_rate}")
                return True

            except ImportError as e:
                self.logger.debug(f"CosyVoice2导入失败: {e}")
                return False
            except Exception as e:
                self.logger.debug(f"CosyVoice2初始化失败: {e}")
                return False

        except Exception as e:
            self.logger.debug(f"CosyVoice2初始化异常: {e}")
            return False

    def _init_cosyvoice(self):
        """初始化CosyVoice模型"""
        try:
            # 检查CosyVoice模型文件
            cosyvoice_paths = [
                os.path.join(self.model_path, "CosyVoice-300M-SFT"),
                os.path.join(self.model_path, "CosyVoice-300M"),
                self.model_path  # 直接使用模型路径
            ]

            cosyvoice_path = None
            for path in cosyvoice_paths:
                if os.path.exists(path):
                    cosyvoice_path = path
                    break

            if not cosyvoice_path:
                self.logger.debug("未找到CosyVoice模型文件")
                return False

            # 导入CosyVoice
            try:
                # 添加插件库路径
                cosyvoice_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "plugins", "tts", "CosyVoice")
                matcha_path = os.path.join(cosyvoice_path, "third_party", "Matcha-TTS")
                if os.path.exists(cosyvoice_path):
                    sys.path.insert(0, cosyvoice_path)
                if os.path.exists(matcha_path):
                    sys.path.insert(0, matcha_path)

                from cosyvoice.cli.cosyvoice import CosyVoice
                from cosyvoice.utils.file_utils import load_wav

                # 初始化CosyVoice模型
                self.cosyvoice = CosyVoice(
                    cosyvoice_path,
                    load_jit=False,
                    load_trt=False,
                    fp16=self.enable_fp16
                )

                self.sample_rate = getattr(self.cosyvoice, 'sample_rate', 22050)
                self.logger.info(f"CosyVoice模型加载成功，路径: {cosyvoice_path}，采样率: {self.sample_rate}")
                return True

            except ImportError as e:
                self.logger.debug(f"CosyVoice导入失败: {e}")
                return False
            except Exception as e:
                self.logger.debug(f"CosyVoice初始化失败: {e}")
                return False

        except Exception as e:
            self.logger.debug(f"CosyVoice初始化异常: {e}")
            return False

    def synthesize(self, text: str, prompt_text: Optional[str] = None, prompt_speech_16k: Optional[np.ndarray] = None) -> Optional[np.ndarray]:
        """
        语音合成主函数

        Args:
            text: 要合成的文本
            prompt_text: 提示文本（用于zero-shot）
            prompt_speech_16k: 提示音频（用于zero-shot）

        Returns:
            合成的音频数据（numpy数组）
        """
        if not self.is_initialized:
            self.logger.error("TTS模型未初始化")
            return None

        if not text or not text.strip():
            self.logger.warning("输入文本为空")
            return None

        start_time = time.time()

        try:
            # 根据模型类型选择合成方法
            if self.model_type == "cosyvoice_300m":
                audio_data = self._synthesize_cosyvoice_300m(text, prompt_text, prompt_speech_16k)
            elif self.model_type == "cosyvoice2":
                audio_data = self._synthesize_cosyvoice2(text, prompt_text, prompt_speech_16k)
            elif self.model_type == "cosyvoice":
                audio_data = self._synthesize_cosyvoice(text, prompt_text, prompt_speech_16k)
            else:
                self.logger.error(f"未知的模型类型: {self.model_type}")
                return None

            # 更新性能统计
            synthesis_time = time.time() - start_time
            self.synthesis_count += 1
            self.total_synthesis_time += synthesis_time
            self.last_synthesis_time = synthesis_time

            if audio_data is not None:
                self.logger.info(f"TTS合成成功，耗时: {synthesis_time:.3f}s，文本长度: {len(text)}")
                return audio_data
            else:
                self.logger.error("TTS合成返回空结果")
                return None

        except Exception as e:
            self.logger.error(f"TTS合成异常: {e}")
            return None

    def _synthesize_cosyvoice_300m(self, text: str, prompt_text: Optional[str] = None, prompt_speech_16k: Optional[np.ndarray] = None) -> Optional[np.ndarray]:
        """
        使用CosyVoice-300M进行语音合成

        Args:
            text: 要合成的文本
            prompt_text: 提示文本（用于zero-shot）
            prompt_speech_16k: 提示音频（用于zero-shot）

        Returns:
            合成的音频数据
        """
        try:
            if prompt_text and prompt_speech_16k is not None:
                # Zero-shot合成
                self.logger.info("使用CosyVoice-300M zero-shot模式")
                for i, result in enumerate(self.cosyvoice_300m.inference_zero_shot(text, prompt_text, prompt_speech_16k, stream=False)):
                    if 'tts_speech' in result:
                        audio_data = result['tts_speech'].cpu().numpy()
                        if audio_data.ndim > 1:
                            audio_data = audio_data.squeeze()
                        self.logger.info(f"CosyVoice-300M zero-shot合成成功，音频长度: {len(audio_data)}")
                        return audio_data.astype(np.float32)
            else:
                # 使用zero-shot模式（基础模型需要prompt音频）
                try:
                    # 检查是否有可用的说话人（SFT模式）
                    available_spks = self.cosyvoice_300m.list_available_spks()
                    if len(available_spks) > 0:
                        # 选择一个中文女声说话人
                        speaker_id = None
                        for spk in available_spks:
                            if '中文女' in spk or '女' in spk:
                                speaker_id = spk
                                break

                        # 如果没找到女声，使用第一个可用的说话人
                        if speaker_id is None:
                            speaker_id = available_spks[0]

                        self.logger.info(f"使用CosyVoice-300M SFT模式，说话人: {speaker_id}")
                        for i, result in enumerate(self.cosyvoice_300m.inference_sft(text, speaker_id, stream=False)):
                            if 'tts_speech' in result:
                                audio_data = result['tts_speech'].cpu().numpy()
                                if audio_data.ndim > 1:
                                    audio_data = audio_data.squeeze()
                                self.logger.info(f"CosyVoice-300M SFT合成成功，音频长度: {len(audio_data)}")
                                return audio_data.astype(np.float32)
                    else:
                        # 没有预设说话人，使用zero-shot模式
                        self.logger.info("CosyVoice-300M没有预设说话人，使用zero-shot模式")

                        # 使用提供的prompt音频文件
                        prompt_audio_path = os.path.join(self.model_path, "asset", "zero_shot_prompt.wav")
                        if os.path.exists(prompt_audio_path):
                            # 加载prompt音频
                            from cosyvoice.utils.file_utils import load_wav
                            prompt_speech_16k = load_wav(prompt_audio_path, 16000)
                            prompt_text = "希望你以后能够做的比我还好呦。"  # 根据官方示例

                            self.logger.info(f"使用zero-shot模式，prompt文件: {prompt_audio_path}")
                            for i, result in enumerate(self.cosyvoice_300m.inference_zero_shot(text, prompt_text, prompt_speech_16k, stream=False)):
                                if 'tts_speech' in result:
                                    audio_data = result['tts_speech'].cpu().numpy()
                                    if audio_data.ndim > 1:
                                        audio_data = audio_data.squeeze()
                                    self.logger.info(f"CosyVoice-300M zero-shot合成成功，音频长度: {len(audio_data)}")
                                    return audio_data.astype(np.float32)
                        else:
                            self.logger.warning(f"找不到prompt音频文件: {prompt_audio_path}")

                except Exception as e:
                    self.logger.warning(f"CosyVoice-300M合成失败: {e}")

            return None

        except Exception as e:
            self.logger.error(f"CosyVoice-300M合成异常: {e}")
            return None

    def _synthesize_cosyvoice2(self, text: str, prompt_text: Optional[str] = None, prompt_speech_16k: Optional[np.ndarray] = None) -> Optional[np.ndarray]:
        """
        使用CosyVoice2进行语音合成

        Args:
            text: 要合成的文本
            prompt_text: 提示文本
            prompt_speech_16k: 提示音频

        Returns:
            合成的音频数据
        """
        try:
            if prompt_text and prompt_speech_16k is not None:
                # Zero-shot合成
                for i, result in enumerate(self.cosyvoice2.inference_zero_shot(text, prompt_text, prompt_speech_16k, stream=False)):
                    if 'tts_speech' in result:
                        audio_data = result['tts_speech'].cpu().numpy()
                        if audio_data.ndim > 1:
                            audio_data = audio_data.squeeze()
                        return audio_data.astype(np.float32)
            else:
                # 使用预设说话人（如果支持）
                try:
                    # 检查可用的说话人
                    spk_info = self.cosyvoice2.frontend.spk2info
                    spk_list = list(spk_info.keys())

                    if len(spk_list) > 0:
                        # 使用第一个可用的说话人
                        speaker_id = spk_list[0]
                        self.logger.info(f"使用说话人 '{speaker_id}' 进行SFT合成")

                        for i, result in enumerate(self.cosyvoice2.inference_sft(text, speaker_id, stream=False)):
                            if 'tts_speech' in result:
                                audio_data = result['tts_speech'].cpu().numpy()
                                if audio_data.ndim > 1:
                                    audio_data = audio_data.squeeze()
                                return audio_data.astype(np.float32)
                    else:
                        # 没有预设说话人，尝试instruct2模式
                        self.logger.info("没有预设说话人，尝试instruct2模式")
                        import torch
                        import torchaudio

                        # 使用真实的音频文件作为prompt（静音无法被speech tokenizer处理）
                        prompt_audio_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "models", "asr", "example", "zh.mp3")

                        try:
                            if os.path.exists(prompt_audio_path):
                                # 加载示例音频文件
                                prompt_speech, orig_sr = torchaudio.load(prompt_audio_path)

                                # 转换为单声道
                                if prompt_speech.shape[0] > 1:
                                    prompt_speech = prompt_speech.mean(dim=0, keepdim=True)

                                # 重采样到16kHz
                                if orig_sr != 16000:
                                    resampler = torchaudio.transforms.Resample(orig_freq=orig_sr, new_freq=16000)
                                    prompt_speech = resampler(prompt_speech)

                                # 使用更短的prompt音频，避免padding问题
                                # 尝试不同的长度，找到合适的
                                for duration in [0.5, 0.8, 1.0, 1.2]:
                                    max_length = int(duration * 16000)
                                    if prompt_speech.shape[1] > max_length:
                                        test_prompt = prompt_speech[:, :max_length].squeeze()
                                    else:
                                        test_prompt = prompt_speech.squeeze()

                                    # 确保长度是合适的（避免padding问题）
                                    # 根据错误信息，我们需要确保音频长度满足模型要求
                                    if len(test_prompt) >= 8000:  # 至少0.5秒的16kHz音频
                                        prompt_speech_16k = test_prompt
                                        break
                                else:
                                    # 如果都不合适，使用原始音频的前0.5秒
                                    max_length = int(0.5 * 16000)
                                    prompt_speech_16k = prompt_speech[:, :max_length].squeeze()

                                instruct_text = "请用温柔的女声朗读这段文字。"

                                self.logger.info(f"使用instruct2模式，prompt音频长度: {len(prompt_speech_16k)}")
                                for i, result in enumerate(self.cosyvoice2.inference_instruct2(text, instruct_text, prompt_speech_16k, stream=False)):
                                    if 'tts_speech' in result:
                                        audio_data = result['tts_speech'].cpu().numpy()
                                        if audio_data.ndim > 1:
                                            audio_data = audio_data.squeeze()
                                        self.logger.info(f"Instruct2合成成功，音频长度: {len(audio_data)}")
                                        return audio_data.astype(np.float32)
                            else:
                                self.logger.warning(f"找不到prompt音频文件: {prompt_audio_path}")

                        except Exception as e:
                            self.logger.warning(f"Instruct2模式失败: {e}")

                            # 尝试使用cross_lingual模式（更简单）
                            try:
                                self.logger.info("尝试使用cross_lingual模式")

                                for i, result in enumerate(self.cosyvoice2.inference_cross_lingual(text, prompt_speech_16k, stream=False)):
                                    if 'tts_speech' in result:
                                        audio_data = result['tts_speech'].cpu().numpy()
                                        if audio_data.ndim > 1:
                                            audio_data = audio_data.squeeze()
                                        self.logger.info(f"Cross-lingual合成成功，音频长度: {len(audio_data)}")
                                        return audio_data.astype(np.float32)
                            except Exception as e2:
                                self.logger.warning(f"Cross-lingual模式失败: {e2}")

                                # 最后尝试zero-shot模式
                                try:
                                    self.logger.info("尝试使用zero-shot模式")
                                    prompt_text = "你好，这是一个测试。"  # 简单的提示文本

                                    for i, result in enumerate(self.cosyvoice2.inference_zero_shot(text, prompt_text, prompt_speech_16k, stream=False)):
                                        if 'tts_speech' in result:
                                            audio_data = result['tts_speech'].cpu().numpy()
                                            if audio_data.ndim > 1:
                                                audio_data = audio_data.squeeze()
                                            self.logger.info(f"Zero-shot合成成功，音频长度: {len(audio_data)}")
                                            return audio_data.astype(np.float32)
                                except Exception as e3:
                                    self.logger.warning(f"Zero-shot模式也失败: {e3}")
                                    pass

                except Exception as e:
                    # 如果SFT失败，尝试其他方法
                    self.logger.warning(f"SFT/Instruct2模式失败: {e}")
                    pass

            return None

        except Exception as e:
            self.logger.error(f"CosyVoice2合成异常: {e}")
            return None

    def _synthesize_cosyvoice(self, text: str, prompt_text: Optional[str] = None, prompt_speech_16k: Optional[np.ndarray] = None) -> Optional[np.ndarray]:
        """
        使用CosyVoice进行语音合成

        Args:
            text: 要合成的文本
            prompt_text: 提示文本
            prompt_speech_16k: 提示音频

        Returns:
            合成的音频数据
        """
        try:
            if prompt_text and prompt_speech_16k is not None:
                # Zero-shot合成
                for i, result in enumerate(self.cosyvoice.inference_zero_shot(text, prompt_text, prompt_speech_16k, stream=False)):
                    if 'tts_speech' in result:
                        audio_data = result['tts_speech'].cpu().numpy()
                        if audio_data.ndim > 1:
                            audio_data = audio_data.squeeze()
                        return audio_data.astype(np.float32)
            else:
                # 尝试使用SFT模式
                try:
                    for i, result in enumerate(self.cosyvoice.inference_sft(text, "中文女", stream=False)):
                        if 'tts_speech' in result:
                            audio_data = result['tts_speech'].cpu().numpy()
                            if audio_data.ndim > 1:
                                audio_data = audio_data.squeeze()
                            return audio_data.astype(np.float32)
                except:
                    # 如果SFT失败，尝试其他方法
                    pass

            return None

        except Exception as e:
            self.logger.error(f"CosyVoice合成异常: {e}")
            return None

    def _synthesize_fallback(self, text: str) -> Optional[np.ndarray]:
        """
        备用合成方案（生成静音音频）

        Args:
            text: 要合成的文本

        Returns:
            静音音频数据
        """
        try:
            # 处理None或空文本
            if text is None:
                text = ""

            # 根据文本长度生成对应时长的静音
            duration = max(1.0, len(text) * 0.1)  # 每个字符0.1秒
            samples = int(duration * self.sample_rate)

            # 生成静音音频
            silence = np.zeros(samples, dtype=np.float32)

            self.logger.info(f"使用备用TTS方案，生成{duration:.1f}秒静音")
            return silence

        except Exception as e:
            self.logger.error(f"备用TTS方案异常: {e}")
            return None

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_path": self.model_path,
            "model_type": self.model_type,
            "is_initialized": self.is_initialized,
            "sample_rate": self.sample_rate,
            "speed": self.speed,
            "volume": self.volume,
            "pitch": self.pitch,
            "device": self.device,
            "enable_fp16": self.enable_fp16,
            "synthesis_count": self.synthesis_count,
            "total_synthesis_time": self.total_synthesis_time,
            "last_synthesis_time": self.last_synthesis_time,
            "avg_synthesis_time": self.total_synthesis_time / max(1, self.synthesis_count)
        }

    def is_healthy(self) -> bool:
        """检查TTS模块健康状态"""
        return self.is_initialized and (self.cosyvoice is not None or self.cosyvoice2 is not None)

    def reset_stats(self):
        """重置性能统计"""
        self.synthesis_count = 0
        self.total_synthesis_time = 0.0
        self.last_synthesis_time = 0.0

    def set_speed(self, speed: float):
        """设置语速"""
        self.speed = max(0.5, min(2.0, speed))
        self.logger.info(f"TTS语速设置为: {self.speed}")

    def set_volume(self, volume: float):
        """设置音量"""
        self.volume = max(0.0, min(2.0, volume))
        self.logger.info(f"TTS音量设置为: {self.volume}")

    def set_pitch(self, pitch: float):
        """设置音调"""
        self.pitch = max(0.5, min(2.0, pitch))
        self.logger.info(f"TTS音调设置为: {self.pitch}")


# 兼容性函数
def tts_synthesize(text: str, config=None) -> Optional[np.ndarray]:
    """
    兼容性TTS合成函数

    Args:
        text: 要合成的文本
        config: 配置对象

    Returns:
        合成的音频数据
    """
    if config is None:
        # 使用默认配置
        from modules.config_manager import ConfigManager
        config = ConfigManager()

    processor = TTSProcessor(config)
    return processor.synthesize(text)


if __name__ == "__main__":
    # 测试代码
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))

    from modules.config_manager import ConfigManager

    # 初始化配置
    config = ConfigManager()

    # 创建TTS处理器
    tts = TTSProcessor(config)

    # 测试合成
    test_text = "你好，我是aibi语音助手，很高兴为您服务。"
    print(f"测试文本: {test_text}")

    audio_data = tts.synthesize(test_text)

    if audio_data is not None:
        print(f"合成成功！音频长度: {len(audio_data)} 采样点")
        print(f"音频时长: {len(audio_data) / tts.sample_rate:.2f} 秒")

        # 保存测试音频
        output_file = "test_tts_output.wav"
        sf.write(output_file, audio_data, tts.sample_rate)
        print(f"音频已保存到: {output_file}")
    else:
        print("合成失败！")

    # 显示模型信息
    print("\n模型信息:")
    model_info = tts.get_model_info()
    for key, value in model_info.items():
        print(f"  {key}: {value}")

    print(f"\n健康状态: {'正常' if tts.is_healthy() else '异常'}")
