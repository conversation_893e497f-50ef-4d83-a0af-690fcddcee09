#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - FunASR SenseVoiceSmall模型直接测试
直接测试FunASR框架和SenseVoiceSmall模型的功能
"""

import os
import sys
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_funasr_sensevoice():
    """直接测试FunASR和SenseVoiceSmall模型"""
    print("🧪 开始直接测试FunASR和SenseVoiceSmall模型...")
    
    try:
        # 导入FunASR
        from funasr import AutoModel
        
        print("✅ FunASR导入成功")
        
        # 检查模型路径
        model_dir = "./models/asr"
        if not os.path.exists(model_dir):
            print(f"❌ 模型目录不存在: {model_dir}")
            return False
        
        print(f"✅ 模型目录存在: {model_dir}")
        
        # 检查关键文件
        required_files = [
            "model.pt",
            "configuration.json", 
            "tokens.json",
            "chn_jpn_yue_eng_ko_spectok.bpe.model",
            "config.yaml"
        ]
        
        for file in required_files:
            file_path = os.path.join(model_dir, file)
            if not os.path.exists(file_path):
                print(f"❌ 缺少必需文件: {file}")
                return False
            print(f"✅ {file} 存在")
        
        # 加载模型
        print("\n📥 正在加载SenseVoiceSmall模型...")
        start_time = time.time()
        
        try:
            # 使用AutoModel加载模型
            model = AutoModel(
                model=model_dir,
                model_revision="v1.0.0",
                device="cpu"  # 先使用CPU测试
            )
            
            load_time = time.time() - start_time
            print(f"✅ 模型加载成功! 耗时: {load_time:.2f}秒")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
        
        # 测试示例音频
        print("\n🎵 测试示例音频文件...")
        example_dir = os.path.join(model_dir, "example")
        if os.path.exists(example_dir):
            audio_files = [f for f in os.listdir(example_dir) if f.endswith('.mp3')]
            print(f"找到 {len(audio_files)} 个示例音频文件")
            
            for audio_file in audio_files[:2]:  # 只测试前2个文件
                audio_path = os.path.join(example_dir, audio_file)
                print(f"\n🔊 测试音频: {audio_file}")
                
                try:
                    # 进行语音识别
                    start_time = time.time()
                    result = model.generate(input=audio_path)
                    inference_time = time.time() - start_time
                    
                    print(f"✅ 识别结果: {result}")
                    print(f"⏱️  推理时间: {inference_time:.2f}秒")
                    
                except Exception as e:
                    print(f"❌ 音频识别失败: {e}")
        
        # 测试流式识别
        print("\n🔄 测试流式识别功能...")
        try:
            # 创建流式识别器
            streaming_model = AutoModel(
                model=model_dir,
                model_revision="v1.0.0",
                device="cpu",
                streaming=True
            )
            print("✅ 流式识别器创建成功")
            
        except Exception as e:
            print(f"❌ 流式识别器创建失败: {e}")
        
        print("\n🎉 FunASR SenseVoiceSmall模型测试完成!")
        return True
        
    except ImportError as e:
        print(f"❌ FunASR导入失败: {e}")
        print("请确保已安装FunASR: pip install funasr")
        return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_model_info():
    """测试模型信息"""
    print("\n📋 模型信息测试...")
    
    model_dir = "./models/asr"
    
    # 读取配置文件
    config_file = os.path.join(model_dir, "config.yaml")
    if os.path.exists(config_file):
        import yaml
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("📊 模型配置信息:")
        print(f"  模型类型: {config.get('model_type', 'N/A')}")
        print(f"  词汇表大小: {config.get('vocab_size', 'N/A')}")
        print(f"  隐藏层大小: {config.get('hidden_size', 'N/A')}")
        print(f"  编码器层数: {config.get('encoder_layers', 'N/A')}")
    
    # 读取tokens.json
    tokens_file = os.path.join(model_dir, "tokens.json")
    if os.path.exists(tokens_file):
        import json
        with open(tokens_file, 'r', encoding='utf-8') as f:
            tokens = json.load(f)
        
        print(f"📝 词汇表信息:")
        print(f"  词汇表大小: {len(tokens)}")
        if isinstance(tokens, list):
            print(f"  前5个词汇: {tokens[:5]}")
        elif isinstance(tokens, dict):
            print(f"  前5个词汇: {list(tokens.keys())[:5]}")
        else:
            print(f"  词汇表类型: {type(tokens)}")

if __name__ == "__main__":
    print("🚀 aibi语音交互系统 - FunASR SenseVoiceSmall模型测试")
    print("=" * 60)
    
    # 测试模型信息
    test_model_info()
    
    # 测试FunASR模型
    success = test_funasr_sensevoice()
    
    if success:
        print("\n✅ 所有测试通过! SenseVoiceSmall模型可以正常使用")
    else:
        print("\n❌ 测试失败，请检查模型文件和FunASR安装")
    
    print("\n📝 测试完成!") 