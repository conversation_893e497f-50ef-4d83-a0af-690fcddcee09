*** cmudict-0.4.scm	Thu Jun 25 09:40:32 1998
--- cmudict-0.4-nopos.scm	Thu Jun 25 09:41:28 1998
***************
*** 39,45 ****
  ;; ## 
  ;; ## bob weide (<EMAIL>)
  ;; ##
! ("a" nil (ax))
  ("aaa" nil (t r ih1 p ax l ey1))
  ("aaberg" nil (aa1 b er0 g))
  ("aachen" nil (aa1 k ax n))
--- 39,46 ----
  ;; ## 
  ;; ## bob weide (<EMAIL>)
  ;; ##
! ("a" dt (ax))
! ("a" n (ey1))
  ("aaa" nil (t r ih1 p ax l ey1))
  ("aaberg" nil (aa1 b er0 g))
  ("aachen" nil (aa1 k ax n))
***************
*** 187,193 ****
  ("abiomed" nil (ey0 b iy1 ax m eh0 d))
  ("abitibi" nil (ae1 b ih0 t iy1 b iy0))
  ("abitz" nil (ae1 b ih0 t s))
! ("abject" nil (ae1 b jh eh0 k t))
  ("ablaze" nil (ax b l ey1 z))
  ("able" nil (ey1 b ax l))
  ("abler" nil (ey1 b ax l er0))
--- 188,195 ----
  ("abiomed" nil (ey0 b iy1 ax m eh0 d))
  ("abitibi" nil (ae1 b ih0 t iy1 b iy0))
  ("abitz" nil (ae1 b ih0 t s))
! ("abject" v (ae0 b jh eh1 k t))
! ("abject" n (ae1 b jh eh0 k t))
  ("ablaze" nil (ax b l ey1 z))
  ("able" nil (ey1 b ax l))
  ("abler" nil (ey1 b ax l er0))
***************
*** 279,285 ****
  ("abscess" nil (ae1 b s eh1 s))
  ("absence" nil (ae1 b s ax n s))
  ("absences" nil (ae1 b s ax n s ax z))
! ("absent" nil (ae1 b s ax n t))
  ("absentee" nil (ae1 b s ax n t iy1))
  ("absenteeism" nil (ae1 b s ax n t iy1 ih0 z ax m))
  ("absentees" nil (ae1 b s ax n t iy1 z))
--- 281,288 ----
  ("abscess" nil (ae1 b s eh1 s))
  ("absence" nil (ae1 b s ax n s))
  ("absences" nil (ae1 b s ax n s ax z))
! ("absent" j (ae1 b s ax n t))
! ("absent" v (ax0 b s eh1 n t))
  ("absentee" nil (ae1 b s ax n t iy1))
  ("absenteeism" nil (ae1 b s ax n t iy1 ih0 z ax m))
  ("absentees" nil (ae1 b s ax n t iy1 z))
***************
*** 315,325 ****
  ("abstinence" nil (ae1 b s t ax n ax n s))
  ("abstinent" nil (ae1 b s t ax n ax n t))
  ("abston" nil (ae1 b s t ax n))
! ("abstract" nil (ae0 b s t r ae1 k t))
  ("abstracted" nil (ae1 b s t r ae1 k t ih0 d))
  ("abstraction" nil (ae0 b s t r ae1 k sh ax n))
  ("abstractions" nil (ae0 b s t r ae1 k sh ax n z))
! ("abstracts" nil (ae1 b s t r ae0 k t s))
  ("abstruse" nil (ax b s t r uw1 s))
  ("absurd" nil (ax b s er1 d))
  ("absurdist" nil (ax b s er1 d ih0 s t))
--- 318,330 ----
  ("abstinence" nil (ae1 b s t ax n ax n s))
  ("abstinent" nil (ae1 b s t ax n ax n t))
  ("abston" nil (ae1 b s t ax n))
! ("abstract" v (ae0 b s t r ae1 k t))
! ("abstract" n (ae1 b s t r ae0 k t))
  ("abstracted" nil (ae1 b s t r ae1 k t ih0 d))
  ("abstraction" nil (ae0 b s t r ae1 k sh ax n))
  ("abstractions" nil (ae0 b s t r ae1 k sh ax n z))
! ("abstracts" n (ae1 b s t r ae0 k t s))
! ("abstracts" v (ax0 b s t r ae1 k t s))
  ("abstruse" nil (ax b s t r uw1 s))
  ("absurd" nil (ax b s er1 d))
  ("absurdist" nil (ax b s er1 d ih0 s t))
***************
*** 334,344 ****
  ("abundant" nil (ax b ah1 n d ax n t))
  ("abundantly" nil (ax b ah1 n d ax n t l iy0))
  ("aburto" nil (ax b uh1 r t ow1))
! ("abuse" nil (ax b y uw1 s))
  ("abused" nil (ax b y uw1 z d))
  ("abuser" nil (ax b y uw1 z er0))
  ("abusers" nil (ax b y uw1 z er0 z))
! ("abuses" nil (ax b y uw1 s ax z))
  ("abusing" nil (ax b y uw1 z ih0 ng))
  ("abusive" nil (ax b y uw1 s ih0 v))
  ("abut" nil (ax b ah1 t))
--- 339,351 ----
  ("abundant" nil (ax b ah1 n d ax n t))
  ("abundantly" nil (ax b ah1 n d ax n t l iy0))
  ("aburto" nil (ax b uh1 r t ow1))
! ("abuse" n (ax b y uw1 s))
! ("abuse" v (ax b y uw1 z))
  ("abused" nil (ax b y uw1 z d))
  ("abuser" nil (ax b y uw1 z er0))
  ("abusers" nil (ax b y uw1 z er0 z))
! ("abuses" n (ax b y uw1 s ax z))
! ("abuses" v (ax b y uw1 z ih0 z))
  ("abusing" nil (ax b y uw1 z ih0 ng))
  ("abusive" nil (ax b y uw1 s ih0 v))
  ("abut" nil (ax b ah1 t))
***************
*** 380,388 ****
  ("accelerator" nil (ae0 k s eh1 l er0 ey1 t er0))
  ("accelerometer" nil (ae0 k s eh1 l er0 aa1 m ax t er0))
  ("accelerometers" nil (ae0 k s eh1 l er0 aa1 m ax t er0 z))
! ("accent" nil (ax k s eh1 n t))
  ("accented" nil (ae1 k s eh0 n t ih0 d))
! ("accents" nil (ae1 k s eh0 n t s))
  ("accentuate" nil (ax k s eh1 n ch uw0 ey0 t))
  ("accentuated" nil (ae0 k s eh1 n ch ax w ey1 t ih0 d))
  ("accentuating" nil (ae0 k s eh1 n ch ax w ey1 t ih0 ng))
--- 387,397 ----
  ("accelerator" nil (ae0 k s eh1 l er0 ey1 t er0))
  ("accelerometer" nil (ae0 k s eh1 l er0 aa1 m ax t er0))
  ("accelerometers" nil (ae0 k s eh1 l er0 aa1 m ax t er0 z))
! ("accent" v (ae0 k s eh1 n t))
! ("accent" n (ae1 k s eh0 n t))
  ("accented" nil (ae1 k s eh0 n t ih0 d))
! ("accents" n (ae1 k s eh0 n t s))
! ("accents" v (ae0 k s eh1 n t s))
  ("accentuate" nil (ax k s eh1 n ch uw0 ey0 t))
  ("accentuated" nil (ae0 k s eh1 n ch ax w ey1 t ih0 d))
  ("accentuating" nil (ae0 k s eh1 n ch ax w ey1 t ih0 ng))
***************
*** 753,765 ****
  ("adder" nil (ae1 d er0))
  ("adderley" nil (ax d er1 l iy0))
  ("addicks" nil (ae1 d ih0 k s))
! ("addict" nil (ax d ih1 k t))
  ("addicted" nil (ax d ih1 k t ax d))
  ("addicting" nil (ax d ih1 k t ih0 ng))
  ("addiction" nil (ax d ih1 k sh ax n))
  ("addictions" nil (ax d ih1 k sh ax n z))
  ("addictive" nil (ax d ih1 k t ih0 v))
! ("addicts" nil (ax d ih1 k t s))
  ("addidas" nil (ax d iy1 d ax s))
  ("addie" nil (ae1 d iy0))
  ("adding" nil (ae1 d ih0 ng))
--- 762,776 ----
  ("adder" nil (ae1 d er0))
  ("adderley" nil (ax d er1 l iy0))
  ("addicks" nil (ae1 d ih0 k s))
! ("addict" v (ax d ih1 k t))
! ("addict" n (ah1 d ih0 k t))
  ("addicted" nil (ax d ih1 k t ax d))
  ("addicting" nil (ax d ih1 k t ih0 ng))
  ("addiction" nil (ax d ih1 k sh ax n))
  ("addictions" nil (ax d ih1 k sh ax n z))
  ("addictive" nil (ax d ih1 k t ih0 v))
! ("addicts" v (ax d ih1 k t s))
! ("addicts" n (ah1 d ih0 k t s))
  ("addidas" nil (ax d iy1 d ax s))
  ("addie" nil (ae1 d iy0))
  ("adding" nil (ae1 d ih0 ng))
***************
*** 774,780 ****
  ("additives" nil (ae1 d ax t ih0 v z))
  ("addled" nil (ae1 d ax l d))
  ("addleman" nil (ae1 d ax l m ax n))
! ("address" nil (ae1 d r eh1 s))
  ("addressable" nil (ax d r eh1 s ax b ax l))
  ("addressed" nil (ax d r eh1 s t))
  ("addressee" nil (ae1 d r eh0 s iy1))
--- 785,792 ----
  ("additives" nil (ae1 d ax t ih0 v z))
  ("addled" nil (ae1 d ax l d))
  ("addleman" nil (ae1 d ax l m ax n))
! ("address" v (ae1 d r eh1 s))
! ("address" n (ax d r eh1 s))
  ("addressable" nil (ax d r eh1 s ax b ax l))
  ("addressed" nil (ax d r eh1 s t))
  ("addressee" nil (ae1 d r eh0 s iy1))
***************
*** 1051,1059 ****
  ("advisory" nil (ae0 d v ay1 z er0 iy0))
  ("advo" nil (ae1 d v ow0))
  ("advocacy" nil (ae1 d v ax k ax s iy0))
! ("advocate" nil (ae1 d v ax k ax t))
  ("advocated" nil (ae1 d v ax k ey1 t ax d))
! ("advocates" nil (ae1 d v ax k ax t s))
  ("advocating" nil (ae1 d v ax k ey1 t ih0 ng))
  ("advocation" nil (ae1 d v ax k ey1 sh ax n))
  ("adweek" nil (ae1 d w iy0 k))
--- 1063,1073 ----
  ("advisory" nil (ae0 d v ay1 z er0 iy0))
  ("advo" nil (ae1 d v ow0))
  ("advocacy" nil (ae1 d v ax k ax s iy0))
! ("advocate" n (ae1 d v ax k ax t))
! ("advocate" v (ae1 d v ax k ey1 t))
  ("advocated" nil (ae1 d v ax k ey1 t ax d))
! ("advocates" n (ae1 d v ax k ax t s))
! ("advocates" v (ae1 d v ax k ey1 t s))
  ("advocating" nil (ae1 d v ax k ey1 t ih0 ng))
  ("advocation" nil (ae1 d v ax k ey1 sh ax n))
  ("adweek" nil (ae1 d w iy0 k))
***************
*** 1139,1147 ****
  ("affirmed" nil (ax f er1 m d))
  ("affirming" nil (ax f er1 m ih0 ng))
  ("affirms" nil (ax f er1 m z))
! ("affix" nil (ae1 f ih0 k s))
  ("affixed" nil (ax f ih1 k s t))
! ("affixes" nil (ae1 f ih0 k s ih0 z))
  ("affixing" nil (ax f ih1 k s ih0 ng))
  ("affleck" nil (ae1 f l ih0 k))
  ("afflerbach" nil (ae1 f l er0 b aa1 k))
--- 1153,1163 ----
  ("affirmed" nil (ax f er1 m d))
  ("affirming" nil (ax f er1 m ih0 ng))
  ("affirms" nil (ax f er1 m z))
! ("affix" n (ae1 f ih0 k s))
! ("affix" v (ae0 f ih1 k s))
  ("affixed" nil (ax f ih1 k s t))
! ("affixes" vl (ax f ih1 k s ih0 z))
! ("affixes" n (ah1 f ih0 k s ih0 z))
  ("affixing" nil (ax f ih1 k s ih0 ng))
  ("affleck" nil (ae1 f l ih0 k))
  ("afflerbach" nil (ae1 f l er0 b aa1 k))
***************
*** 1237,1243 ****
  ("agatha" nil (ae1 g ax th ax))
  ("agco" nil (ae1 g k ow1))
  ("age" nil (ey1 jh))
! ("aged" nil (ey1 jh d))
  ("agee" nil (ey1 jh iy1))
  ("ageless" nil (ey1 jh l ax s))
  ("agence" nil (ae1 jh ax n s))
--- 1253,1260 ----
  ("agatha" nil (ae1 g ax th ax))
  ("agco" nil (ae1 g k ow1))
  ("age" nil (ey1 jh))
! ("aged" v (ey1 jh d))
! ("aged" j (ey1 jh ih0 d))
  ("agee" nil (ey1 jh iy1))
  ("ageless" nil (ey1 jh l ax s))
  ("agence" nil (ae1 jh ax n s))
***************
*** 1265,1273 ****
  ("aggravates" nil (ae1 g r ax v ey1 t s))
  ("aggravating" nil (ae1 g r ax v ey1 t ih0 ng))
  ("aggravation" nil (ae1 g r ax v ey1 sh ax n))
! ("aggregate" nil (ae1 g r ax g ax t))
  ("aggregated" nil (ae1 g r ax g ey1 t ax d))
! ("aggregates" nil (ae1 g r ax g ih0 t s))
  ("aggression" nil (ax g r eh1 sh ax n))
  ("aggressions" nil (ax g r eh1 sh ax n z))
  ("aggressive" nil (ax g r eh1 s ih0 v))
--- 1282,1292 ----
  ("aggravates" nil (ae1 g r ax v ey1 t s))
  ("aggravating" nil (ae1 g r ax v ey1 t ih0 ng))
  ("aggravation" nil (ae1 g r ax v ey1 sh ax n))
! ("aggregate" v (ae1 g r ax g ax t))
! ("aggregate" n (ae1 g r ax g ih0 t))
  ("aggregated" nil (ae1 g r ax g ey1 t ax d))
! ("aggregates" n (ae1 g r ax g ih0 t s))
! ("aggregates" v (ae1 g r ax g ax t s))
  ("aggression" nil (ax g r eh1 sh ax n))
  ("aggressions" nil (ax g r eh1 sh ax n z))
  ("aggressive" nil (ax g r eh1 s ih0 v))
***************
*** 2054,2060 ****
  ("allie" nil (ae1 l iy0))
  ("allied" nil (ax l ay1 d))
  ("alliedsignal" nil (ae1 l ay1 d s ih1 g n ax l))
! ("allies" nil (ae1 l ay0 z))
  ("alligator" nil (ae1 l ax g ey1 t er0))
  ("alligators" nil (ae1 l ax g ey1 t er0 z))
  ("alligood" nil (ae1 l ih0 g uh1 d))
--- 2073,2080 ----
  ("allie" nil (ae1 l iy0))
  ("allied" nil (ax l ay1 d))
  ("alliedsignal" nil (ae1 l ay1 d s ih1 g n ax l))
! ("allies" n (ae1 l ay0 z))
! ("allies" v (ax l ay1 z))
  ("alligator" nil (ae1 l ax g ey1 t er0))
  ("alligators" nil (ae1 l ax g ey1 t er0 z))
  ("alligood" nil (ae1 l ih0 g uh1 d))
***************
*** 2140,2146 ****
  ("allwaste" nil (ao1 l w ey1 s t))
  ("allways" nil (ao1 l w ey1 z))
  ("allweiss" nil (aa1 l w iy1 s))
! ("ally" nil (ae1 l ay0))
  ("allying" nil (ae1 l ay0 ih0 ng))
  ("allyn" nil (ae1 l ih0 n))
  ("allys" nil (ae1 l ay0 z))
--- 2160,2167 ----
  ("allwaste" nil (ao1 l w ey1 s t))
  ("allways" nil (ao1 l w ey1 z))
  ("allweiss" nil (aa1 l w iy1 s))
! ("ally" n (ae1 l ay0))
! ("ally" v (ax l ay1))
  ("allying" nil (ae1 l ay0 ih0 ng))
  ("allyn" nil (ae1 l ih0 n))
  ("allys" nil (ae1 l ay0 z))
***************
*** 2278,2284 ****
  ("altering" nil (ao1 l t er0 ih0 ng))
  ("alterman" nil (ao1 l t er0 m ax n))
  ("alternacare" nil (ao0 l t er1 n ax k eh1 r))
! ("alternate" nil (ao1 l t er0 n ax t))
  ("alternated" nil (ao1 l t er0 n ey1 t ax d))
  ("alternately" nil (ao1 l t er0 n ax t l iy0))
  ("alternates" nil (ao1 l t er0 n ey1 t s))
--- 2299,2306 ----
  ("altering" nil (ao1 l t er0 ih0 ng))
  ("alterman" nil (ao1 l t er0 m ax n))
  ("alternacare" nil (ao0 l t er1 n ax k eh1 r))
! ("alternate" j (ao1 l t er0 n ax t))
! ("alternate" v (ao1 l t er0 n ey1 t))
  ("alternated" nil (ao1 l t er0 n ey1 t ax d))
  ("alternately" nil (ao1 l t er0 n ax t l iy0))
  ("alternates" nil (ao1 l t er0 n ey1 t s))
***************
*** 2804,2810 ****
  ("analogous" nil (ax n ae1 l ax g ax s))
  ("analogue" nil (ae1 n ax l ao1 g))
  ("analogy" nil (ax n ae1 l ax jh iy0))
! ("analyses" nil (ax n ae1 l ax s iy1 z))
  ("analysis" nil (ax n ae1 l ax s ax s))
  ("analyst" nil (ae1 n ax l ax s t))
  ("analysts" nil (ae1 n ax l ax s t s))
--- 2826,2833 ----
  ("analogous" nil (ax n ae1 l ax g ax s))
  ("analogue" nil (ae1 n ax l ao1 g))
  ("analogy" nil (ax n ae1 l ax jh iy0))
! ("analyses" n (ax n ae1 l ax s iy0 z))
! ("analyses" v (ah1 n ae0 l ay s ih0 z))
  ("analysis" nil (ax n ae1 l ax s ax s))
  ("analyst" nil (ae1 n ax l ax s t))
  ("analysts" nil (ae1 n ax l ax s t s))
***************
*** 3078,3084 ****
  ("aniline" nil (ae1 n ax l iy1 n))
  ("animal" nil (ae1 n ax m ax l))
  ("animals" nil (ae1 n ax m ax l z))
! ("animate" nil (ae1 n ax m ax t))
  ("animated" nil (ae1 n ax m ey1 t ax d))
  ("animates" nil (ae1 n ax m ax t s))
  ("animation" nil (ae1 n ax m ey1 sh ax n))
--- 3101,3108 ----
  ("aniline" nil (ae1 n ax l iy1 n))
  ("animal" nil (ae1 n ax m ax l))
  ("animals" nil (ae1 n ax m ax l z))
! ("animate" j (ae1 n ax m ax t))
! ("animate" v (ae1 n ax m ey1 t))
  ("animated" nil (ae1 n ax m ey1 t ax d))
  ("animates" nil (ae1 n ax m ax t s))
  ("animation" nil (ae1 n ax m ey1 sh ax n))
***************
*** 3131,3139 ****
  ("annese" nil (aa0 n ey1 z iy0))
  ("annett" nil (ax n eh1 t))
  ("annette" nil (ax n eh1 t))
! ("annex" nil (ae1 n eh1 k s))
  ("annexation" nil (ae1 n eh0 k s ey1 sh ax n))
  ("annexed" nil (ae1 n eh0 k s t))
  ("annexing" nil (ax n eh1 k s ih0 ng))
  ("annick" nil (ae1 n ih0 k))
  ("annie" nil (ae1 n iy0))
--- 3155,3166 ----
  ("annese" nil (aa0 n ey1 z iy0))
  ("annett" nil (ax n eh1 t))
  ("annette" nil (ax n eh1 t))
! ("annex" n (ae1 n eh0 k s))
! ("annex" v (ax n eh1 k s))
  ("annexation" nil (ae1 n eh0 k s ey1 sh ax n))
  ("annexed" nil (ae1 n eh0 k s t))
+ ("annexes" n (ae1 n eh0 k s ih0 z))
+ ("annexes" v (ax n eh1 k s ih0 z))
  ("annexing" nil (ax n eh1 k s ih0 ng))
  ("annick" nil (ae1 n ih0 k))
  ("annie" nil (ae1 n iy0))
***************
*** 3651,3660 ****
  ("approach" nil (ax p r ow1 ch))
  ("approachable" nil (ax p r ow1 ch ax b ax l))
  ("approached" nil (ax p r ow1 ch t))
! ("approaches" nil (ax p r ow1 ch ax z))
  ("approaching" nil (ax p r ow1 ch ih0 ng))
  ("approbation" nil (ae1 p r ax b ey1 sh ax n))
- ("appropriate" nil (ax p r ow1 p r iy0 ax t))
  ("appropriated" nil (ax p r ow1 p r iy0 ey1 t ih0 d))
  ("appropriately" nil (ax p r ow1 p r iy0 ih0 t l iy0))
  ("appropriateness" nil (ax p r ow1 p r iy0 ax t n ax s))
--- 3678,3687 ----
  ("approach" nil (ax p r ow1 ch))
  ("approachable" nil (ax p r ow1 ch ax b ax l))
  ("approached" nil (ax p r ow1 ch t))
! ("appropriate" j (ax p r ow1 p r iy0 ax t))
! ("appropriate" v (ax p r ow1 p r iy0 ey0 t))
  ("approaching" nil (ax p r ow1 ch ih0 ng))
  ("approbation" nil (ae1 p r ax b ey1 sh ax n))
  ("appropriated" nil (ax p r ow1 p r iy0 ey1 t ih0 d))
  ("appropriately" nil (ax p r ow1 p r iy0 ih0 t l iy0))
  ("appropriateness" nil (ax p r ow1 p r iy0 ax t n ax s))
***************
*** 3671,3677 ****
  ("approves" nil (ax p r uw1 v z))
  ("approving" nil (ax p r uw1 v ih0 ng))
  ("approvingly" nil (ax p r uw1 v ih0 ng l iy0))
! ("approximate" nil (ax p r aa1 k s ax m ax t))
  ("approximated" nil (ax p r aa1 k s ax m ey1 t ax d))
  ("approximately" nil (ax p r aa1 k s ax m ax t l iy0))
  ("approximates" nil (ax p r aa1 k s ax m ax t s))
--- 3698,3705 ----
  ("approves" nil (ax p r uw1 v z))
  ("approving" nil (ax p r uw1 v ih0 ng))
  ("approvingly" nil (ax p r uw1 v ih0 ng l iy0))
! ("approximate" j (ax p r aa1 k s ax m ax t))
! ("approximate" v (ax p r aa1 k s ax m ey0 t))
  ("approximated" nil (ax p r aa1 k s ax m ey1 t ax d))
  ("approximately" nil (ax p r aa1 k s ax m ax t l iy0))
  ("approximates" nil (ax p r aa1 k s ax m ax t s))
***************
*** 4290,4296 ****
  ("artichokes" nil (aa1 r t ih0 ch ow1 k s))
  ("article" nil (aa1 r t ax k ax l))
  ("articles" nil (aa1 r t ax k ax l z))
! ("articulate" nil (aa0 r t ih1 k y ax l ey1 t))
  ("articulated" nil (aa0 r t ih1 k y ax l ey1 t ax d))
  ("articulates" nil (aa0 r t ih1 k y ax l ax t s))
  ("articulating" nil (aa0 r t ih1 k y ax l ey1 t ih0 ng))
--- 4318,4325 ----
  ("artichokes" nil (aa1 r t ih0 ch ow1 k s))
  ("article" nil (aa1 r t ax k ax l))
  ("articles" nil (aa1 r t ax k ax l z))
! ("articulate" v (aa0 r t ih1 k y ax l ey1 t))
! ("articulate" j (aa0 r t ih1 k y ax l ax t))
  ("articulated" nil (aa0 r t ih1 k y ax l ey1 t ax d))
  ("articulates" nil (aa0 r t ih1 k y ax l ax t s))
  ("articulating" nil (aa0 r t ih1 k y ax l ey1 t ih0 ng))
***************
*** 4500,4505 ****
--- 4529,4536 ----
  ("aspinall" nil (ae1 s p ih0 n ao0 l))
  ("aspirant" nil (ae1 s p er0 ax n t))
  ("aspirants" nil (ae1 s p er0 ax n t s))
+ ("aspirate" n (ae1 s p er0 ax t))
+ ("aspirate" v (ae1 s p er0 ey1 t))
  ("aspiration" nil (ae1 s p er0 ey1 sh ax n))
  ("aspirations" nil (ae1 s p er0 ey1 sh ax n z))
  ("aspire" nil (ax s p ay1 r))
***************
*** 4588,4596 ****
  ("assisting" nil (ax s ih1 s t ih0 ng))
  ("assists" nil (ax s ih1 s t s))
  ("assocation" nil (ae1 s ax k ey1 sh ax n))
! ("associate" nil (ax s ow1 s iy0 ax t))
  ("associated" nil (ax s ow1 s iy0 ey1 t ax d))
! ("associates" nil (ax s ow1 s iy0 ax t s))
  ("associating" nil (ax s ow1 s iy0 ey1 t ih0 ng))
  ("association" nil (ax s ow1 s iy0 ey1 sh ax n))
  ("associations" nil (ax s ow1 s iy0 ey1 sh ax n z))
--- 4619,4629 ----
  ("assisting" nil (ax s ih1 s t ih0 ng))
  ("assists" nil (ax s ih1 s t s))
  ("assocation" nil (ae1 s ax k ey1 sh ax n))
! ("associate" n (ax s ow1 s iy0 ax t))
! ("associate" v (ax s ow1 s iy0 ey0 t))
  ("associated" nil (ax s ow1 s iy0 ey1 t ax d))
! ("associates" n (ax s ow1 s iy0 ih0 t s))
! ("associates" v (ax s ow1 s iy0 ey0 t s))
  ("associating" nil (ax s ow1 s iy0 ey1 t ih0 ng))
  ("association" nil (ax s ow1 s iy0 ey1 sh ax n))
  ("associations" nil (ax s ow1 s iy0 ey1 sh ax n z))
***************
*** 4867,4875 ****
  ("attractiveness" nil (ax t r ae1 k t ih0 v n ax s))
  ("attracts" nil (ax t r ae1 k t s))
  ("attributable" nil (ax t r ih1 b y ax t ax b ax l))
! ("attribute" nil (ae1 t r ax b y uw1 t))
  ("attributed" nil (ax t r ih1 b y ax t ax d))
! ("attributes" nil (ae1 t r ax b y uw1 t s))
  ("attributing" nil (ax t r ih1 b y ax t ih0 ng))
  ("attribution" nil (ae1 t r ih0 b y uw1 sh ax n))
  ("attridge" nil (ax t r ih1 jh))
--- 4900,4910 ----
  ("attractiveness" nil (ax t r ae1 k t ih0 v n ax s))
  ("attracts" nil (ax t r ae1 k t s))
  ("attributable" nil (ax t r ih1 b y ax t ax b ax l))
! ("attribute" n (ae1 t r ax b y uw1 t))
! ("attribute" v (ax t r ih1 b y uw1 t))
  ("attributed" nil (ax t r ih1 b y ax t ax d))
! ("attributes" n (ae1 t r ax b y uw1 t s))
! ("attributes" v (ax t r ih1 b y uw1 t s))
  ("attributing" nil (ax t r ih1 b y ax t ih0 ng))
  ("attribution" nil (ae1 t r ih0 b y uw1 sh ax n))
  ("attridge" nil (ax t r ih1 jh))
***************
*** 5314,5320 ****
  ("axelrod" nil (ae1 k s ax l r aa1 d))
  ("axelsen" nil (ae0 k s eh1 l s ax n))
  ("axelson" nil (ae1 k s ih0 l s ax n))
! ("axes" nil (ae1 k s ih0 z))
  ("axford" nil (ae0 k s f ao1 r d))
  ("axid" nil (ae1 k s ih0 d))
  ("axilrod" nil (ae1 k s ih0 l r aa1 d))
--- 5349,5356 ----
  ("axelrod" nil (ae1 k s ax l r aa1 d))
  ("axelsen" nil (ae0 k s eh1 l s ax n))
  ("axelson" nil (ae1 k s ih0 l s ax n))
! ("axes" v (ae1 k s ih0 z))
! ("axes" n (ae1 k s iy0 z))
  ("axford" nil (ae0 k s f ao1 r d))
  ("axid" nil (ae1 k s ih0 d))
  ("axilrod" nil (ae1 k s ih0 l r aa1 d))
***************
*** 6790,6796 ****
  ("bathrobes" nil (b ae1 th r ow1 b z))
  ("bathroom" nil (b ae1 th r uw1 m))
  ("bathrooms" nil (b ae1 th r uw1 m z))
! ("baths" nil (b ae1 th s))
  ("bathsheba" nil (b ae0 th sh iy1 b ax))
  ("bathtub" nil (b ae1 th t ax b))
  ("bathtubs" nil (b ae1 th t ah1 b z))
--- 6826,6833 ----
  ("bathrobes" nil (b ae1 th r ow1 b z))
  ("bathroom" nil (b ae1 th r uw1 m))
  ("bathrooms" nil (b ae1 th r uw1 m z))
! ("baths" v (b ae1 th z))
! ("baths" n (b ae1 th s))
  ("bathsheba" nil (b ae0 th sh iy1 b ax))
  ("bathtub" nil (b ae1 th t ax b))
  ("bathtubs" nil (b ae1 th t ah1 b z))
***************
*** 9327,9333 ****
  ("blepharisma" nil (b l eh1 f er0 ih1 z m ax))
  ("bleser" nil (b l iy1 z er0))
  ("bless" nil (b l eh1 s))
! ("blessed" nil (b l eh1 s t))
  ("blesses" nil (b l eh1 s ih0 z))
  ("blessing" nil (b l eh1 s ih0 ng))
  ("blessinger" nil (b l eh1 s ih0 ng er0))
--- 9364,9371 ----
  ("blepharisma" nil (b l eh1 f er0 ih1 z m ax))
  ("bleser" nil (b l iy1 z er0))
  ("bless" nil (b l eh1 s))
! ("blessed" v (b l eh1 s t))
! ("blessed" j (b l eh1 s ih d))
  ("blesses" nil (b l eh1 s ih0 z))
  ("blessing" nil (b l eh1 s ih0 ng))
  ("blessinger" nil (b l eh1 s ih0 ng er0))
***************
*** 15349,15355 ****
  ("certainteed" nil (s er1 t ax n t iy1 d))
  ("certainties" nil (s er1 t ax n t iy0 z))
  ("certainty" nil (s er1 t ax n t iy0))
! ("certificate" nil (s er0 t ih1 f ih0 k ax t))
  ("certificates" nil (s er0 t ih1 f ih0 k ax t s))
  ("certification" nil (s er1 t ax f ax k ey1 sh ax n))
  ("certifications" nil (s er1 t ax f ax k ey1 sh ax n z))
--- 15387,15394 ----
  ("certainteed" nil (s er1 t ax n t iy1 d))
  ("certainties" nil (s er1 t ax n t iy0 z))
  ("certainty" nil (s er1 t ax n t iy0))
! ("certificate" n (s er1 t ah1 f ax k ax t))
! ("certificate" v (s er1 t ah1 f ax k ey0 t))
  ("certificates" nil (s er0 t ih1 f ih0 k ax t s))
  ("certification" nil (s er1 t ax f ax k ey1 sh ax n))
  ("certifications" nil (s er1 t ax f ax k ey1 sh ax n z))
***************
*** 17242,17248 ****
  ("clorinda" nil (k l ao0 r iy1 n d ax))
  ("clorox" nil (k l ao1 r aa0 k s))
  ("clos" nil (k l aa1 s))
! ("close" nil (k l ow1 s))
  ("closed" nil (k l ow1 z d))
  ("closedown" nil (k l ow1 z d aw1 n))
  ("closedowns" nil (k l ow1 z d aw1 n z))
--- 17281,17288 ----
  ("clorinda" nil (k l ao0 r iy1 n d ax))
  ("clorox" nil (k l ao1 r aa0 k s))
  ("clos" nil (k l aa1 s))
! ("close" n (k l ow1 s))
! ("close" v (k l ow1 z))
  ("closed" nil (k l ow1 z d))
  ("closedown" nil (k l ow1 z d aw1 n))
  ("closedowns" nil (k l ow1 z d aw1 n z))
***************
*** 17791,17797 ****
  ("colle" nil (k ow1 l))
  ("colleague" nil (k aa1 l iy0 g))
  ("colleagues" nil (k aa1 l iy0 g z))
! ("collect" nil (k ax l eh1 k t))
  ("collected" nil (k ax l eh1 k t ax d))
  ("collectibility" nil (k ax l eh1 k t ih0 b ih1 l ih0 t iy0))
  ("collectible" nil (k ax l eh1 k t ax b ax l))
--- 17831,17838 ----
  ("colle" nil (k ow1 l))
  ("colleague" nil (k aa1 l iy0 g))
  ("colleagues" nil (k aa1 l iy0 g z))
! ("collect" n (k ah1 l eh k t))
! ("collect" v (k ax l eh1 k t))
  ("collected" nil (k ax l eh1 k t ax d))
  ("collectibility" nil (k ax l eh1 k t ih0 b ih1 l ih0 t iy0))
  ("collectible" nil (k ax l eh1 k t ax b ax l))
***************
*** 18008,18016 ****
  ("combinable" nil (k ax m b ay1 n ax b ax l))
  ("combination" nil (k aa1 m b ax n ey1 sh ax n))
  ("combinations" nil (k aa1 m b ax n ey1 sh ax n z))
! ("combine" nil (k aa1 m b ay0 n))
  ("combined" nil (k ax m b ay1 n d))
! ("combines" nil (k ax m b ay1 n z))
  ("combing" nil (k ow1 m ih0 ng))
  ("combining" nil (k ax m b ay1 n ih0 ng))
  ("combo" nil (k aa1 m b ow1))
--- 18049,18059 ----
  ("combinable" nil (k ax m b ay1 n ax b ax l))
  ("combination" nil (k aa1 m b ax n ey1 sh ax n))
  ("combinations" nil (k aa1 m b ax n ey1 sh ax n z))
! ("combine" n (k aa1 m b ay0 n))
! ("combine" v (k ax m b ay1 n))
  ("combined" nil (k ax m b ay1 n d))
! ("combines" n (k ah1 m b ay0 n z))
! ("combines" v (k ax m b ay1 n z))
  ("combing" nil (k ow1 m ih0 ng))
  ("combining" nil (k ax m b ay1 n ih0 ng))
  ("combo" nil (k aa1 m b ow1))
***************
*** 18205,18215 ****
  ("commuting" nil (k ax m y uw1 t ih0 ng))
  ("como" nil (k ow1 m ow0))
  ("comp" nil (k aa1 m p))
! ("compact" nil (k aa1 m p ae0 k t))
  ("compacted" nil (k ax m p ae1 k t ih0 d))
  ("compactness" nil (k ax m p ae1 k t n ax s))
  ("compactor" nil (k ax m p ae1 k t er0))
! ("compacts" nil (k ax m p ae1 k t s))
  ("compagnie" nil (k ah1 m p ax n iy0))
  ("companhia" nil (k ax m p ae1 n hh iy0 ax))
  ("compania" nil (k ax m p ey1 n iy0 ax))
--- 18248,18260 ----
  ("commuting" nil (k ax m y uw1 t ih0 ng))
  ("como" nil (k ow1 m ow0))
  ("comp" nil (k aa1 m p))
! ("compact" n (k aa1 m p ae0 k t))
! ("compact" v (k ax m p ae1 k t))
  ("compacted" nil (k ax m p ae1 k t ih0 d))
  ("compactness" nil (k ax m p ae1 k t n ax s))
  ("compactor" nil (k ax m p ae1 k t er0))
! ("compacts" n (k ah1 m p ae0 k t s))
! ("compacts" v (k ax m p ae1 k t s))
  ("compagnie" nil (k ah1 m p ax n iy0))
  ("companhia" nil (k ax m p ae1 n hh iy0 ax))
  ("compania" nil (k ax m p ey1 n iy0 ax))
***************
*** 18327,18335 ****
  ("complicity" nil (k ax m p l ih1 s ax t iy0))
  ("complied" nil (k ax m p l ay1 d))
  ("complies" nil (k ax m p l ay1 z))
! ("compliment" nil (k aa1 m p l ax m ax n t))
  ("complimentary" nil (k aa1 m p l ax m eh1 n t er0 iy0))
! ("compliments" nil (k aa1 m p l ax m ax n t s))
  ("comply" nil (k ax m p l ay1))
  ("complying" nil (k ax m p l ay1 ih0 ng))
  ("compo" nil (k aa1 m p ow1))
--- 18372,18382 ----
  ("complicity" nil (k ax m p l ih1 s ax t iy0))
  ("complied" nil (k ax m p l ay1 d))
  ("complies" nil (k ax m p l ay1 z))
! ("compliment" n (k aa1 m p l ax m ax n t))
! ("compliment" v (k aa1 m p l ax m eh0 n t))
  ("complimentary" nil (k aa1 m p l ax m eh1 n t er0 iy0))
! ("compliments" n (k aa1 m p l ax m ax n t s))
! ("compliments" v (k aa1 m p l ax m eh0 n t s))
  ("comply" nil (k ax m p l ay1))
  ("complying" nil (k ax m p l ay1 ih0 ng))
  ("compo" nil (k aa1 m p ow1))
***************
*** 18351,18360 ****
  ("composting" nil (k aa1 m p ow1 s t ih0 ng))
  ("composure" nil (k ax m p ow1 zh er0))
  ("compote" nil (k aa1 m p ow0 t))
! ("compound" nil (k aa1 m p aw0 n d))
  ("compounded" nil (k ax m p aw1 n d ax d))
  ("compounding" nil (k ax m p aw1 n d ih0 ng))
! ("compounds" nil (k aa1 m p aw0 n d z))
  ("comprehend" nil (k aa1 m p r iy0 hh eh1 n d))
  ("comprehended" nil (k aa1 m p r ih0 hh eh1 n d ih0 d))
  ("comprehending" nil (k aa1 m p r ih0 hh eh1 n d ih0 ng))
--- 18398,18409 ----
  ("composting" nil (k aa1 m p ow1 s t ih0 ng))
  ("composure" nil (k ax m p ow1 zh er0))
  ("compote" nil (k aa1 m p ow0 t))
! ("compound" n (k aa1 m p aw0 n d))
! ("compound" v (k ax m p aw1 n d))
  ("compounded" nil (k ax m p aw1 n d ax d))
  ("compounding" nil (k ax m p aw1 n d ih0 ng))
! ("compounds" n (k aa1 m p aw0 n d z))
! ("compounds" v (k ax m p aw1 n d z))
  ("comprehend" nil (k aa1 m p r iy0 hh eh1 n d))
  ("comprehended" nil (k aa1 m p r ih0 hh eh1 n d ih0 d))
  ("comprehending" nil (k aa1 m p r ih0 hh eh1 n d ih0 ng))
***************
*** 18363,18371 ****
  ("comprehension" nil (k aa1 m p r iy0 hh eh1 n sh ax n))
  ("comprehensive" nil (k aa1 m p r iy0 hh eh1 n s ih0 v))
  ("comprehensively" nil (k aa1 m p r ih0 hh eh1 n s ih0 v l iy0))
! ("compress" nil (k aa1 m p r eh0 s))
  ("compressed" nil (k ax m p r eh1 s t))
! ("compresses" nil (k aa1 m p r eh0 s ax z))
  ("compressing" nil (k ax m p r eh1 s ih0 ng))
  ("compression" nil (k ax m p r eh1 sh ax n))
  ("compressor" nil (k ax m p r eh1 s er0))
--- 18412,18422 ----
  ("comprehension" nil (k aa1 m p r iy0 hh eh1 n sh ax n))
  ("comprehensive" nil (k aa1 m p r iy0 hh eh1 n s ih0 v))
  ("comprehensively" nil (k aa1 m p r ih0 hh eh1 n s ih0 v l iy0))
! ("compress" n (k aa1 m p r eh0 s))
! ("compress" v (k ax m p r eh1 s))
  ("compressed" nil (k ax m p r eh1 s t))
! ("compresses" n (k aa1 m p r eh0 s ax z))
! ("compresses" v (k ax m p r eh1 s ax z))
  ("compressing" nil (k ax m p r eh1 s ih0 ng))
  ("compression" nil (k ax m p r eh1 sh ax n))
  ("compressor" nil (k ax m p r eh1 s er0))
***************
*** 18480,18492 ****
  ("concerned" nil (k ax n s er1 n d))
  ("concerning" nil (k ax n s er1 n ih0 ng))
  ("concerns" nil (k ax n s er1 n z))
! ("concert" nil (k aa1 n s er0 t))
  ("concerted" nil (k ax n s er1 t ax d))
  ("concerti" nil (k ax n ch eh1 r t iy0))
  ("concertmaster" nil (k aa1 n s er0 t m ae1 s t er0))
  ("concerto" nil (k ax n ch eh1 r t ow0))
  ("concertos" nil (k ax n ch eh1 r t ow0 z))
! ("concerts" nil (k aa1 n s er0 t s))
  ("concession" nil (k ax n s eh1 sh ax n))
  ("concessionaire" nil (k ax n s eh1 sh ax n eh1 r))
  ("concessional" nil (k ax n s eh1 sh ax n ax l))
--- 18531,18545 ----
  ("concerned" nil (k ax n s er1 n d))
  ("concerning" nil (k ax n s er1 n ih0 ng))
  ("concerns" nil (k ax n s er1 n z))
! ("concert" n (k aa1 n s er0 t))
! ("concert" v (k aa0 n s er1 t))
  ("concerted" nil (k ax n s er1 t ax d))
  ("concerti" nil (k ax n ch eh1 r t iy0))
  ("concertmaster" nil (k aa1 n s er0 t m ae1 s t er0))
  ("concerto" nil (k ax n ch eh1 r t ow0))
  ("concertos" nil (k ax n ch eh1 r t ow0 z))
! ("concerts" n (k aa1 n s er0 t s))
! ("concerts" v (k ax n s er1 t s))
  ("concession" nil (k ax n s eh1 sh ax n))
  ("concessionaire" nil (k ax n s eh1 sh ax n eh1 r))
  ("concessional" nil (k ax n s eh1 sh ax n ax l))
***************
*** 18587,18593 ****
  ("condron" nil (k aa1 n d r ax n))
  ("condry" nil (k aa1 n d er0 iy0))
  ("conducive" nil (k ax n d uw1 s ih0 v))
! ("conduct" nil (k ax n d ah1 k t))
  ("conducted" nil (k ax n d ah1 k t ax d))
  ("conducting" nil (k ax n d ah1 k t ih0 ng))
  ("conduction" nil (k ax n d ah1 k sh ax n))
--- 18640,18647 ----
  ("condron" nil (k aa1 n d r ax n))
  ("condry" nil (k aa1 n d er0 iy0))
  ("conducive" nil (k ax n d uw1 s ih0 v))
! ("conduct" v (k ax n d ah1 k t))
! ("conduct" n (k ah1 n d ax k t))
  ("conducted" nil (k ax n d ah1 k t ax d))
  ("conducting" nil (k ax n d ah1 k t ih0 ng))
  ("conduction" nil (k ax n d ah1 k sh ax n))
***************
*** 18652,18661 ****
  ("configurations" nil (k ax n f ih1 g y er0 ey1 sh ax n z))
  ("configured" nil (k ax n f ih1 g y er0 d))
  ("confindustria" nil (k aa1 n f ih0 n d ah1 s t r iy0 ax))
! ("confine" nil (k ax n f ay1 n))
  ("confined" nil (k ax n f ay1 n d))
  ("confinement" nil (k ax n f ay1 n m ax n t))
! ("confines" nil (k aa1 n f ay1 n z))
  ("confining" nil (k ax n f ay1 n ih0 ng))
  ("confirm" nil (k ax n f er1 m))
  ("confirmable" nil (k ax n f er1 m ax b ax l))
--- 18706,18717 ----
  ("configurations" nil (k ax n f ih1 g y er0 ey1 sh ax n z))
  ("configured" nil (k ax n f ih1 g y er0 d))
  ("confindustria" nil (k aa1 n f ih0 n d ah1 s t r iy0 ax))
! ("confine" v (k ax n f ay1 n))
! ("confine" n (k aa1 n f ay0 n))
  ("confined" nil (k ax n f ay1 n d))
  ("confinement" nil (k ax n f ay1 n m ax n t))
! ("confines" n (k aa1 n f ay1 n z))
! ("confines" v (k ax n f ay1 n z))
  ("confining" nil (k ax n f ay1 n ih0 ng))
  ("confirm" nil (k ax n f er1 m))
  ("confirmable" nil (k ax n f er1 m ax b ax l))
***************
*** 18672,18681 ****
  ("confiscatory" nil (k ax n f ih1 s k ax t ao1 r iy0))
  ("confiterias" nil (k aa1 n f ih0 t ih1 r iy0 ax z))
  ("conflagration" nil (k aa1 n f l ax g r ey1 sh ax n))
! ("conflict" nil (k aa1 n f l ih0 k t))
  ("conflicted" nil (k ax n f l ih1 k t ih0 d))
  ("conflicting" nil (k ax n f l ih1 k t ih0 ng))
! ("conflicts" nil (k ax n f l ih1 k t s))
  ("confluence" nil (k aa1 n f l uw0 ax n s))
  ("conform" nil (k ax n f ao1 r m))
  ("conformance" nil (k ax n f ao1 r m ax n s))
--- 18728,18739 ----
  ("confiscatory" nil (k ax n f ih1 s k ax t ao1 r iy0))
  ("confiterias" nil (k aa1 n f ih0 t ih1 r iy0 ax z))
  ("conflagration" nil (k aa1 n f l ax g r ey1 sh ax n))
! ("conflict" n (k aa1 n f l ih0 k t))
! ("conflict" v (k ax n f l ih1 k t))
  ("conflicted" nil (k ax n f l ih1 k t ih0 d))
  ("conflicting" nil (k ax n f l ih1 k t ih0 ng))
! ("conflicts" v (k ax n f l ih1 k t s))
! ("conflicts" n (k aa1 n f l ih0 k t s))
  ("confluence" nil (k aa1 n f l uw0 ax n s))
  ("conform" nil (k ax n f ao1 r m))
  ("conformance" nil (k ax n f ao1 r m ax n s))
***************
*** 18864,18872 ****
  ("conscious" nil (k aa1 n sh ax s))
  ("consciously" nil (k aa1 n sh ax s l iy0))
  ("consciousness" nil (k aa1 n sh ax s n ax s))
  ("conscripted" nil (k ax n s k r ih1 p t ih0 d))
  ("conscription" nil (k ax n s k r ih1 p sh ax n))
! ("conscripts" nil (k aa1 n s k r ih0 p t s))
  ("conseco" nil (k aa0 n s ey1 k ow0))
  ("consecrated" nil (k aa1 n s ax k r ey1 t ax d))
  ("consecutive" nil (k ax n s eh1 k y ax t ih0 v))
--- 18922,18933 ----
  ("conscious" nil (k aa1 n sh ax s))
  ("consciously" nil (k aa1 n sh ax s l iy0))
  ("consciousness" nil (k aa1 n sh ax s n ax s))
+ ("conscript" n (k aa1 n s k r ih0 p t))
+ ("conscript" v (k ax n s k r ih1 p t))
  ("conscripted" nil (k ax n s k r ih1 p t ih0 d))
  ("conscription" nil (k ax n s k r ih1 p sh ax n))
! ("conscripts" n (k aa1 n s k r ih0 p t s))
! ("conscripts" v (k ax n s k r ih1 p t s))
  ("conseco" nil (k aa0 n s ey1 k ow0))
  ("consecrated" nil (k aa1 n s ax k r ey1 t ax d))
  ("consecutive" nil (k ax n s eh1 k y ax t ih0 v))
***************
*** 18927,18935 ****
  ("consolata" nil (k ow0 n s ow0 l aa1 t ax))
  ("consolation" nil (k aa1 n s ax l ey1 sh ax n))
  ("consolations" nil (k aa1 n s ax l ey1 sh ax n z))
! ("console" nil (k aa1 n s ow0 l))
  ("consoled" nil (k ax n s ow1 l d))
! ("consoles" nil (k ax n s ow1 l z))
  ("consoli" nil (k ow0 n s ow1 l iy0))
  ("consolidate" nil (k ax n s aa1 l ih0 d ey1 t))
  ("consolidated" nil (k ax n s aa1 l ax d ey1 t ax d))
--- 18988,18998 ----
  ("consolata" nil (k ow0 n s ow0 l aa1 t ax))
  ("consolation" nil (k aa1 n s ax l ey1 sh ax n))
  ("consolations" nil (k aa1 n s ax l ey1 sh ax n z))
! ("console" n (k aa1 n s ow0 l))
! ("console" v (k ax n s ow1 l))
  ("consoled" nil (k ax n s ow1 l d))
! ("consoles" v (k ax n s ow1 l z))
! ("consoles" n (k aa1 n s ow0 l z))
  ("consoli" nil (k ow0 n s ow1 l iy0))
  ("consolidate" nil (k ax n s aa1 l ih0 d ey1 t))
  ("consolidated" nil (k ax n s aa1 l ax d ey1 t ax d))
***************
*** 18943,18953 ****
  ("consonant" nil (k aa1 n s ax n ax n t))
  ("consonantal" nil (k ae1 n s ax n ae1 n t ax l))
  ("consonants" nil (k aa1 n s ax n ax n t s))
! ("consort" nil (k ax n s ao1 r t))
  ("consortia" nil (k ax n s ao1 r sh ax))
  ("consorting" nil (k ax n s ao1 r t ih0 ng))
  ("consortium" nil (k ax n s ao1 r sh iy0 ax m))
  ("consortiums" nil (k ax n s ao1 r sh ax m z))
  ("conspicuous" nil (k ax n s p ih1 k y uw0 ax s))
  ("conspicuously" nil (k ax n s p ih1 k y uw0 ax s l iy0))
  ("conspiracies" nil (k ax n s p ih1 r ax s iy0 z))
--- 19006,19019 ----
  ("consonant" nil (k aa1 n s ax n ax n t))
  ("consonantal" nil (k ae1 n s ax n ae1 n t ax l))
  ("consonants" nil (k aa1 n s ax n ax n t s))
! ("consort" v (k ax n s ao1 r t))
! ("consort" n (k aa1 n s ao0 r t))
  ("consortia" nil (k ax n s ao1 r sh ax))
  ("consorting" nil (k ax n s ao1 r t ih0 ng))
  ("consortium" nil (k ax n s ao1 r sh iy0 ax m))
  ("consortiums" nil (k ax n s ao1 r sh ax m z))
+ ("consorts" v (k ax n s ao1 r t s))
+ ("consorts" n (k aa1 n s ao0 r t s))
  ("conspicuous" nil (k ax n s p ih1 k y uw0 ax s))
  ("conspicuously" nil (k ax n s p ih1 k y uw0 ax s l iy0))
  ("conspiracies" nil (k ax n s p ih1 r ax s iy0 z))
***************
*** 19095,19101 ****
  ("contenders" nil (k ax n t eh1 n d er0 z))
  ("contending" nil (k ax n t eh1 n d ih0 ng))
  ("contends" nil (k ax n t eh1 n d z))
! ("content" nil (k aa1 n t eh0 n t))
  ("contented" nil (k ax n t eh1 n t ax d))
  ("contentedly" nil (k ax n t eh1 n t ax d l iy0))
  ("contention" nil (k ax n t eh1 n sh ax n))
--- 19161,19168 ----
  ("contenders" nil (k ax n t eh1 n d er0 z))
  ("contending" nil (k ax n t eh1 n d ih0 ng))
  ("contends" nil (k ax n t eh1 n d z))
! ("content" n (k aa1 n t eh0 n t))
! ("content" v (k ax n t eh1 n t))
  ("contented" nil (k ax n t eh1 n t ax d))
  ("contentedly" nil (k ax n t eh1 n t ax d l iy0))
  ("contention" nil (k ax n t eh1 n sh ax n))
***************
*** 19104,19117 ****
  ("contentiousness" nil (k ax n t eh1 n sh ax s n ax s))
  ("contentment" nil (k ax n t eh1 n t m ax n t))
  ("contento" nil (k ow0 n t eh1 n t ow0))
! ("contents" nil (k aa1 n t eh0 n t s))
! ("contest" nil (k aa1 n t eh0 s t))
  ("contestable" nil (k ax n t eh1 s t ax b ax l))
  ("contestant" nil (k ax n t eh1 s t ax n t))
  ("contestants" nil (k ax n t eh1 s t ax n t s))
  ("contested" nil (k ax n t eh1 s t ax d))
  ("contesting" nil (k ax n t eh1 s t ih0 ng))
! ("contests" nil (k aa1 n t eh0 s t s))
  ("context" nil (k aa1 n t eh0 k s t))
  ("contexts" nil (k aa1 n t eh1 k s t s))
  ("conti" nil (k ow1 n t iy0))
--- 19171,19187 ----
  ("contentiousness" nil (k ax n t eh1 n sh ax s n ax s))
  ("contentment" nil (k ax n t eh1 n t m ax n t))
  ("contento" nil (k ow0 n t eh1 n t ow0))
! ("contents" n (k aa1 n t eh0 n t s))
! ("contents" v (k ax n t eh1 n t s))
! ("contest" n (k aa1 n t eh0 s t))
! ("contest" v (k ax n t eh1 s t))
  ("contestable" nil (k ax n t eh1 s t ax b ax l))
  ("contestant" nil (k ax n t eh1 s t ax n t))
  ("contestants" nil (k ax n t eh1 s t ax n t s))
  ("contested" nil (k ax n t eh1 s t ax d))
  ("contesting" nil (k ax n t eh1 s t ih0 ng))
! ("contests" n (k aa1 n t eh0 s t s))
! ("contests" v (k ax n t eh1 s t s))
  ("context" nil (k aa1 n t eh0 k s t))
  ("contexts" nil (k aa1 n t eh1 k s t s))
  ("conti" nil (k ow1 n t iy0))
***************
*** 19159,19172 ****
  ("contraception" nil (k aa1 n t r ax s eh1 p sh ax n))
  ("contraceptive" nil (k aa1 n t r ax s eh1 p t ih0 v))
  ("contraceptives" nil (k aa1 n t r ax s eh1 p t ih0 v z))
! ("contract" nil (k aa1 n t r ae1 k t))
  ("contracted" nil (k aa1 n t r ae0 k t ax d))
  ("contracting" nil (k aa1 n t r ae0 k t ih0 ng))
  ("contraction" nil (k ax n t r ae1 k sh ax n))
  ("contractions" nil (k ax n t r ae1 k sh ax n z))
  ("contractor" nil (k aa1 n t r ae1 k t er0))
  ("contractors" nil (k aa1 n t r ae1 k t er0 z))
! ("contracts" nil (k aa1 n t r ae1 k t s))
  ("contractual" nil (k ax n t r ae1 k ch uw0 ax l))
  ("contractually" nil (k ax n t r ae1 k ch uw0 ax l iy0))
  ("contradict" nil (k aa1 n t r ax d ih1 k t))
--- 19229,19245 ----
  ("contraception" nil (k aa1 n t r ax s eh1 p sh ax n))
  ("contraceptive" nil (k aa1 n t r ax s eh1 p t ih0 v))
  ("contraceptives" nil (k aa1 n t r ax s eh1 p t ih0 v z))
! ("contract" j (k aa1 n t r ae0 k t))
! ("contract" n (k aa1 n t r ae1 k t))
! ("contract" v (k ax n t r ae1 k t))
  ("contracted" nil (k aa1 n t r ae0 k t ax d))
  ("contracting" nil (k aa1 n t r ae0 k t ih0 ng))
  ("contraction" nil (k ax n t r ae1 k sh ax n))
  ("contractions" nil (k ax n t r ae1 k sh ax n z))
  ("contractor" nil (k aa1 n t r ae1 k t er0))
  ("contractors" nil (k aa1 n t r ae1 k t er0 z))
! ("contracts" n (k aa1 n t r ae1 k t s))
! ("contracts" v (k ax n t r ae1 k t s))
  ("contractual" nil (k ax n t r ae1 k ch uw0 ax l))
  ("contractually" nil (k ax n t r ae1 k ch uw0 ax l iy0))
  ("contradict" nil (k aa1 n t r ax d ih1 k t))
***************
*** 19183,19194 ****
  ("contrarian" nil (k aa1 n t r eh1 r iy0 ax n))
  ("contrarians" nil (k ax n t r eh1 r iy0 ax n z))
  ("contrariness" nil (k aa1 n t r eh0 r iy0 n ax s))
! ("contrary" nil (k aa1 n t r eh0 r iy0))
  ("contras" nil (k aa1 n t r ax z))
! ("contrast" nil (k aa1 n t r ae0 s t))
  ("contrasted" nil (k ax n t r ae1 s t ax d))
  ("contrasting" nil (k ax n t r ae1 s t ih0 ng))
! ("contrasts" nil (k aa1 n t r ae0 s t s))
  ("contravene" nil (k aa1 n t r ax v iy1 n))
  ("contravention" nil (k aa1 n t r ax v eh1 n ch ax n))
  ("contreras" nil (k ow0 n t r eh1 r aa0 z))
--- 19256,19270 ----
  ("contrarian" nil (k aa1 n t r eh1 r iy0 ax n))
  ("contrarians" nil (k ax n t r eh1 r iy0 ax n z))
  ("contrariness" nil (k aa1 n t r eh0 r iy0 n ax s))
! ("contrary" n (k aa1 n t r eh0 r iy0))
! ("contrary" j (k aa1 n t r eh1 r iy0))
  ("contras" nil (k aa1 n t r ax z))
! ("contrast" n (k aa1 n t r ae0 s t))
! ("contrast" v (k ax n t r ae1 s t))
  ("contrasted" nil (k ax n t r ae1 s t ax d))
  ("contrasting" nil (k ax n t r ae1 s t ih0 ng))
! ("contrasts" n (k aa1 n t r ae0 s t s))
! ("contrasts" v (k ax n t r ae1 s t s))
  ("contravene" nil (k aa1 n t r ax v iy1 n))
  ("contravention" nil (k aa1 n t r ax v eh1 n ch ax n))
  ("contreras" nil (k ow0 n t r eh1 r aa0 z))
***************
*** 19253,19265 ****
  ("conversational" nil (k aa1 n v er0 s ey1 sh ax n ax l))
  ("conversationalist" nil (k aa1 n v er0 s ey1 sh ax n ax l ax s t))
  ("conversations" nil (k aa1 n v er0 s ey1 sh ax n z))
! ("converse" nil (k aa1 n v er0 s))
  ("conversed" nil (k ax n v er1 s t))
  ("conversely" nil (k aa1 n v er0 s l iy0))
  ("conversing" nil (k ax n v er1 s ih0 ng))
  ("conversion" nil (k ax n v er1 zh ax n))
  ("conversions" nil (k ax n v er1 zh ax n z))
! ("convert" nil (k aa1 n v er0 t))
  ("converted" nil (k ax n v er1 t ih0 d))
  ("converter" nil (k ax n v er1 t er0))
  ("converters" nil (k ax n v er1 t er0 z))
--- 19329,19343 ----
  ("conversational" nil (k aa1 n v er0 s ey1 sh ax n ax l))
  ("conversationalist" nil (k aa1 n v er0 s ey1 sh ax n ax l ax s t))
  ("conversations" nil (k aa1 n v er0 s ey1 sh ax n z))
! ("converse" n (k aa1 n v er0 s))
! ("converse" v (k ax n v er1 s))
  ("conversed" nil (k ax n v er1 s t))
  ("conversely" nil (k aa1 n v er0 s l iy0))
  ("conversing" nil (k ax n v er1 s ih0 ng))
  ("conversion" nil (k ax n v er1 zh ax n))
  ("conversions" nil (k ax n v er1 zh ax n z))
! ("convert" n (k aa1 n v er0 t))
! ("convert" v (k ax n v er1 t))
  ("converted" nil (k ax n v er1 t ih0 d))
  ("converter" nil (k ax n v er1 t er0))
  ("converters" nil (k ax n v er1 t er0 z))
***************
*** 19267,19273 ****
  ("convertible" nil (k ax n v er1 t ax b ax l))
  ("convertibles" nil (k ax n v er1 t ax b ax l z))
  ("converting" nil (k ax n v er1 t ih0 ng))
! ("converts" nil (k aa1 n v er0 t s))
  ("convery" nil (k aa1 n v er0 iy0))
  ("convex" nil (k ax n v eh1 k s))
  ("convey" nil (k ax n v ey1))
--- 19345,19352 ----
  ("convertible" nil (k ax n v er1 t ax b ax l))
  ("convertibles" nil (k ax n v er1 t ax b ax l z))
  ("converting" nil (k ax n v er1 t ih0 ng))
! ("converts" n (k aa1 n v er0 t s))
! ("converts" v (k ax n v er1 t s))
  ("convery" nil (k aa1 n v er0 iy0))
  ("convex" nil (k ax n v eh1 k s))
  ("convey" nil (k ax n v ey1))
***************
*** 19277,19288 ****
  ("conveying" nil (k ax n v ey1 ih0 ng))
  ("conveyor" nil (k ax n v ey1 er0))
  ("conveys" nil (k ax n v ey1 z))
! ("convict" nil (k aa1 n v ih0 k t))
  ("convicted" nil (k ax n v ih1 k t ax d))
  ("convicting" nil (k ax n v ih1 k t ih0 ng))
  ("conviction" nil (k ax n v ih1 k sh ax n))
  ("convictions" nil (k ax n v ih1 k sh ax n z))
! ("convicts" nil (k aa1 n v ih0 k t s))
  ("conville" nil (k ow1 n v ih0 l))
  ("convince" nil (k ax n v ih1 n s))
  ("convinced" nil (k ax n v ih1 n s t))
--- 19356,19369 ----
  ("conveying" nil (k ax n v ey1 ih0 ng))
  ("conveyor" nil (k ax n v ey1 er0))
  ("conveys" nil (k ax n v ey1 z))
! ("convict" v (k ax n v ih1 k t))
! ("convict" n (k aa1 n v ih0 k t))
  ("convicted" nil (k ax n v ih1 k t ax d))
  ("convicting" nil (k ax n v ih1 k t ih0 ng))
  ("conviction" nil (k ax n v ih1 k sh ax n))
  ("convictions" nil (k ax n v ih1 k sh ax n z))
! ("convicts" n (k aa1 n v ih0 k t s))
! ("convicts" v (k ax n v ih1 k t s))
  ("conville" nil (k ow1 n v ih0 l))
  ("convince" nil (k ax n v ih1 n s))
  ("convinced" nil (k ax n v ih1 n s t))
***************
*** 19379,19387 ****
  ("coopervision" nil (k uw1 p er0 v ih1 zh ax n))
  ("coopman" nil (k uw1 p m ax n))
  ("cooprider" nil (k uw1 p r ay1 d er0))
! ("coordinate" nil (k ow0 ao1 r d ax n ax t))
  ("coordinated" nil (k ow0 ao1 r d ax n ey0 t ih0 d))
! ("coordinates" nil (k ow0 ao1 r d ax n ax t s))
  ("coordinating" nil (k ow0 ao1 r d ax n ey1 t ih0 ng))
  ("coordination" nil (k ow0 ao1 r d ax n ey1 sh ax n))
  ("coordinator" nil (k ow0 ao1 r d ax n ey1 t er0))
--- 19460,19470 ----
  ("coopervision" nil (k uw1 p er0 v ih1 zh ax n))
  ("coopman" nil (k uw1 p m ax n))
  ("cooprider" nil (k uw1 p r ay1 d er0))
! ("coordinate" n (k ow0 ao1 r d ax n ax t))
! ("coordinate" v (k ow0 ao1 r d ax n ey1 t))
  ("coordinated" nil (k ow0 ao1 r d ax n ey0 t ih0 d))
! ("coordinates" n (k ow0 ao1 r d ax n ax t s))
! ("coordinates" v (k ow0 ao1 r d ax n ey1 t s))
  ("coordinating" nil (k ow0 ao1 r d ax n ey1 t ih0 ng))
  ("coordination" nil (k ow0 ao1 r d ax n ey1 sh ax n))
  ("coordinator" nil (k ow0 ao1 r d ax n ey1 t er0))
***************
*** 22628,22636 ****
  ("decoy" nil (d ax k oy1))
  ("decoys" nil (d iy1 k oy0 z))
  ("decrane" nil (d ax k r ey1 n))
! ("decrease" nil (d ih0 k r iy1 s))
  ("decreased" nil (d ih0 k r iy1 s t))
! ("decreases" nil (d ih0 k r iy1 s ax z))
  ("decreasing" nil (d ih0 k r iy1 s ih0 ng))
  ("decree" nil (d ih0 k r iy1))
  ("decreed" nil (d ih0 k r iy1 d))
--- 22711,22721 ----
  ("decoy" nil (d ax k oy1))
  ("decoys" nil (d iy1 k oy0 z))
  ("decrane" nil (d ax k r ey1 n))
! ("decrease" v (d ih0 k r iy1 s))
! ("decrease" n (d iy1 k r iy0 s))
  ("decreased" nil (d ih0 k r iy1 s t))
! ("decreases" v (d ih0 k r iy1 s ax z))
! ("decreases" n (d iy1 k r iy0 s ax z))
  ("decreasing" nil (d ih0 k r iy1 s ih0 ng))
  ("decree" nil (d ih0 k r iy1))
  ("decreed" nil (d ih0 k r iy1 d))
***************
*** 22736,22742 ****
  ("defeatism" nil (d ih0 f iy1 t ih0 z ax m))
  ("defeatist" nil (d ih0 f iy1 t ih0 s t))
  ("defeats" nil (d ih0 f iy1 t s))
! ("defect" nil (d iy1 f eh0 k t))
  ("defected" nil (d ih0 f eh1 k t ih0 d))
  ("defecting" nil (d ih0 f eh1 k t ih0 ng))
  ("defection" nil (d ih0 f eh1 k sh ax n))
--- 22821,22828 ----
  ("defeatism" nil (d ih0 f iy1 t ih0 z ax m))
  ("defeatist" nil (d ih0 f iy1 t ih0 s t))
  ("defeats" nil (d ih0 f iy1 t s))
! ("defect" v (d ih1 f eh1 k t))
! ("defect" n (d iy1 f eh0 k t))
  ("defected" nil (d ih0 f eh1 k t ih0 d))
  ("defecting" nil (d ih0 f eh1 k t ih0 ng))
  ("defection" nil (d ih0 f eh1 k sh ax n))
***************
*** 22744,22750 ****
  ("defective" nil (d ih0 f eh1 k t ih0 v))
  ("defector" nil (d ih0 f eh1 k t er0))
  ("defectors" nil (d ih0 f eh1 k t er0 z))
! ("defects" nil (d iy1 f eh0 k t s))
  ("defee" nil (d eh1 f iy0))
  ("defelice" nil (d ih0 f eh1 l ih0 s))
  ("defenbaugh" nil (d eh1 f ih0 n b aw0))
--- 22830,22837 ----
  ("defective" nil (d ih0 f eh1 k t ih0 v))
  ("defector" nil (d ih0 f eh1 k t er0))
  ("defectors" nil (d ih0 f eh1 k t er0 z))
! ("defects" v (d ih1 f eh1 k t))
! ("defects" n (d iy1 f eh0 k t))
  ("defee" nil (d eh1 f iy0))
  ("defelice" nil (d ih0 f eh1 l ih0 s))
  ("defenbaugh" nil (d eh1 f ih0 n b aw0))
***************
*** 23109,23115 ****
  ("delhi" nil (d eh1 l iy0))
  ("deli" nil (d eh1 l iy0))
  ("delia" nil (d iy1 l y ax))
! ("deliberate" nil (d ih0 l ih1 b er0 ax t))
  ("deliberated" nil (d ih0 l ih1 b er0 ey1 t ih0 d))
  ("deliberately" nil (d ih0 l ih1 b er0 ax t l iy0))
  ("deliberating" nil (d ih0 l ih1 b er0 ey1 t ih0 ng))
--- 23196,23203 ----
  ("delhi" nil (d eh1 l iy0))
  ("deli" nil (d eh1 l iy0))
  ("delia" nil (d iy1 l y ax))
! ("deliberate" j (d ih0 l ih1 b er0 ih0 t))
! ("deliberate" v (d ih0 l ih1 b er0 ey1 t))
  ("deliberated" nil (d ih0 l ih1 b er0 ey1 t ih0 d))
  ("deliberately" nil (d ih0 l ih1 b er0 ax t l iy0))
  ("deliberating" nil (d ih0 l ih1 b er0 ey1 t ih0 ng))
***************
*** 23925,23938 ****
  ("desena" nil (d ih0 s eh1 n ax))
  ("desensitize" nil (d ih0 s eh1 n s ax t ay1 z))
  ("deseret" nil (d eh1 s er0 eh1 t))
! ("desert" nil (d eh1 z er0 t))
  ("deserted" nil (d ih0 z er1 t ih0 d))
  ("deserter" nil (d eh1 z er0 t er0))
  ("deserters" nil (d eh1 z er0 t er0 z))
  ("deserting" nil (d eh1 z er0 t ih0 ng))
  ("desertion" nil (d ih0 z er1 sh ax n))
  ("desertions" nil (d ih0 z er1 sh ax n z))
! ("deserts" nil (d eh1 z er0 t s))
  ("deserve" nil (d ih0 z er1 v))
  ("deserved" nil (d ih0 z er1 v d))
  ("deservedly" nil (d ih0 z er1 v ax d l iy0))
--- 24013,24028 ----
  ("desena" nil (d ih0 s eh1 n ax))
  ("desensitize" nil (d ih0 s eh1 n s ax t ay1 z))
  ("deseret" nil (d eh1 s er0 eh1 t))
! ("desert" n (d eh1 z er0 t))
! ("desert" v (d ih0 z er1 t))
  ("deserted" nil (d ih0 z er1 t ih0 d))
  ("deserter" nil (d eh1 z er0 t er0))
  ("deserters" nil (d eh1 z er0 t er0 z))
  ("deserting" nil (d eh1 z er0 t ih0 ng))
  ("desertion" nil (d ih0 z er1 sh ax n))
  ("desertions" nil (d ih0 z er1 sh ax n z))
! ("deserts" n (d eh1 z er0 t s))
! ("deserts" v (d ih0 z er1 t s))
  ("deserve" nil (d ih0 z er1 v))
  ("deserved" nil (d ih0 z er1 v d))
  ("deservedly" nil (d ih0 z er1 v ax d l iy0))
***************
*** 24551,24559 ****
  ("dicostanzo" nil (d ih0 k ow0 s t aa1 n z ow0))
  ("dicots" nil (d ay1 k aa0 t s))
  ("dictaphone" nil (d ih1 k t ax f ow1 n))
! ("dictate" nil (d ih0 k t ey1 t))
  ("dictated" nil (d ih0 k t ey1 t ax d))
! ("dictates" nil (d ih0 k t ey1 t s))
  ("dictating" nil (d ih1 k t ey1 t ih0 ng))
  ("dictation" nil (d ih0 k t ey1 sh ax n))
  ("dictator" nil (d ih0 k t ey1 t er0))
--- 24641,24651 ----
  ("dicostanzo" nil (d ih0 k ow0 s t aa1 n z ow0))
  ("dicots" nil (d ay1 k aa0 t s))
  ("dictaphone" nil (d ih1 k t ax f ow1 n))
! ("dictate" v (d ih0 k t ey1 t))
! ("dictate" n (d ih1 k t ey1 t))
  ("dictated" nil (d ih0 k t ey1 t ax d))
! ("dictates" v (d ih0 k t ey1 t s))
! ("dictates" n (d ih1 k t ey1 t s))
  ("dictating" nil (d ih1 k t ey1 t ih0 ng))
  ("dictation" nil (d ih0 k t ey1 sh ax n))
  ("dictator" nil (d ih0 k t ey1 t er0))
***************
*** 24681,24687 ****
  ("diffley" nil (d ih1 f l iy0))
  ("diffract" nil (d ih0 f r ae1 k t))
  ("diffraction" nil (d ih0 f r ae1 k sh ax n))
! ("diffuse" nil (d ih0 f y uw1 s))
  ("diffused" nil (d ih0 f y uw1 z d))
  ("diffuses" nil (d ih0 f y uw1 z ax z))
  ("diffusion" nil (d ih0 f y uw1 zh ax n))
--- 24773,24780 ----
  ("diffley" nil (d ih1 f l iy0))
  ("diffract" nil (d ih0 f r ae1 k t))
  ("diffraction" nil (d ih0 f r ae1 k sh ax n))
! ("diffuse" j (d ih0 f y uw1 s))
! ("diffuse" v (d ih0 f y uw1 z))
  ("diffused" nil (d ih0 f y uw1 z d))
  ("diffuses" nil (d ih0 f y uw1 z ax z))
  ("diffusion" nil (d ih0 f y uw1 zh ax n))
***************
*** 24700,24713 ****
  ("digennaro" nil (d ih0 jh eh0 n aa1 r ow0))
  ("digenova" nil (d iy1 jh eh0 n ow1 v ax))
  ("digeronimo" nil (d ih0 jh er0 ow0 n iy1 m ow0))
! ("digest" nil (d ay0 jh eh1 s t))
  ("digested" nil (d ay1 jh eh1 s t ih0 d))
  ("digester" nil (d ay1 jh eh1 s t er0))
  ("digestible" nil (d ay0 jh eh1 s t ax b ax l))
  ("digesting" nil (d ay0 jh eh1 s t ih0 ng))
  ("digestion" nil (d ay0 jh eh1 s ch ax n))
  ("digestive" nil (d ay0 jh eh1 s t ih0 v))
! ("digests" nil (d ay0 jh eh1 s t s))
  ("digger" nil (d ih1 g er0))
  ("diggers" nil (d ih1 g er0 z))
  ("digges" nil (d ih1 g z))
--- 24793,24808 ----
  ("digennaro" nil (d ih0 jh eh0 n aa1 r ow0))
  ("digenova" nil (d iy1 jh eh0 n ow1 v ax))
  ("digeronimo" nil (d ih0 jh er0 ow0 n iy1 m ow0))
! ("digest" v (d ay0 jh eh1 s t))
! ("digest" n (d ay1 jh eh0 s t))
  ("digested" nil (d ay1 jh eh1 s t ih0 d))
  ("digester" nil (d ay1 jh eh1 s t er0))
  ("digestible" nil (d ay0 jh eh1 s t ax b ax l))
  ("digesting" nil (d ay0 jh eh1 s t ih0 ng))
  ("digestion" nil (d ay0 jh eh1 s ch ax n))
  ("digestive" nil (d ay0 jh eh1 s t ih0 v))
! ("digests" v (d ay0 jh eh1 s t s))
! ("digests" n (d ay1 jh eh0 s t s))
  ("digger" nil (d ih1 g er0))
  ("diggers" nil (d ih1 g er0 z))
  ("digges" nil (d ih1 g z))
***************
*** 25143,25151 ****
  ("discerning" nil (d ih0 s er1 n ih0 ng))
  ("discernment" nil (d ih0 s er1 n m ax n t))
  ("disch" nil (d ih1 sh))
! ("discharge" nil (d ih0 s ch aa1 r jh))
  ("discharged" nil (d ih0 s ch aa1 r jh d))
! ("discharges" nil (d ih0 s ch aa1 r jh ax z))
  ("discharging" nil (d ih0 s ch aa1 r jh ih0 ng))
  ("discher" nil (d ih1 sh er0))
  ("dischinger" nil (d ih1 sh ih0 n jh er0))
--- 25238,25248 ----
  ("discerning" nil (d ih0 s er1 n ih0 ng))
  ("discernment" nil (d ih0 s er1 n m ax n t))
  ("disch" nil (d ih1 sh))
! ("discharge" v (d ih0 s ch aa1 r jh))
! ("discharge" n (d ih1 s ch aa0 r jh))
  ("discharged" nil (d ih0 s ch aa1 r jh d))
! ("discharges" v (d ih0 s ch aa1 r jh ax z))
! ("discharges" n (d ih1 s ch aa0 r jh ax z))
  ("discharging" nil (d ih0 s ch aa1 r jh ih0 ng))
  ("discher" nil (d ih1 sh er0))
  ("dischinger" nil (d ih1 sh ih0 n jh er0))
***************
*** 25191,25203 ****
  ("discordant" nil (d ih0 s k ao1 r d ax n t))
  ("discos" nil (d ih1 s k ow0 z))
  ("discotheque" nil (d ih1 s k ow0 t eh1 k))
! ("discount" nil (d ih0 s k aw1 n t))
  ("discountable" nil (d ih1 s k aw1 n t ax b ax l))
  ("discounted" nil (d ih1 s k aw1 n t ih0 d))
  ("discounter" nil (d ih1 s k aw1 n t er0))
  ("discounters" nil (d ih0 s k aw1 n t er0 z))
  ("discounting" nil (d ih1 s k aw1 n t ih0 ng))
! ("discounts" nil (d ih0 s k aw1 n t s))
  ("discourage" nil (d ih0 s k er1 ih0 jh))
  ("discouraged" nil (d ih0 s k er1 ax jh d))
  ("discouragement" nil (d ih0 s k er1 ih0 jh m ax n t))
--- 25288,25302 ----
  ("discordant" nil (d ih0 s k ao1 r d ax n t))
  ("discos" nil (d ih1 s k ow0 z))
  ("discotheque" nil (d ih1 s k ow0 t eh1 k))
! ("discount" v (d ih0 s k aw1 n t))
! ("discount" n (d ih1 s k aw0 n t))
  ("discountable" nil (d ih1 s k aw1 n t ax b ax l))
  ("discounted" nil (d ih1 s k aw1 n t ih0 d))
  ("discounter" nil (d ih1 s k aw1 n t er0))
  ("discounters" nil (d ih0 s k aw1 n t er0 z))
  ("discounting" nil (d ih1 s k aw1 n t ih0 ng))
! ("discounts" v (d ih0 s k aw1 n t s))
! ("discounts" n (d ih1 s k aw0 n t s))
  ("discourage" nil (d ih0 s k er1 ih0 jh))
  ("discouraged" nil (d ih0 s k er1 ax jh d))
  ("discouragement" nil (d ih0 s k er1 ih0 jh m ax n t))
***************
*** 25897,25903 ****
  ("doerr" nil (d ao1 r))
  ("doers" nil (d uw1 er0 z))
  ("doersam" nil (d ao1 r s ax m))
! ("does" nil (d ah1 z))
  ("doescher" nil (d ow1 sh er0))
  ("doetsch" nil (d ow1 ch))
  ("dofasco" nil (d ax f ae1 s k ow0))
--- 25996,26003 ----
  ("doerr" nil (d ao1 r))
  ("doers" nil (d uw1 er0 z))
  ("doersam" nil (d ao1 r s ax m))
! ("does" v (d ah1 z))
! ("does" n (d ow1 z))
  ("doescher" nil (d ow1 sh er0))
  ("doetsch" nil (d ow1 ch))
  ("dofasco" nil (d ax f ae1 s k ow0))
***************
*** 27328,27336 ****
  ("dupler" nil (d uw1 p ax l er0))
  ("duplessis" nil (d uw1 p l ih0 s ih0 s))
  ("duplex" nil (d uw1 p l eh1 k s))
! ("duplicate" nil (d uw1 p l ax k ax t))
  ("duplicated" nil (d uw1 p l ih0 k ey1 t ih0 d))
! ("duplicates" nil (d y uw1 p l ax k ey1 t s))
  ("duplicating" nil (d uw1 p l ih0 k ey1 t ih0 ng))
  ("duplication" nil (d y uw1 p l ax k ey1 sh ax n))
  ("duplications" nil (d uw1 p l ih0 k ey1 sh ax n z))
--- 27428,27438 ----
  ("dupler" nil (d uw1 p ax l er0))
  ("duplessis" nil (d uw1 p l ih0 s ih0 s))
  ("duplex" nil (d uw1 p l eh1 k s))
! ("duplicate" n (d uw1 p l ax k ax t))
! ("duplicate" v (d uw1 p l ax k ey1 t))
  ("duplicated" nil (d uw1 p l ih0 k ey1 t ih0 d))
! ("duplicates" v (d y uw1 p l ax k ey1 t s))
! ("duplicates" n (d y uw1 p l ax k ax t s))
  ("duplicating" nil (d uw1 p l ih0 k ey1 t ih0 ng))
  ("duplication" nil (d y uw1 p l ax k ey1 sh ax n))
  ("duplications" nil (d uw1 p l ih0 k ey1 sh ax n z))
***************
*** 28403,28409 ****
  ("ekstrom" nil (eh1 k s t r ax m))
  ("el" nil (eh1 l))
  ("ela" nil (eh1 l ax))
! ("elaborate" nil (ih0 l ae1 b r ax t))
  ("elaborated" nil (ih0 l ae1 b er0 ey0 t ax d))
  ("elaborately" nil (ih0 l ae1 b r ax t l iy0))
  ("elaborates" nil (ih0 l ae1 b er0 ey1 t s))
--- 28505,28512 ----
  ("ekstrom" nil (eh1 k s t r ax m))
  ("el" nil (eh1 l))
  ("ela" nil (eh1 l ax))
! ("elaborate" n (ih0 l ae1 b r ax t))
! ("elaborate" v (ih0 l ae1 b r ey0 t))
  ("elaborated" nil (ih0 l ae1 b er0 ey0 t ax d))
  ("elaborately" nil (ih0 l ae1 b r ax t l iy0))
  ("elaborates" nil (ih0 l ae1 b er0 ey1 t s))
***************
*** 29573,29581 ****
  ("entomology" nil (eh1 n t ax m aa1 l ax jh iy0))
  ("entourage" nil (aa1 n t uh0 r aa1 zh))
  ("entrails" nil (eh1 n t r ax l z))
! ("entrance" nil (eh1 n t r ax n s))
  ("entranced" nil (ih0 n t r ae1 n s t))
! ("entrances" nil (eh1 n t r ax n s ax z))
  ("entrant" nil (eh1 n t r ax n t))
  ("entrants" nil (eh1 n t r ax n t s))
  ("entrapment" nil (ih0 n t r ae1 p m ax n t))
--- 29676,29686 ----
  ("entomology" nil (eh1 n t ax m aa1 l ax jh iy0))
  ("entourage" nil (aa1 n t uh0 r aa1 zh))
  ("entrails" nil (eh1 n t r ax l z))
! ("entrance" n (eh1 n t r ax n s))
! ("entrance" v (eh0 n t r aa1 n s))
  ("entranced" nil (ih0 n t r ae1 n s t))
! ("entrances" n (eh1 n t r ax n s ax z))
! ("entrances" v (eh0 n t r aa1 n s ax z))
  ("entrant" nil (eh1 n t r ax n t))
  ("entrants" nil (eh1 n t r ax n t s))
  ("entrapment" nil (ih0 n t r ae1 p m ax n t))
***************
*** 30034,30046 ****
  ("escobedo" nil (eh0 s k ow0 b ey1 d ow0))
  ("escoe" nil (ih0 s k ow1))
  ("escondido" nil (eh1 s k aa0 n d iy1 d ow0))
! ("escort" nil (eh0 s k ao1 r t))
  ("escorted" nil (eh0 s k ao1 r t ih0 d))
  ("escorting" nil (eh1 s k ao0 r t ih0 ng))
! ("escorts" nil (eh1 s k ao0 r t s))
  ("escoto" nil (eh0 s k ow1 t ow0))
  ("escott" nil (eh1 s k ax t))
! ("escrow" nil (eh0 s k r ow1))
  ("escrowed" nil (eh1 s k r ow0 d))
  ("escudero" nil (eh0 s k uw0 d eh1 r ow0))
  ("escudo" nil (eh0 s k uw1 d ow0))
--- 30139,30154 ----
  ("escobedo" nil (eh0 s k ow0 b ey1 d ow0))
  ("escoe" nil (ih0 s k ow1))
  ("escondido" nil (eh1 s k aa0 n d iy1 d ow0))
! ("escort" v (eh0 s k ao1 r t))
! ("escort" n (eh1 s k ao0 r t))
  ("escorted" nil (eh0 s k ao1 r t ih0 d))
  ("escorting" nil (eh1 s k ao0 r t ih0 ng))
! ("escorts" n (eh1 s k ao0 r t s))
! ("escorts" v (eh0 s k ao1 r t s))
  ("escoto" nil (eh0 s k ow1 t ow0))
  ("escott" nil (eh1 s k ax t))
! ("escrow" v (eh0 s k r ow1))
! ("escrow" n (eh1 s k r ow0))
  ("escrowed" nil (eh1 s k r ow0 d))
  ("escudero" nil (eh0 s k uw0 d eh1 r ow0))
  ("escudo" nil (eh0 s k uw1 d ow0))
***************
*** 30176,30184 ****
  ("esther" nil (eh1 s t er0))
  ("estill" nil (ey0 s t iy1 l))
  ("estimable" nil (eh1 s t ax m ax b ax l))
! ("estimate" nil (eh1 s t ax m ax t))
  ("estimated" nil (eh1 s t ax m ey1 t ax d))
! ("estimates" nil (eh1 s t ax m ax t s))
  ("estimating" nil (eh1 s t ax m ey1 t ih0 ng))
  ("estimation" nil (eh1 s t ax m ey1 sh ax n))
  ("estimators" nil (ax s t ih1 m ax t er0 z))
--- 30284,30294 ----
  ("esther" nil (eh1 s t er0))
  ("estill" nil (ey0 s t iy1 l))
  ("estimable" nil (eh1 s t ax m ax b ax l))
! ("estimate" n (eh1 s t ax m ax t))
! ("estimate" v (eh1 s t ax m ey1 t))
  ("estimated" nil (eh1 s t ax m ey1 t ax d))
! ("estimates" n (eh1 s t ax m ax t s))
! ("estimates" v (eh1 s t ax m ey0 t s))
  ("estimating" nil (eh1 s t ax m ey1 t ih0 ng))
  ("estimation" nil (eh1 s t ax m ey1 sh ax n))
  ("estimators" nil (ax s t ih1 m ax t er0 z))
***************
*** 30630,30637 ****
  ("excerpt" nil (eh1 k s er0 p t))
  ("excerpted" nil (eh1 k s er1 p t ih0 d))
  ("excerpts" nil (eh1 k s er0 p t s))
! ("excess" nil (eh1 k s eh1 s))
! ("excesses" nil (eh1 k s eh1 s ih0 z))
  ("excessive" nil (ih0 k s eh1 s ih0 v))
  ("excessively" nil (ih0 k s eh1 s ih0 v l iy0))
  ("exchange" nil (ih0 k s ch ey1 n jh))
--- 30740,30749 ----
  ("excerpt" nil (eh1 k s er0 p t))
  ("excerpted" nil (eh1 k s er1 p t ih0 d))
  ("excerpts" nil (eh1 k s er0 p t s))
! ("excess" j (eh1 k s eh1 s))
! ("excess" n (eh1 k s eh1 s))
! ("excesses" n (eh1 k s eh1 s ih0 z))
! ("excesses" j (ih0 k s eh1 s ih0 z))
  ("excessive" nil (ih0 k s eh1 s ih0 v))
  ("excessively" nil (ih0 k s eh1 s ih0 v l iy0))
  ("exchange" nil (ih0 k s ch ey1 n jh))
***************
*** 30687,30695 ****
  ("exculpatory" nil (eh1 k s k ah1 l p ax t ao1 r iy0))
  ("excursion" nil (ih0 k s k er1 zh ax n))
  ("excursions" nil (ih0 k s k er1 zh ax n z))
! ("excuse" nil (ih0 k s k y uw1 s))
  ("excused" nil (ih0 k s k y uw1 z d))
! ("excuses" nil (ih0 k s k y uw1 s ih0 z))
  ("exec" nil (eh1 g z eh1 k))
  ("execrable" nil (eh1 g z eh1 k r ax b ax l))
  ("execs" nil (eh1 g z eh1 k s))
--- 30799,30809 ----
  ("exculpatory" nil (eh1 k s k ah1 l p ax t ao1 r iy0))
  ("excursion" nil (ih0 k s k er1 zh ax n))
  ("excursions" nil (ih0 k s k er1 zh ax n z))
! ("excuse" n (ih0 k s k y uw1 s))
! ("excuse" v (ih0 k s k y uw1 z))
  ("excused" nil (ih0 k s k y uw1 z d))
! ("excuses" n (ih0 k s k y uw1 s ih0 z))
! ("excuses" v (ih0 k s k y uw1 z ih0 z))
  ("exec" nil (eh1 g z eh1 k))
  ("execrable" nil (eh1 g z eh1 k r ax b ax l))
  ("execs" nil (eh1 g z eh1 k s))
***************
*** 30894,30905 ****
  ("exploded" nil (ih0 k s p l ow1 d ax d))
  ("explodes" nil (ih0 k s p l ow1 d z))
  ("exploding" nil (ih0 k s p l ow1 d ih0 ng))
! ("exploit" nil (eh1 k s p l oy1 t))
  ("exploitation" nil (eh1 k s p l oy1 t ey1 sh ax n))
  ("exploitative" nil (eh1 k s p l oy1 t ax t ih0 v))
  ("exploited" nil (eh1 k s p l oy1 t ax d))
  ("exploiting" nil (eh1 k s p l oy1 t ih0 ng))
! ("exploits" nil (eh1 k s p l oy1 t s))
  ("exploration" nil (eh1 k s p l er0 ey1 sh ax n))
  ("explorations" nil (eh1 k s p l er0 ey1 sh ax n z))
  ("exploratory" nil (ih0 k s p l ao1 r ax t ao1 r iy0))
--- 31008,31021 ----
  ("exploded" nil (ih0 k s p l ow1 d ax d))
  ("explodes" nil (ih0 k s p l ow1 d z))
  ("exploding" nil (ih0 k s p l ow1 d ih0 ng))
! ("exploit" n (eh1 k s p l oy0 t))
! ("exploit" v (ih0 k s p l oy1 t))
  ("exploitation" nil (eh1 k s p l oy1 t ey1 sh ax n))
  ("exploitative" nil (eh1 k s p l oy1 t ax t ih0 v))
  ("exploited" nil (eh1 k s p l oy1 t ax d))
  ("exploiting" nil (eh1 k s p l oy1 t ih0 ng))
! ("exploits" n (eh1 k s p l oy1 t s))
! ("exploits" v (ih0 k s p l oy1 t s))
  ("exploration" nil (eh1 k s p l er0 ey1 sh ax n))
  ("explorations" nil (eh1 k s p l er0 ey1 sh ax n z))
  ("exploratory" nil (ih0 k s p l ao1 r ax t ao1 r iy0))
***************
*** 30920,30932 ****
  ("exponential" nil (eh1 k s p ow0 n eh1 n ch ax l))
  ("exponentially" nil (eh1 k s p ow0 n eh1 n sh ax l iy0))
  ("exponents" nil (ih0 k s p ow1 n ax n t s))
! ("export" nil (eh1 k s p ao0 r t))
  ("exportable" nil (eh0 k s p ao1 r t ax b ax l))
  ("exported" nil (ih0 k s p ao1 r t ax d))
  ("exporter" nil (ih0 k s p ao1 r t er0))
  ("exporters" nil (ih0 k s p ao1 r t er0 z))
  ("exporting" nil (ih0 k s p ao1 r t ih0 ng))
! ("exports" nil (eh1 k s p ao0 r t s))
  ("expos" nil (eh1 k s p ow0 z))
  ("expose" nil (ih0 k s p ow1 z))
  ("exposed" nil (ih0 k s p ow1 z d))
--- 31036,31050 ----
  ("exponential" nil (eh1 k s p ow0 n eh1 n ch ax l))
  ("exponentially" nil (eh1 k s p ow0 n eh1 n sh ax l iy0))
  ("exponents" nil (ih0 k s p ow1 n ax n t s))
! ("export" n (eh1 k s p ao0 r t))
! ("export" v (ih0 k s p ao1 r t))
  ("exportable" nil (eh0 k s p ao1 r t ax b ax l))
  ("exported" nil (ih0 k s p ao1 r t ax d))
  ("exporter" nil (ih0 k s p ao1 r t er0))
  ("exporters" nil (ih0 k s p ao1 r t er0 z))
  ("exporting" nil (ih0 k s p ao1 r t ih0 ng))
! ("exports" n (eh1 k s p ao0 r t s))
! ("exports" v (ih0 k s p ao1 r t s))
  ("expos" nil (eh1 k s p ow0 z))
  ("expose" nil (ih0 k s p ow1 z))
  ("exposed" nil (ih0 k s p ow1 z d))
***************
*** 31007,31018 ****
  ("extortionate" nil (ih0 k s t ao1 r sh ax n ax t))
  ("extra" nil (eh1 k s t r ax))
  ("extracellularly" nil (eh1 k s t r ax s eh1 l y ax l er0 l iy0))
! ("extract" nil (ih0 k s t r ae1 k t))
  ("extracted" nil (ih0 k s t r ae1 k t ax d))
  ("extracting" nil (ih0 k s t r ae1 k t ih0 ng))
  ("extraction" nil (ih0 k s t r ae1 k sh ax n))
  ("extractions" nil (ih0 k s t r ae1 k sh ax n z))
! ("extracts" nil (ih0 k s t r ae1 k t s))
  ("extracurricular" nil (eh1 k s t r ax k er0 ih1 k y ax l er0))
  ("extradite" nil (eh1 k s t r ax d ay1 t))
  ("extradited" nil (eh1 k s t r ax d ay1 t ih0 d))
--- 31125,31138 ----
  ("extortionate" nil (ih0 k s t ao1 r sh ax n ax t))
  ("extra" nil (eh1 k s t r ax))
  ("extracellularly" nil (eh1 k s t r ax s eh1 l y ax l er0 l iy0))
! ("extract" v (ih0 k s t r ae1 k t))
! ("extract" n (eh1 k s t r ae1 k t))
  ("extracted" nil (ih0 k s t r ae1 k t ax d))
  ("extracting" nil (ih0 k s t r ae1 k t ih0 ng))
  ("extraction" nil (ih0 k s t r ae1 k sh ax n))
  ("extractions" nil (ih0 k s t r ae1 k sh ax n z))
! ("extracts" v (ih0 k s t r ae1 k t s))
! ("extracts" n (eh1 k s t r ae1 k t s))
  ("extracurricular" nil (eh1 k s t r ax k er0 ih1 k y ax l er0))
  ("extradite" nil (eh1 k s t r ax d ay1 t))
  ("extradited" nil (eh1 k s t r ax d ay1 t ih0 d))
***************
*** 34261,34272 ****
  ("frager" nil (f r ey1 g er0))
  ("fragile" nil (f r ae1 jh ax l))
  ("fragility" nil (f r ax jh ih1 l ax t iy0))
! ("fragment" nil (f r ae1 g m ax n t))
  ("fragmentary" nil (f r ae1 g m ax n t eh1 r iy0))
  ("fragmentation" nil (f r ae1 g m ax n t ey1 sh ax n))
  ("fragmented" nil (f r ae1 g m ax n t ih0 d))
  ("fragmenting" nil (f r ae1 g m ax n t ih0 ng))
! ("fragments" nil (f r ae1 g m ax n t s))
  ("frago" nil (f r ey1 g ow0))
  ("fragonard" nil (f r ae1 g ax n er0 d))
  ("fragoso" nil (f r aa0 g ow1 s ow0))
--- 34381,34394 ----
  ("frager" nil (f r ey1 g er0))
  ("fragile" nil (f r ae1 jh ax l))
  ("fragility" nil (f r ax jh ih1 l ax t iy0))
! ("fragment" n (f r ae1 g m ax n t))
! ("fragment" v (f r ae0 g m eh1 n t))
  ("fragmentary" nil (f r ae1 g m ax n t eh1 r iy0))
  ("fragmentation" nil (f r ae1 g m ax n t ey1 sh ax n))
  ("fragmented" nil (f r ae1 g m ax n t ih0 d))
  ("fragmenting" nil (f r ae1 g m ax n t ih0 ng))
! ("fragments" n (f r ae1 g m ax n t s))
! ("fragments" v (f r ae0 g m eh1 n t s))
  ("frago" nil (f r ey1 g ow0))
  ("fragonard" nil (f r ae1 g ax n er0 d))
  ("fragoso" nil (f r aa0 g ow1 s ow0))
***************
*** 34629,34635 ****
  ("freons" nil (f r iy1 aa0 n z))
  ("frequencies" nil (f r iy1 k w ax n s iy0 z))
  ("frequency" nil (f r iy1 k w ax n s iy0))
! ("frequent" nil (f r iy1 k w ax n t))
  ("frequented" nil (f r iy1 k w ax n t ih0 d))
  ("frequently" nil (f r iy1 k w ax n t l iy0))
  ("frequents" nil (f r iy1 k w ax n t s))
--- 34751,34758 ----
  ("freons" nil (f r iy1 aa0 n z))
  ("frequencies" nil (f r iy1 k w ax n s iy0 z))
  ("frequency" nil (f r iy1 k w ax n s iy0))
! ("frequent" j (f r iy1 k w ax n t))
! ("frequent" v (f r ih0 k w eh1 n t))
  ("frequented" nil (f r iy1 k w ax n t ih0 d))
  ("frequently" nil (f r iy1 k w ax n t l iy0))
  ("frequents" nil (f r iy1 k w ax n t s))
***************
*** 38344,38352 ****
  ("gradualism" nil (g r ae1 jh ax w ax l ih1 z ax m))
  ("gradualist" nil (g r ae1 jh ax w ax l ih0 s t))
  ("gradually" nil (g r ae1 jh uw0 ax l iy0))
! ("graduate" nil (g r ae1 jh ax w ax t))
  ("graduated" nil (g r ae1 jh uw0 ey1 t ih0 d))
! ("graduates" nil (g r ae1 jh ax w ax t s))
  ("graduating" nil (g r ae1 jh ax w ey1 t ih0 ng))
  ("graduation" nil (g r ae1 jh uw0 ey1 sh ax n))
  ("gradus" nil (g r ey1 d ax s))
--- 38467,38477 ----
  ("gradualism" nil (g r ae1 jh ax w ax l ih1 z ax m))
  ("gradualist" nil (g r ae1 jh ax w ax l ih0 s t))
  ("gradually" nil (g r ae1 jh uw0 ax l iy0))
! ("graduate" n (g r ae1 jh ax w ax t))
! ("graduate" v (g r ae1 jh ax w ey0 t))
  ("graduated" nil (g r ae1 jh uw0 ey1 t ih0 d))
! ("graduates" n (g r ae1 jh ax w ax t s))
! ("graduates" v (g r ae1 jh ax w ey0 t s))
  ("graduating" nil (g r ae1 jh ax w ey1 t ih0 ng))
  ("graduation" nil (g r ae1 jh uw0 ey1 sh ax n))
  ("gradus" nil (g r ey1 d ax s))
***************
*** 44219,44225 ****
  ("hours" nil (aw1 er0 z))
  ("housand" nil (hh aw1 s ax n d))
  ("housden" nil (hh aw1 s d ax n))
! ("house" nil (hh aw1 s))
  ("houseal" nil (hh aw1 s ax l))
  ("houseboat" nil (hh aw1 s b ow1 t))
  ("housebroken" nil (hh aw1 s b r ow1 k ax n))
--- 44344,44351 ----
  ("hours" nil (aw1 er0 z))
  ("housand" nil (hh aw1 s ax n d))
  ("housden" nil (hh aw1 s d ax n))
! ("house" n (hh aw1 s))
! ("house" v (hh aw1 z))
  ("houseal" nil (hh aw1 s ax l))
  ("houseboat" nil (hh aw1 s b ow1 t))
  ("housebroken" nil (hh aw1 s b r ow1 k ax n))
***************
*** 44237,44243 ****
  ("housel" nil (hh aw1 s ax l))
  ("houseman" nil (hh aw1 s m ax n))
  ("houser" nil (hh aw1 z er0))
! ("houses" nil (hh aw1 s ax z))
  ("houseware" nil (hh aw1 s w eh1 r))
  ("housewares" nil (hh aw1 s w eh1 r z))
  ("housewife" nil (hh aw1 s w ay1 f))
--- 44363,44370 ----
  ("housel" nil (hh aw1 s ax l))
  ("houseman" nil (hh aw1 s m ax n))
  ("houser" nil (hh aw1 z er0))
! ("houses" n (hh aw1 s ax z))
! ("houses" v (hh aw1 z ih0 z))
  ("houseware" nil (hh aw1 s w eh1 r))
  ("housewares" nil (hh aw1 s w eh1 r z))
  ("housewife" nil (hh aw1 s w ay1 f))
***************
*** 45504,45513 ****
  ("imogen" nil (ih1 m ax g ax n))
  ("imogene" nil (ih1 m ax jh iy1 n))
  ("imondi" nil (ih0 m ow1 n d iy0))
! ("impact" nil (ih0 m p ae1 k t))
  ("impacted" nil (ih1 m p ae1 k t ih0 d))
  ("impacting" nil (ih0 m p ae1 k t ih0 ng))
! ("impacts" nil (ih0 m p ae1 k t s))
  ("impair" nil (ih0 m p eh1 r))
  ("impaired" nil (ih0 m p eh1 r d))
  ("impairing" nil (ih0 m p eh1 r ih0 ng))
--- 45631,45642 ----
  ("imogen" nil (ih1 m ax g ax n))
  ("imogene" nil (ih1 m ax jh iy1 n))
  ("imondi" nil (ih0 m ow1 n d iy0))
! ("impact" v (ih0 m p ae1 k t))
! ("impact" n (ih1 m p ae0 k t))
  ("impacted" nil (ih1 m p ae1 k t ih0 d))
  ("impacting" nil (ih0 m p ae1 k t ih0 ng))
! ("impacts" v (ih0 m p ae1 k t s))
! ("impacts" n (ih1 m p ae0 k t s))
  ("impair" nil (ih0 m p eh1 r))
  ("impaired" nil (ih0 m p eh1 r d))
  ("impairing" nil (ih0 m p eh1 r ih0 ng))
***************
*** 45590,45600 ****
  ("implants" nil (ih0 m p l ae1 n t s))
  ("implausible" nil (ih0 m p l ao1 z ax b ax l))
  ("implausibly" nil (ih0 m p l ao1 z ax b l iy0))
! ("implement" nil (ih1 m p l ax m ax n t))
  ("implementation" nil (ih1 m p l ax m eh0 n t ey1 sh ax n))
  ("implemented" nil (ih1 m p l ax m eh1 n t ax d))
  ("implementing" nil (ih1 m p l ax m eh1 n t ih0 ng))
! ("implements" nil (ih1 m p l ax m ax n t s))
  ("implicate" nil (ih1 m p l ih0 k ey1 t))
  ("implicated" nil (ih1 m p l ih0 k ey1 t ih0 d))
  ("implicating" nil (ih1 m p l ih0 k ey1 t ih0 ng))
--- 45719,45731 ----
  ("implants" nil (ih0 m p l ae1 n t s))
  ("implausible" nil (ih0 m p l ao1 z ax b ax l))
  ("implausibly" nil (ih0 m p l ao1 z ax b l iy0))
! ("implement" n (ih1 m p l ax m ax n t))
! ("implement" v (ih1 m p l ax m eh1 n t))
  ("implementation" nil (ih1 m p l ax m eh0 n t ey1 sh ax n))
  ("implemented" nil (ih1 m p l ax m eh1 n t ax d))
  ("implementing" nil (ih1 m p l ax m eh1 n t ih0 ng))
! ("implements" n (ih1 m p l ax m ax n t s))
! ("implements" v (ih1 m p l ax m eh1 n t s))
  ("implicate" nil (ih1 m p l ih0 k ey1 t))
  ("implicated" nil (ih1 m p l ih0 k ey1 t ih0 d))
  ("implicating" nil (ih1 m p l ih0 k ey1 t ih0 ng))
***************
*** 45617,45623 ****
  ("impolitic" nil (ih0 m p ao1 l ih1 t ih1 k))
  ("imponderable" nil (ih0 m p aa1 n d er0 ax b ax l))
  ("imponderables" nil (ih0 m p aa1 n d er0 ax b ax l z))
! ("import" nil (ih0 m p ao1 r t))
  ("importance" nil (ih0 m p ao1 r t ax n s))
  ("important" nil (ih0 m p ao1 r t ax n t))
  ("importantly" nil (ih0 m p ao1 r t ax n t l iy0))
--- 45748,45755 ----
  ("impolitic" nil (ih0 m p ao1 l ih1 t ih1 k))
  ("imponderable" nil (ih0 m p aa1 n d er0 ax b ax l))
  ("imponderables" nil (ih0 m p aa1 n d er0 ax b ax l z))
! ("import" v (ih0 m p ao1 r t))
! ("import" n (ih1 m p ao0 r t))
  ("importance" nil (ih0 m p ao1 r t ax n s))
  ("important" nil (ih0 m p ao1 r t ax n t))
  ("importantly" nil (ih0 m p ao1 r t ax n t l iy0))
***************
*** 45626,45632 ****
  ("importer" nil (ih0 m p ao1 r t er0))
  ("importers" nil (ih0 m p ao1 r t er0 z))
  ("importing" nil (ih0 m p ao1 r t ih0 ng))
! ("imports" nil (ih0 m p ao1 r t s))
  ("impose" nil (ih0 m p ow1 z))
  ("imposed" nil (ih0 m p ow1 z d))
  ("imposes" nil (ih0 m p ow1 z ax z))
--- 45758,45765 ----
  ("importer" nil (ih0 m p ao1 r t er0))
  ("importers" nil (ih0 m p ao1 r t er0 z))
  ("importing" nil (ih0 m p ao1 r t ih0 ng))
! ("imports" v (ih0 m p ao1 r t s))
! ("imports" n (ih1 m p ao0 r t s))
  ("impose" nil (ih0 m p ow1 z))
  ("imposed" nil (ih0 m p ow1 z d))
  ("imposes" nil (ih0 m p ow1 z ax z))
***************
*** 45653,45661 ****
  ("impregnated" nil (ih0 m p r eh1 g n ey1 t ax d))
  ("impregnation" nil (ih0 m p r eh1 g n ey1 sh ax n))
  ("impresario" nil (ih1 m p r ih0 s aa1 r iy0 ow1))
! ("impress" nil (ih0 m p r eh1 s))
  ("impressed" nil (ih0 m p r eh1 s t))
! ("impresses" nil (ih0 m p r eh1 s ih0 z))
  ("impressing" nil (ih0 m p r eh1 s ih0 ng))
  ("impression" nil (ih0 m p r eh1 sh ax n))
  ("impressionable" nil (ih0 m p r eh1 sh ax n ax b ax l))
--- 45786,45796 ----
  ("impregnated" nil (ih0 m p r eh1 g n ey1 t ax d))
  ("impregnation" nil (ih0 m p r eh1 g n ey1 sh ax n))
  ("impresario" nil (ih1 m p r ih0 s aa1 r iy0 ow1))
! ("impress" v (ih0 m p r eh1 s))
! ("impress" n (ih1 m p r eh0 s))
  ("impressed" nil (ih0 m p r eh1 s t))
! ("impresses" v (ih0 m p r eh1 s ih0 z))
! ("impresses" n (ih1 m p r eh0 s ih0 z))
  ("impressing" nil (ih0 m p r eh1 s ih0 ng))
  ("impression" nil (ih0 m p r eh1 sh ax n))
  ("impressionable" nil (ih0 m p r eh1 sh ax n ax b ax l))
***************
*** 45669,45677 ****
  ("impressment" nil (ih0 m p r eh1 s m ax n t))
  ("imprimatur" nil (ih1 m p r ih0 m aa1 t er0))
  ("imprimis" nil (ih0 m p r iy1 m ih0 s))
! ("imprint" nil (ih0 m p r ih1 n t))
  ("imprinted" nil (ih0 m p r ih1 n t ih0 d))
! ("imprints" nil (ih0 m p r ih1 n t s))
  ("imprison" nil (ih0 m p r ih1 z ax n))
  ("imprisoned" nil (ih0 m p r ih1 z ax n d))
  ("imprisoning" nil (ih0 m p r ih1 z ax n ih0 ng))
--- 45804,45814 ----
  ("impressment" nil (ih0 m p r eh1 s m ax n t))
  ("imprimatur" nil (ih1 m p r ih0 m aa1 t er0))
  ("imprimis" nil (ih0 m p r iy1 m ih0 s))
! ("imprint" v (ih0 m p r ih1 n t))
! ("imprint" n (ih1 m p r ih0 n t))
  ("imprinted" nil (ih0 m p r ih1 n t ih0 d))
! ("imprints" v (ih0 m p r ih1 n t s))
! ("imprints" n (ih1 m p r ih0 n t s))
  ("imprison" nil (ih0 m p r ih1 z ax n))
  ("imprisoned" nil (ih0 m p r ih1 z ax n d))
  ("imprisoning" nil (ih0 m p r ih1 z ax n ih0 ng))
***************
*** 45787,45793 ****
  ("incata" nil (ih0 ng k aa1 t ax))
  ("ince" nil (ih1 n s))
  ("incendiary" nil (ih0 n s eh1 n d iy0 eh0 r iy0))
! ("incense" nil (ih0 n s eh1 n s))
  ("incensed" nil (ih1 n s eh1 n s t))
  ("incentive" nil (ih0 n s eh1 n t ih0 v))
  ("incentives" nil (ih0 n s eh1 n t ih0 v z))
--- 45924,45931 ----
  ("incata" nil (ih0 ng k aa1 t ax))
  ("ince" nil (ih1 n s))
  ("incendiary" nil (ih0 n s eh1 n d iy0 eh0 r iy0))
! ("incense" v (ih0 n s eh1 n s))
! ("incense" n (ih1 n s eh1 n s))
  ("incensed" nil (ih1 n s eh1 n s t))
  ("incentive" nil (ih0 n s eh1 n t ih0 v))
  ("incentives" nil (ih0 n s eh1 n t ih0 v z))
***************
*** 45826,45834 ****
  ("inclement" nil (ih0 n k l eh1 m ax n t))
  ("inclination" nil (ih1 n k l ax n ey1 sh ax n))
  ("inclinations" nil (ih1 n k l ax n ey1 sh ax n z))
! ("incline" nil (ih0 n k l ay1 n))
  ("inclined" nil (ih0 n k l ay1 n d))
! ("inclines" nil (ih0 n k l ay1 n z))
  ("inclosure" nil (ih0 n k l ow1 zh er0))
  ("include" nil (ih0 n k l uw1 d))
  ("included" nil (ih0 n k l uw1 d ax d))
--- 45964,45974 ----
  ("inclement" nil (ih0 n k l eh1 m ax n t))
  ("inclination" nil (ih1 n k l ax n ey1 sh ax n))
  ("inclinations" nil (ih1 n k l ax n ey1 sh ax n z))
! ("incline" v (ih0 n k l ay1 n))
! ("incline" n (ih1 n k l ay0 n))
  ("inclined" nil (ih0 n k l ay1 n d))
! ("inclines" v (ih0 n k l ay1 n z))
! ("inclines" n (ih1 n k l ay0 n z))
  ("inclosure" nil (ih0 n k l ow1 zh er0))
  ("include" nil (ih0 n k l uw1 d))
  ("included" nil (ih0 n k l uw1 d ax d))
***************
*** 45886,45894 ****
  ("incorrectly" nil (ih0 n k er0 eh1 k t l iy0))
  ("incorrigible" nil (ih0 n k aa1 r ax jh ax b ax l))
  ("incorvaia" nil (ih0 n k ao0 r v aa1 y ax))
! ("increase" nil (ih0 n k r iy1 s))
  ("increased" nil (ih0 n k r iy1 s t))
! ("increases" nil (ih0 n k r iy1 s ax z))
  ("increasing" nil (ih0 n k r iy1 s ih0 ng))
  ("increasingly" nil (ih0 n k r iy1 s ih0 ng l iy0))
  ("incredible" nil (ih0 n k r eh1 d ax b ax l))
--- 46026,46036 ----
  ("incorrectly" nil (ih0 n k er0 eh1 k t l iy0))
  ("incorrigible" nil (ih0 n k aa1 r ax jh ax b ax l))
  ("incorvaia" nil (ih0 n k ao0 r v aa1 y ax))
! ("increase" v (ih0 n k r iy1 s))
! ("increase" n (ih1 n k r iy1 s))
  ("increased" nil (ih0 n k r iy1 s t))
! ("increases" v (ih0 n k r iy1 s ax z))
! ("increases" n (ih1 n k r iy0 s ax z))
  ("increasing" nil (ih0 n k r iy1 s ih0 ng))
  ("increasingly" nil (ih0 n k r iy1 s ih0 ng l iy0))
  ("incredible" nil (ih0 n k r eh1 d ax b ax l))
***************
*** 45946,45951 ****
--- 46088,46097 ----
  ("indemnifying" nil (ih0 n d eh1 m n ih0 f ay1 ih0 ng))
  ("indemnities" nil (ih0 n d eh1 m n ih0 t iy0 z))
  ("indemnity" nil (ih0 n d eh1 m n ax t iy0))
+ ("indent" v (ih0 n d eh1 n t))
+ ("indent" n (ih1 n d eh0 n t))
+ ("indents" v (ih0 n d eh1 n t s))
+ ("indents" n (ih1 n d eh0 n t s))
  ("indenture" nil (ih0 n d eh1 n ch er0))
  ("indentured" nil (ih0 n d eh1 n ch er0 d))
  ("indentures" nil (ih0 n d eh1 n ch er0 z))
***************
*** 46369,46377 ****
  ("initialed" nil (ih0 n ih1 sh ax l d))
  ("initially" nil (ih0 n ih1 sh ax l iy0))
  ("initials" nil (ih0 n ih1 sh ax l z))
! ("initiate" nil (ih0 n ih1 sh iy0 ey1 t))
  ("initiated" nil (ih0 n ih1 sh iy0 ey1 t ax d))
! ("initiates" nil (ih0 n ih1 sh iy0 ax t s))
  ("initiating" nil (ih0 n ih1 sh iy0 ey1 t ih0 ng))
  ("initiation" nil (ih0 n ih1 sh iy0 ey1 sh ax n))
  ("initiative" nil (ih0 n ih1 sh ax t ih0 v))
--- 46515,46525 ----
  ("initialed" nil (ih0 n ih1 sh ax l d))
  ("initially" nil (ih0 n ih1 sh ax l iy0))
  ("initials" nil (ih0 n ih1 sh ax l z))
! ("initiate" n (ih0 n ih1 sh iy0 ax t))
! ("initiate" v (ih0 n ih1 sh iy0 ey1 t))
  ("initiated" nil (ih0 n ih1 sh iy0 ey1 t ax d))
! ("initiates" n (ih0 n ih1 sh iy0 ax t s))
! ("initiates" v (ih0 n ih1 sh iy0 ey1 t s))
  ("initiating" nil (ih0 n ih1 sh iy0 ey1 t ih0 ng))
  ("initiation" nil (ih0 n ih1 sh iy0 ey1 sh ax n))
  ("initiative" nil (ih0 n ih1 sh ax t ih0 v))
***************
*** 46517,46527 ****
  ("insensitivity" nil (ih0 n s eh1 n s ax t ih1 v ax t iy0))
  ("inseparable" nil (ih0 n s eh1 p er0 ax b ax l))
  ("inserra" nil (ih0 n s eh1 r ax))
! ("insert" nil (ih0 n s er1 t))
  ("inserted" nil (ih0 n s er1 t ax d))
  ("inserting" nil (ih0 n s er1 t ih0 ng))
  ("insertion" nil (ih0 n s er1 sh ax n))
! ("inserts" nil (ih0 n s er1 t s))
  ("inset" nil (ih1 n s eh1 t))
  ("inshore" nil (ih1 n sh ao1 r))
  ("inside" nil (ih0 n s ay1 d))
--- 46665,46677 ----
  ("insensitivity" nil (ih0 n s eh1 n s ax t ih1 v ax t iy0))
  ("inseparable" nil (ih0 n s eh1 p er0 ax b ax l))
  ("inserra" nil (ih0 n s eh1 r ax))
! ("insert" v (ih0 n s er1 t))
! ("insert" n (ih1 n s er r t))
  ("inserted" nil (ih0 n s er1 t ax d))
  ("inserting" nil (ih0 n s er1 t ih0 ng))
  ("insertion" nil (ih0 n s er1 sh ax n))
! ("inserts" v (ih0 n s er1 t s))
! ("inserts" n (ih1 n s er0 t s))
  ("inset" nil (ih1 n s eh1 t))
  ("inshore" nil (ih1 n sh ao1 r))
  ("inside" nil (ih0 n s ay1 d))
***************
*** 46658,46667 ****
  ("insulator" nil (ih1 n s ax l ey1 t er0))
  ("insulators" nil (ih1 n s ax l ey1 t er0 z))
  ("insulin" nil (ih1 n s ax l ax n))
! ("insult" nil (ih0 n s ah1 l t))
  ("insulted" nil (ih0 n s ah1 l t ih0 d))
  ("insulting" nil (ih0 n s ah1 l t ih0 ng))
! ("insults" nil (ih0 n s ah1 l t s))
  ("insupportable" nil (ih0 n s ax p ao1 r t ax b ax l))
  ("insurance" nil (ih0 n sh uh1 r ax n s))
  ("insurances" nil (ih0 n sh uh1 r ax n s ih0 z))
--- 46808,46819 ----
  ("insulator" nil (ih1 n s ax l ey1 t er0))
  ("insulators" nil (ih1 n s ax l ey1 t er0 z))
  ("insulin" nil (ih1 n s ax l ax n))
! ("insult" v (ih0 n s ah1 l t))
! ("insult" n (ih1 n s ax l t))
  ("insulted" nil (ih0 n s ah1 l t ih0 d))
  ("insulting" nil (ih0 n s ah1 l t ih0 ng))
! ("insults" v (ih1 n s ax l t s))
! ("insults" n (ih0 n s ah1 l t s))
  ("insupportable" nil (ih0 n s ax p ao1 r t ax b ax l))
  ("insurance" nil (ih0 n sh uh1 r ax n s))
  ("insurances" nil (ih0 n sh uh1 r ax n s ih0 z))
***************
*** 46873,46879 ****
  ("intermodal" nil (ih1 n t er0 m ow1 d ax l))
  ("intermolecular" nil (ih1 n t er0 m ax l eh1 k y ax l er0))
  ("intermountain" nil (ih0 n t er0 m aw1 n t ih0 n))
! ("intern" nil (ih1 n t er0 n))
  ("internacional" nil (ih1 n t er0 n ae1 sh ax n ax l))
  ("internal" nil (ih0 n t er1 n ax l))
  ("internally" nil (ih0 n t er1 n ax l iy0))
--- 47025,47032 ----
  ("intermodal" nil (ih1 n t er0 m ow1 d ax l))
  ("intermolecular" nil (ih1 n t er0 m ax l eh1 k y ax l er0))
  ("intermountain" nil (ih0 n t er0 m aw1 n t ih0 n))
! ("intern" n (ih1 n t er0 n))
! ("intern" v (ih0 n t er1 n))
  ("internacional" nil (ih1 n t er0 n ae1 sh ax n ax l))
  ("internal" nil (ih0 n t er1 n ax l))
  ("internally" nil (ih0 n t er1 n ax l iy0))
***************
*** 46896,46902 ****
  ("internists" nil (ih0 n t er1 n ih0 s t s))
  ("internment" nil (ih0 n t er1 n m ax n t))
  ("internorth" nil (ih1 n t er0 n ao0 r th))
! ("interns" nil (ih1 n t er0 n z))
  ("internship" nil (ih1 n t er0 n sh ih1 p))
  ("internships" nil (ih1 n t er0 n sh ih1 p s))
  ("interoffice" nil (ih1 n t er0 ao1 f ax s))
--- 47049,47056 ----
  ("internists" nil (ih0 n t er1 n ih0 s t s))
  ("internment" nil (ih0 n t er1 n m ax n t))
  ("internorth" nil (ih1 n t er0 n ao0 r th))
! ("interns" v (ih1 n t er1 n z))
! ("interns" n (ih1 n t er0 n z))
  ("internship" nil (ih1 n t er0 n sh ih1 p))
  ("internships" nil (ih1 n t er0 n sh ih1 p s))
  ("interoffice" nil (ih1 n t er0 ao1 f ax s))
***************
*** 47080,47086 ****
  ("invaders" nil (ih0 n v ey1 d er0 z))
  ("invades" nil (ih0 n v ey1 d z))
  ("invading" nil (ih0 n v ey1 d ih0 ng))
! ("invalid" nil (ih1 n v ax l ax d))
  ("invalidate" nil (ih0 n v ae1 l ih0 d ey1 t))
  ("invalidated" nil (ih0 n v ae1 l ax d ey1 t ax d))
  ("invalidating" nil (ih0 n v ae1 l ax d ey1 t ih0 ng))
--- 47234,47241 ----
  ("invaders" nil (ih0 n v ey1 d er0 z))
  ("invades" nil (ih0 n v ey1 d z))
  ("invading" nil (ih0 n v ey1 d ih0 ng))
! ("invalid" n (ih1 n v ax l ax d))
! ("invalid" j (ih1 n v ah1 l ax d))
  ("invalidate" nil (ih0 n v ae1 l ih0 d ey1 t))
  ("invalidated" nil (ih0 n v ae1 l ax d ey1 t ax d))
  ("invalidating" nil (ih0 n v ae1 l ax d ey1 t ih0 ng))
***************
*** 47156,47164 ****
  ("invitation" nil (ih1 n v ih0 t ey1 sh ax n))
  ("invitational" nil (ih1 n v ax t ey1 sh ax n ax l))
  ("invitations" nil (ih1 n v ih0 t ey1 sh ax n z))
! ("invite" nil (ih0 n v ay1 t))
  ("invited" nil (ih0 n v ay1 t ax d))
! ("invites" nil (ih0 n v ay1 t s))
  ("inviting" nil (ih0 n v ay1 t ih0 ng))
  ("invitron" nil (ih1 n v ih0 t r aa0 n))
  ("invocation" nil (ih1 n v ax k ey1 sh ax n))
--- 47311,47321 ----
  ("invitation" nil (ih1 n v ih0 t ey1 sh ax n))
  ("invitational" nil (ih1 n v ax t ey1 sh ax n ax l))
  ("invitations" nil (ih1 n v ih0 t ey1 sh ax n z))
! ("invite" v (ih0 n v ay1 t))
! ("invite" n (ih1 n v ay0 t))
  ("invited" nil (ih0 n v ay1 t ax d))
! ("invites" v (ih0 n v ay1 t s))
! ("invites" n (ih1 n v ay0 t s))
  ("inviting" nil (ih0 n v ay1 t ih0 ng))
  ("invitron" nil (ih1 n v ih0 t r aa0 n))
  ("invocation" nil (ih1 n v ax k ey1 sh ax n))
***************
*** 53637,53643 ****
  ("leaching" nil (l iy1 ch ih0 ng))
  ("leachman" nil (l iy1 ch m ax n))
  ("leacock" nil (l iy1 k aa1 k))
! ("lead" nil (l eh1 d))
  ("leadbetter" nil (l iy1 d b ih0 t er0))
  ("leaded" nil (l eh1 d ih0 d))
  ("leaden" nil (l eh1 d ax n))
--- 53794,53802 ----
  ("leaching" nil (l iy1 ch ih0 ng))
  ("leachman" nil (l iy1 ch m ax n))
  ("leacock" nil (l iy1 k aa1 k))
! ("lead" n (l eh1 d))
! ("lead" v_p (l eh1 d))
! ("lead" v (l iy1 d))
  ("leadbetter" nil (l iy1 d b ih0 t er0))
  ("leaded" nil (l eh1 d ih0 d))
  ("leaden" nil (l eh1 d ax n))
***************
*** 55473,55479 ****
  ("liuzza" nil (l iy0 uw1 t s ax))
  ("liuzzi" nil (l iy0 uw1 t s iy0))
  ("livable" nil (l ih1 v ax b ax l))
! ("live" nil (l ay1 v))
  ("lived" nil (l ay1 v d))
  ("livelier" nil (l ih0 v eh1 l y er0))
  ("liveliest" nil (l ih1 v ax l iy1 s t))
--- 55632,55639 ----
  ("liuzza" nil (l iy0 uw1 t s ax))
  ("liuzzi" nil (l iy0 uw1 t s iy0))
  ("livable" nil (l ih1 v ax b ax l))
! ("live" j (l ay1 v))
! ("live" v (l ih1 v))
  ("lived" nil (l ay1 v d))
  ("livelier" nil (l ih0 v eh1 l y er0))
  ("liveliest" nil (l ih1 v ax l iy1 s t))
***************
*** 55493,55499 ****
  ("liverpool" nil (l ih1 v er0 p uw1 l))
  ("livers" nil (l ih1 v er0 z))
  ("liverworts" nil (l ih1 v er0 w er0 t s))
! ("lives" nil (l ih1 v z))
  ("livesay" nil (l ih1 v ih0 s ey0))
  ("livesey" nil (l ih1 v ih0 s iy0))
  ("livestock" nil (l ay1 v s t aa1 k))
--- 55653,55660 ----
  ("liverpool" nil (l ih1 v er0 p uw1 l))
  ("livers" nil (l ih1 v er0 z))
  ("liverworts" nil (l ih1 v er0 w er0 t s))
! ("lives" v (l ih1 v z))
! ("lives" n (l ay1 v z))
  ("livesay" nil (l ih1 v ih0 s ey0))
  ("livesey" nil (l ih1 v ih0 s iy0))
  ("livestock" nil (l ay1 v s t aa1 k))
***************
*** 62207,62213 ****
  ("minus" nil (m ay1 n ax s))
  ("minuscule" nil (m ih1 n ax s k y uw1 l))
  ("minuses" nil (m ay1 n ax s ih0 z))
! ("minute" nil (m ih1 n ax t))
  ("minutely" nil (m ih1 n ax t l iy0))
  ("minuteman" nil (m ih1 n ax t m ae1 n))
  ("minutes" nil (m ih1 n ax t s))
--- 62368,62375 ----
  ("minus" nil (m ay1 n ax s))
  ("minuscule" nil (m ih1 n ax s k y uw1 l))
  ("minuses" nil (m ay1 n ax s ih0 z))
! ("minute" n (m ih1 n ax t))
! ("minute" j (m ih1 n uw0 t))
  ("minutely" nil (m ih1 n ax t l iy0))
  ("minuteman" nil (m ih1 n ax t m ae1 n))
  ("minutes" nil (m ih1 n ax t s))
***************
*** 62313,62319 ****
  ("misconceived" nil (m ih0 s k ax n s iy1 v d))
  ("misconception" nil (m ih0 s k ax n s eh1 p sh ax n))
  ("misconceptions" nil (m ih1 s k ax n s eh1 p sh ax n z))
! ("misconduct" nil (m ih0 s k aa1 n d ax k t))
  ("misconstrue" nil (m ih0 s k ax n s t r uw1))
  ("misconstrued" nil (m ih1 s k ax n s t r uw1 d))
  ("miscount" nil (m ih1 s k aw1 n t))
--- 62475,62482 ----
  ("misconceived" nil (m ih0 s k ax n s iy1 v d))
  ("misconception" nil (m ih0 s k ax n s eh1 p sh ax n))
  ("misconceptions" nil (m ih1 s k ax n s eh1 p sh ax n z))
! ("misconduct" n (m ih0 s k aa1 n d ax k t))
! ("misconduct" v (m ih0 s k ax n d ah1 k t))
  ("misconstrue" nil (m ih0 s k ax n s t r uw1))
  ("misconstrued" nil (m ih1 s k ax n s t r uw1 d))
  ("miscount" nil (m ih1 s k aw1 n t))
***************
*** 62401,62407 ****
  ("misplace" nil (m ih0 s p l ey1 s))
  ("misplaced" nil (m ih0 s p l ey1 s t))
  ("mispriced" nil (m ih0 s p r ay1 s t))
! ("misprint" nil (m ih1 s p r ih1 n t))
  ("misprision" nil (m ih0 s p r ih1 zh ax n))
  ("misquote" nil (m ih0 s k w ow1 t))
  ("misquoted" nil (m ih0 s k w ow1 t ih0 d))
--- 62564,62573 ----
  ("misplace" nil (m ih0 s p l ey1 s))
  ("misplaced" nil (m ih0 s p l ey1 s t))
  ("mispriced" nil (m ih0 s p r ay1 s t))
! ("misprint" v (m ih0 s p r ih1 n t))
! ("misprint" n (m ih1 s p r ih0 n t))
! ("misprints" v (m ih0 s p r ih1 n t s))
! ("misprints" n (m ih1 s p r ih0 n t s))
  ("misprision" nil (m ih0 s p r ih1 zh ax n))
  ("misquote" nil (m ih0 s k w ow1 t))
  ("misquoted" nil (m ih0 s k w ow1 t ih0 d))
***************
*** 62487,62495 ****
  ("misunderstandings" nil (m ih0 s ah1 n d er0 s t ae1 n d ih0 ng z))
  ("misunderstood" nil (m ih1 s ax n d er0 s t uh1 d))
  ("misuraca" nil (m ih0 s uh0 r aa1 k ax))
! ("misuse" nil (m ih0 s y uw1 z))
  ("misused" nil (m ih0 s y uw1 z d))
! ("misuses" nil (m ih0 s y uw1 z ih0 z))
  ("misusing" nil (m ih0 s y uw1 z ih0 ng))
  ("mita" nil (m iy1 t ax))
  ("mitamura" nil (m iy1 t ax m uh1 r ax))
--- 62653,62663 ----
  ("misunderstandings" nil (m ih0 s ah1 n d er0 s t ae1 n d ih0 ng z))
  ("misunderstood" nil (m ih1 s ax n d er0 s t uh1 d))
  ("misuraca" nil (m ih0 s uh0 r aa1 k ax))
! ("misuse" n (m ih0 s y uw1 s))
! ("misuse" v (m ih0 s y uw1 z))
  ("misused" nil (m ih0 s y uw1 z d))
! ("misuses" n (m ih0 s y uw1 s ih0 z))
! ("misuses" v (m ih0 s y uw1 z ih0 z))
  ("misusing" nil (m ih0 s y uw1 z ih0 ng))
  ("mita" nil (m iy1 t ax))
  ("mitamura" nil (m iy1 t ax m uh1 r ax))
***************
*** 62676,62685 ****
  ("moden" nil (m ow1 d ax n))
  ("modena" nil (m ow0 d eh1 n ax))
  ("moder" nil (m ow1 d er0))
! ("moderate" nil (m aa1 d er0 ax t))
  ("moderated" nil (m aa1 d er0 ey1 t ih0 d))
  ("moderately" nil (m aa1 d er0 ax t l iy0))
! ("moderates" nil (m aa1 d er0 ax t s))
  ("moderating" nil (m aa1 d er0 ey1 t ih0 ng))
  ("moderation" nil (m aa1 d er0 ey1 sh ax n))
  ("moderator" nil (m aa1 d er0 ey1 t er0))
--- 62844,62855 ----
  ("moden" nil (m ow1 d ax n))
  ("modena" nil (m ow0 d eh1 n ax))
  ("moder" nil (m ow1 d er0))
! ("moderate" n (m aa1 d er0 ax t))
! ("moderate" v (m aa1 d er0 ey0 t))
  ("moderated" nil (m aa1 d er0 ey1 t ih0 d))
  ("moderately" nil (m aa1 d er0 ax t l iy0))
! ("moderates" n (m aa1 d er0 ax t s))
! ("moderates" y (m aa1 d er0 ey0 t s))
  ("moderating" nil (m aa1 d er0 ey1 t ih0 ng))
  ("moderation" nil (m aa1 d er0 ey1 sh ax n))
  ("moderator" nil (m aa1 d er0 ey1 t er0))
***************
*** 63762,63768 ****
  ("mousse" nil (m uw1 s))
  ("mousseau" nil (m uw1 s ow1))
  ("moustache" nil (m ah1 s t ae1 sh))
! ("mouth" nil (m aw1 th))
  ("mouthed" nil (m aw1 dh d))
  ("mouthful" nil (m aw1 th f uh1 l))
  ("mouthing" nil (m aw1 dh ih0 ng))
--- 63932,63939 ----
  ("mousse" nil (m uw1 s))
  ("mousseau" nil (m uw1 s ow1))
  ("moustache" nil (m ah1 s t ae1 sh))
! ("mouth" n (m aw1 th))
! ("mouth" v (m aw1 dh))
  ("mouthed" nil (m aw1 dh d))
  ("mouthful" nil (m aw1 th f uh1 l))
  ("mouthing" nil (m aw1 dh ih0 ng))
***************
*** 66921,66927 ****
  ("obits" nil (aa1 b ih0 t s))
  ("obituaries" nil (ow0 b ih1 ch uw0 eh1 r iy0 z))
  ("obituary" nil (ow0 b ih1 ch uw0 eh1 r iy0))
! ("object" nil (aa1 b jh eh0 k t))
  ("objected" nil (ax b jh eh1 k t ax d))
  ("objecting" nil (ax b jh eh1 k t ih0 ng))
  ("objection" nil (ax b jh eh1 k sh ax n))
--- 67092,67099 ----
  ("obits" nil (aa1 b ih0 t s))
  ("obituaries" nil (ow0 b ih1 ch uw0 eh1 r iy0 z))
  ("obituary" nil (ow0 b ih1 ch uw0 eh1 r iy0))
! ("object" n (aa1 b jh eh0 k t))
! ("object" v (ax b jh eh1 k t))
  ("objected" nil (ax b jh eh1 k t ax d))
  ("objecting" nil (ax b jh eh1 k t ih0 ng))
  ("objection" nil (ax b jh eh1 k sh ax n))
***************
*** 66933,66939 ****
  ("objectivity" nil (aa1 b jh eh0 k t ih1 v ih0 t iy0))
  ("objector" nil (ax b jh eh1 k t er0))
  ("objectors" nil (ax b jh eh1 k t er0 z))
! ("objects" nil (aa1 b jh eh0 k t s))
  ("oblak" nil (aa1 b l ax k))
  ("oblander" nil (aa1 b l ax n d er0))
  ("oblast" nil (aa1 b l ae0 s t))
--- 67105,67112 ----
  ("objectivity" nil (aa1 b jh eh0 k t ih1 v ih0 t iy0))
  ("objector" nil (ax b jh eh1 k t er0))
  ("objectors" nil (ax b jh eh1 k t er0 z))
! ("objects" n (aa1 b jh eh0 k t s))
! ("objects" v (ax b jh eh1 k t s))
  ("oblak" nil (aa1 b l ax k))
  ("oblander" nil (aa1 b l ax n d er0))
  ("oblast" nil (aa1 b l ae0 s t))
***************
*** 68612,68618 ****
  ("overacker" nil (ow1 v er0 ax k er0))
  ("overacted" nil (ow1 v er0 ae1 k t ih0 d))
  ("overactive" nil (ow1 v er0 ae1 k t ih0 v))
! ("overall" nil (ow1 v er0 ao1 l))
  ("overallotment" nil (ow1 v er0 ax l aa1 t m ax n t))
  ("overallotments" nil (ow1 v er0 ax l aa1 t m ax n t s))
  ("overalls" nil (ow1 v er0 ao1 l z))
--- 68785,68792 ----
  ("overacker" nil (ow1 v er0 ax k er0))
  ("overacted" nil (ow1 v er0 ae1 k t ih0 d))
  ("overactive" nil (ow1 v er0 ae1 k t ih0 v))
! ("overall" j (ow1 v er0 ao1 l))
! ("overall" n (ow1 v er0 ao1 l))
  ("overallotment" nil (ow1 v er0 ax l aa1 t m ax n t))
  ("overallotments" nil (ow1 v er0 ax l aa1 t m ax n t s))
  ("overalls" nil (ow1 v er0 ao1 l z))
***************
*** 70888,70894 ****
  ("peretz" nil (p er1 ih0 t s))
  ("pereyra" nil (p er0 eh1 r ax))
  ("perez" nil (p eh1 r eh0 z))
! ("perfect" nil (p er0 f eh1 k t))
  ("perfecta" nil (p er0 f eh1 k t ax))
  ("perfected" nil (p er0 f eh1 k t ax d))
  ("perfecting" nil (p er0 f eh1 k t ih0 ng))
--- 71062,71069 ----
  ("peretz" nil (p er1 ih0 t s))
  ("pereyra" nil (p er0 eh1 r ax))
  ("perez" nil (p eh1 r eh0 z))
! ("perfect" j (p er1 f ax k t))
! ("perfect" v (p er0 f eh1 k t))
  ("perfecta" nil (p er0 f eh1 k t ax))
  ("perfected" nil (p er0 f eh1 k t ax d))
  ("perfecting" nil (p er0 f eh1 k t ih0 ng))
***************
*** 70998,71005 ****
  ("permissions" nil (p er0 m ih1 sh ax n z))
  ("permissive" nil (p er0 m ih1 s ih0 v))
  ("permissiveness" nil (p er0 m ih1 s ih0 v n ax s))
! ("permit" nil (p er0 m ih1 t))
! ("permits" nil (p er0 m ih1 t s))
  ("permitted" nil (p er0 m ih1 t ax d))
  ("permitting" nil (p er0 m ih1 t ih0 ng))
  ("permut" nil (p er1 m ax t))
--- 71173,71182 ----
  ("permissions" nil (p er0 m ih1 sh ax n z))
  ("permissive" nil (p er0 m ih1 s ih0 v))
  ("permissiveness" nil (p er0 m ih1 s ih0 v n ax s))
! ("permit" v (p er0 m ih1 t))
! ("permit" n (p er1 m ih0 t))
! ("permits" v (p er0 m ih1 t s))
! ("permits" n (p er1 m ih0 t s))
  ("permitted" nil (p er0 m ih1 t ax d))
  ("permitting" nil (p er0 m ih1 t ih0 ng))
  ("permut" nil (p er1 m ax t))
***************
*** 71188,71197 ****
  ("perversely" nil (p er0 v er1 s l iy0))
  ("perversion" nil (p er0 v er1 zh ax n))
  ("perversity" nil (p er0 v er1 s ax t iy0))
! ("pervert" nil (p er1 v er0 t))
  ("perverted" nil (p er0 v er1 t ih0 d))
  ("perverting" nil (p er0 v er1 t ih0 ng))
! ("perverts" nil (p er1 v er0 t s))
  ("perz" nil (p er1 z))
  ("pesatori" nil (p eh1 s ax t ao1 r iy0))
  ("pesavento" nil (p eh1 s ax v eh1 n t ow0))
--- 71365,71376 ----
  ("perversely" nil (p er0 v er1 s l iy0))
  ("perversion" nil (p er0 v er1 zh ax n))
  ("perversity" nil (p er0 v er1 s ax t iy0))
! ("pervert" v (p er0 v er1 t))
! ("pervert" n (p er1 v er0 t))
  ("perverted" nil (p er0 v er1 t ih0 d))
  ("perverting" nil (p er0 v er1 t ih0 ng))
! ("perverts" v (p er0 v er1 t s))
! ("perverts" n (p er1 v er0 t s))
  ("perz" nil (p er1 z))
  ("pesatori" nil (p eh1 s ax t ao1 r iy0))
  ("pesavento" nil (p eh1 s ax v eh1 n t ow0))
***************
*** 73765,73773 ****
  ("predetermined" nil (p r iy1 d iy0 t er1 m ih0 n d))
  ("predicament" nil (p r ih0 d ih1 k ax m ax n t))
  ("predicaments" nil (p r ih0 d ih1 k ax m ax n t s))
! ("predicate" nil (p r eh1 d ax k ey1 t))
  ("predicated" nil (p r eh1 d ax k ey1 t ih0 d))
! ("predicates" nil (p r eh1 d ax k ey1 t s))
  ("predict" nil (p r ih0 d ih1 k t))
  ("predictability" nil (p r ih0 d ih1 k t ax b ih1 l ih0 t iy0))
  ("predictable" nil (p r ih0 d ih1 k t ax b ax l))
--- 73944,73954 ----
  ("predetermined" nil (p r iy1 d iy0 t er1 m ih0 n d))
  ("predicament" nil (p r ih0 d ih1 k ax m ax n t))
  ("predicaments" nil (p r ih0 d ih1 k ax m ax n t s))
! ("predicate" v (p r eh1 d ih0 k ey1 t))
! ("predicate" n (p r eh1 d ih0 k ax t))
  ("predicated" nil (p r eh1 d ax k ey1 t ih0 d))
! ("predicates" v (p r eh1 d ax k ey1 t s))
! ("predicates" n (p r eh1 d ih0 k ax t s))
  ("predict" nil (p r ih0 d ih1 k t))
  ("predictability" nil (p r ih0 d ih1 k t ax b ih1 l ih0 t iy0))
  ("predictable" nil (p r ih0 d ih1 k t ax b ax l))
***************
*** 73956,73962 ****
  ("prescriptions" nil (p r ax s k r ih1 p sh ax n z))
  ("presence" nil (p r eh1 z ax n s))
  ("presences" nil (p r eh1 z ax n s ih0 z))
! ("present" nil (p r eh1 z ax n t))
  ("presentable" nil (p r ax z eh1 n t ax b ax l))
  ("presentation" nil (p r eh1 z ax n t ey1 sh ax n))
  ("presentations" nil (p r eh1 z ax n t ey1 sh ax n z))
--- 74137,74144 ----
  ("prescriptions" nil (p r ax s k r ih1 p sh ax n z))
  ("presence" nil (p r eh1 z ax n s))
  ("presences" nil (p r eh1 z ax n s ih0 z))
! ("present" n (p r eh1 z ax n t))
! ("present" v (p r iy0 z eh1 n t))
  ("presentable" nil (p r ax z eh1 n t ax b ax l))
  ("presentation" nil (p r eh1 z ax n t ey1 sh ax n))
  ("presentations" nil (p r eh1 z ax n t ey1 sh ax n z))
***************
*** 73966,73972 ****
  ("presenting" nil (p r ih0 z eh1 n t ih0 ng))
  ("presently" nil (p r eh1 z ax n t l iy0))
  ("presentment" nil (p r iy0 z eh1 n t m ax n t))
! ("presents" nil (p r eh1 z ax n t s))
  ("preservation" nil (p r eh1 z er0 v ey1 sh ax n))
  ("preservationist" nil (p r eh1 z er0 v ey1 sh ax n ih0 s t))
  ("preservationists" nil (p r eh1 z er0 v ey1 sh ax n ih0 s t s))
--- 74148,74155 ----
  ("presenting" nil (p r ih0 z eh1 n t ih0 ng))
  ("presently" nil (p r eh1 z ax n t l iy0))
  ("presentment" nil (p r iy0 z eh1 n t m ax n t))
! ("presents" n (p r eh1 z ax n t s))
! ("presents" v (p r iy0 z eh1 n t s))
  ("preservation" nil (p r eh1 z er0 v ey1 sh ax n))
  ("preservationist" nil (p r eh1 z er0 v ey1 sh ax n ih0 s t))
  ("preservationists" nil (p r eh1 z er0 v ey1 sh ax n ih0 s t s))
***************
*** 74342,74352 ****
  ("procedural" nil (p r ax s iy1 jh er0 ax l))
  ("procedure" nil (p r ax s iy1 jh er0))
  ("procedures" nil (p r ax s iy1 jh er0 z))
! ("proceed" nil (p r ax s iy1 d))
  ("proceeded" nil (p r ax s iy1 d ax d))
  ("proceeding" nil (p r ax s iy1 d ih0 ng))
  ("proceedings" nil (p r ow0 s iy1 d ih0 ng z))
! ("proceeds" nil (p r ax s iy1 d z))
  ("procell" nil (p r ow0 s eh1 l))
  ("proceso" nil (p r ow1 s eh1 s ow0))
  ("process" nil (p r aa1 s eh1 s))
--- 74525,74537 ----
  ("procedural" nil (p r ax s iy1 jh er0 ax l))
  ("procedure" nil (p r ax s iy1 jh er0))
  ("procedures" nil (p r ax s iy1 jh er0 z))
! ("proceed" v (p r ax s iy1 d))
! ("proceed" n (p r ow0 s iy1 d))
  ("proceeded" nil (p r ax s iy1 d ax d))
  ("proceeding" nil (p r ax s iy1 d ih0 ng))
  ("proceedings" nil (p r ow0 s iy1 d ih0 ng z))
! ("proceeds" v (p r ax s iy1 d z))
! ("proceeds" n (p r ow0 s iy1 d z))
  ("procell" nil (p r ow0 s eh1 l))
  ("proceso" nil (p r ow1 s eh1 s ow0))
  ("process" nil (p r aa1 s eh1 s))
***************
*** 74403,74413 ****
  ("prodigiously" nil (p r ow0 d ih1 jh ih0 s l iy0))
  ("prodigy" nil (p r aa1 d ax jh iy0))
  ("prods" nil (p r aa1 d z))
! ("produce" nil (p r ax d uw1 s))
  ("produced" nil (p r ax d uw1 s t))
  ("producer" nil (p r ax d uw1 s er0))
  ("producers" nil (p r ax d uw1 s er0 z))
! ("produces" nil (p r ax d uw1 s ax z))
  ("producing" nil (p r ax d uw1 s ih0 ng))
  ("product" nil (p r aa1 d ax k t))
  ("production" nil (p r ax d ah1 k sh ax n))
--- 74588,74600 ----
  ("prodigiously" nil (p r ow0 d ih1 jh ih0 s l iy0))
  ("prodigy" nil (p r aa1 d ax jh iy0))
  ("prods" nil (p r aa1 d z))
! ("produce" v (p r ax d uw1 s))
! ("produce" n (p r ow1 d uw0 s))
  ("produced" nil (p r ax d uw1 s t))
  ("producer" nil (p r ax d uw1 s er0))
  ("producers" nil (p r ax d uw1 s er0 z))
! ("produces" v (p r ax d uw1 s ax z))
! ("produces" n (p r ax d uw1 s ih0 z))
  ("producing" nil (p r ax d uw1 s ih0 ng))
  ("product" nil (p r aa1 d ax k t))
  ("production" nil (p r ax d ah1 k sh ax n))
***************
*** 74488,74494 ****
  ("programmers" nil (p r ow1 g r ae1 m er0 z))
  ("programming" nil (p r ow1 g r ae1 m ih0 ng))
  ("programs" nil (p r ow1 g r ae1 m z))
! ("progress" nil (p r aa1 g r eh1 s))
  ("progressed" nil (p r ax g r eh1 s t))
  ("progresses" nil (p r aa1 g r eh1 s ax z))
  ("progressing" nil (p r ax g r eh1 s ih0 ng))
--- 74675,74682 ----
  ("programmers" nil (p r ow1 g r ae1 m er0 z))
  ("programming" nil (p r ow1 g r ae1 m ih0 ng))
  ("programs" nil (p r ow1 g r ae1 m z))
! ("progress" n (p r aa1 g r eh1 s))
! ("progress" v (p r ax g r eh1 s))
  ("progressed" nil (p r ax g r eh1 s t))
  ("progresses" nil (p r aa1 g r eh1 s ax z))
  ("progressing" nil (p r ax g r eh1 s ih0 ng))
***************
*** 74509,74515 ****
  ("prohibits" nil (p r ow0 hh ih1 b ax t s))
  ("proia" nil (p r ow1 y ax))
  ("proietti" nil (p r oy0 eh1 t iy0))
! ("project" nil (p r aa1 jh eh0 k t))
  ("projected" nil (p r ax jh eh1 k t ax d))
  ("projectile" nil (p r ax jh eh1 k t ax l))
  ("projectiles" nil (p r ax jh eh1 k t ax l z))
--- 74697,74704 ----
  ("prohibits" nil (p r ow0 hh ih1 b ax t s))
  ("proia" nil (p r ow1 y ax))
  ("proietti" nil (p r oy0 eh1 t iy0))
! ("project" n (p r aa1 jh eh0 k t))
! ("project" v (p r ax jh eh1 k t))
  ("projected" nil (p r ax jh eh1 k t ax d))
  ("projectile" nil (p r ax jh eh1 k t ax l))
  ("projectiles" nil (p r ax jh eh1 k t ax l z))
***************
*** 74519,74525 ****
  ("projective" nil (p r ax jh eh1 k t ih0 v))
  ("projector" nil (p r ax jh eh1 k t er0))
  ("projectors" nil (p r ax jh eh1 k t er0 z))
! ("projects" nil (p r aa1 jh eh0 k t s))
  ("prokofiev" nil (p r aa1 k ow0 f iy1 v))
  ("prokop" nil (p r ow1 k ax p))
  ("prolactin" nil (p r ow0 l ae1 k t ax n))
--- 74708,74715 ----
  ("projective" nil (p r ax jh eh1 k t ih0 v))
  ("projector" nil (p r ax jh eh1 k t er0))
  ("projectors" nil (p r ax jh eh1 k t er0 z))
! ("projects" n (p r aa1 jh eh0 k t s))
! ("projects" v (p r ax jh eh1 k t s))
  ("prokofiev" nil (p r aa1 k ow0 f iy1 v))
  ("prokop" nil (p r ow1 k ax p))
  ("prolactin" nil (p r ow0 l ae1 k t ax n))
***************
*** 74751,74757 ****
  ("proteges" nil (p r ow1 t ih0 z ey1 z))
  ("protein" nil (p r ow1 t iy1 n))
  ("proteins" nil (p r ow1 t iy1 n z))
! ("protest" nil (p r ow1 t eh1 s t))
  ("protestant" nil (p r aa1 t ax s t ax n t))
  ("protestantism" nil (p r aa1 t ax s t ax n t ih1 z ax m))
  ("protestants" nil (p r aa1 t ax s t ax n t s))
--- 74941,74948 ----
  ("proteges" nil (p r ow1 t ih0 z ey1 z))
  ("protein" nil (p r ow1 t iy1 n))
  ("proteins" nil (p r ow1 t iy1 n z))
! ("protest" n (p r ow1 t eh0 s t))
! ("protest" v (p r ow1 t eh1 s t))
  ("protestant" nil (p r aa1 t ax s t ax n t))
  ("protestantism" nil (p r aa1 t ax s t ax n t ih1 z ax m))
  ("protestants" nil (p r aa1 t ax s t ax n t s))
***************
*** 74761,74767 ****
  ("protesters" nil (p r ow1 t eh1 s t er0 z))
  ("protesting" nil (p r ax t eh1 s t ih0 ng))
  ("protestors" nil (p r ow1 t eh1 s t er0 z))
! ("protests" nil (p r ow1 t eh1 s t s))
  ("prothallus" nil (p r ow1 th ae1 l ax s))
  ("prothero" nil (p r aa1 dh er0 ow1))
  ("prothorax" nil (p r ow0 th ao1 r ae0 k s))
--- 74952,74959 ----
  ("protesters" nil (p r ow1 t eh1 s t er0 z))
  ("protesting" nil (p r ax t eh1 s t ih0 ng))
  ("protestors" nil (p r ow1 t eh1 s t er0 z))
! ("protests" n (p r ow1 t eh0 s t s))
! ("protests" v (p r ow1 t eh1 s t s))
  ("prothallus" nil (p r ow1 th ae1 l ax s))
  ("prothero" nil (p r aa1 dh er0 ow1))
  ("prothorax" nil (p r ow0 th ao1 r ae0 k s))
***************
*** 76198,76206 ****
  ("ramonda" nil (r ax m aa1 n d ax))
  ("ramos" nil (r aa1 m ow0 s))
  ("ramp" nil (r ae1 m p))
! ("rampage" nil (r ae1 m p ey1 jh))
  ("rampaged" nil (r ae0 m p ey1 jh d))
! ("rampages" nil (r ae1 m p ey1 jh ih0 z))
  ("rampant" nil (r ae1 m p ax n t))
  ("ramparts" nil (r ae1 m p aa1 r t s))
  ("rampell" nil (r ae0 m p eh1 l))
--- 76390,76400 ----
  ("ramonda" nil (r ax m aa1 n d ax))
  ("ramos" nil (r aa1 m ow0 s))
  ("ramp" nil (r ae1 m p))
! ("rampage" n (r ae1 m p ey1 jh))
! ("rampage" v (r ae0 m p ey1 jh))
  ("rampaged" nil (r ae0 m p ey1 jh d))
! ("rampages" n (r ae1 m p ey1 jh ih0 z))
! ("rampages" v (r ae0 m p ey1 jh ih0 z))
  ("rampant" nil (r ae1 m p ax n t))
  ("ramparts" nil (r ae1 m p aa1 r t s))
  ("rampell" nil (r ae0 m p eh1 l))
***************
*** 76699,76705 ****
  ("readies" nil (r eh1 d iy0 z))
  ("readily" nil (r eh1 d ax l iy0))
  ("readiness" nil (r eh1 d iy0 n ax s))
! ("reading" nil (r eh1 d ih0 ng))
  ("readinger" nil (r eh1 d ih0 ng er0))
  ("readings" nil (r eh1 d ih0 ng z))
  ("readjust" nil (r iy1 ax jh ah1 s t))
--- 76893,76900 ----
  ("readies" nil (r eh1 d iy0 z))
  ("readily" nil (r eh1 d ax l iy0))
  ("readiness" nil (r eh1 d iy0 n ax s))
! ("reading" n (r eh1 d ih0 ng))
! ("reading" v (r iy1 d ih0 ng))
  ("readinger" nil (r eh1 d ih0 ng er0))
  ("readings" nil (r eh1 d ih0 ng z))
  ("readjust" nil (r iy1 ax jh ah1 s t))
***************
*** 76871,76877 ****
  ("rebeck" nil (r iy1 b eh0 k))
  ("rebeka" nil (r ih0 b iy1 k ax))
  ("rebekka" nil (r ih0 b eh1 k ax))
! ("rebel" nil (r eh1 b ax l))
  ("rebelled" nil (r ih0 b eh1 l d))
  ("rebelling" nil (r ih0 b eh1 l ih0 ng))
  ("rebellion" nil (r ih0 b eh1 l y ax n))
--- 77066,77073 ----
  ("rebeck" nil (r iy1 b eh0 k))
  ("rebeka" nil (r ih0 b iy1 k ax))
  ("rebekka" nil (r ih0 b eh1 k ax))
! ("rebel" n (r eh1 b ax l))
! ("rebel" v (r ax b eh1 l))
  ("rebelled" nil (r ih0 b eh1 l d))
  ("rebelling" nil (r ih0 b eh1 l ih0 ng))
  ("rebellion" nil (r ih0 b eh1 l y ax n))
***************
*** 76880,76886 ****
  ("rebelliousness" nil (r ax b eh1 l iy0 ax s n ax s))
  ("rebello" nil (r eh0 b eh1 l ow0))
  ("rebelo" nil (r eh0 b eh1 l ow0))
! ("rebels" nil (r eh1 b ax l z))
  ("reber" nil (r eh1 b er0))
  ("rebert" nil (r eh1 b er0 t))
  ("rebholz" nil (r eh1 b hh ow0 l z))
--- 77076,77083 ----
  ("rebelliousness" nil (r ax b eh1 l iy0 ax s n ax s))
  ("rebello" nil (r eh0 b eh1 l ow0))
  ("rebelo" nil (r eh0 b eh1 l ow0))
! ("rebels" n (r eh1 b ax l z))
! ("rebels" v (r ax b eh1 l z))
  ("reber" nil (r eh1 b er0))
  ("rebert" nil (r eh1 b er0 t))
  ("rebholz" nil (r eh1 b hh ow0 l z))
***************
*** 76891,76900 ****
  ("rebmann" nil (r eh1 b m ax n))
  ("rebo" nil (r iy1 b ow0))
  ("reborn" nil (r iy1 b ao1 r n))
! ("rebound" nil (r iy0 b aw1 n d))
  ("rebounded" nil (r ih0 b aw1 n d ih0 d))
  ("rebounding" nil (r ih0 b aw1 n d ih0 ng))
! ("rebounds" nil (r iy1 b aw1 n d z))
  ("rebroadcast" nil (r iy0 b r ao1 d k ae1 s t))
  ("rebstock" nil (r eh1 b s t aa1 k))
  ("rebuck" nil (r eh1 b ax k))
--- 77088,77099 ----
  ("rebmann" nil (r eh1 b m ax n))
  ("rebo" nil (r iy1 b ow0))
  ("reborn" nil (r iy1 b ao1 r n))
! ("rebound" v (r iy0 b aw1 n d))
! ("rebound" n (r iy1 b aw1 n d))
  ("rebounded" nil (r ih0 b aw1 n d ih0 d))
  ("rebounding" nil (r ih0 b aw1 n d ih0 ng))
! ("rebounds" n (r iy1 b aw1 n d z))
! ("rebounds" v (r iy0 b aw1 n d z))
  ("rebroadcast" nil (r iy0 b r ao1 d k ae1 s t))
  ("rebstock" nil (r eh1 b s t aa1 k))
  ("rebuck" nil (r eh1 b ax k))
***************
*** 77104,77110 ****
  ("reconvene" nil (r iy0 k ax n v iy1 n))
  ("reconvened" nil (r iy0 k ax n v iy1 n d))
  ("reconvenes" nil (r iy0 k ax n v iy1 n z))
! ("record" nil (r ax k ao1 r d))
  ("recordable" nil (r ih0 k ao1 r d ax b ax l))
  ("recorded" nil (r ax k ao1 r d ax d))
  ("recorder" nil (r ih0 k ao1 r d er0))
--- 77303,77310 ----
  ("reconvene" nil (r iy0 k ax n v iy1 n))
  ("reconvened" nil (r iy0 k ax n v iy1 n d))
  ("reconvenes" nil (r iy0 k ax n v iy1 n z))
! ("record" v (r ax k ao1 r d))
! ("record" n (r eh1 k er0 d))
  ("recordable" nil (r ih0 k ao1 r d ax b ax l))
  ("recorded" nil (r ax k ao1 r d ax d))
  ("recorder" nil (r ih0 k ao1 r d er0))
***************
*** 77112,77123 ****
  ("recording" nil (r ax k ao1 r d ih0 ng))
  ("recordings" nil (r ih0 k ao1 r d ih0 ng z))
  ("recordkeeping" nil (r eh1 k er0 d k iy1 p ih0 ng))
! ("records" nil (r ax k ao1 r d z))
  ("recore" nil (r eh0 k ao1 r iy0))
! ("recount" nil (r ih0 k aw1 n t))
  ("recounted" nil (r ih0 k aw1 n t ih0 d))
  ("recounting" nil (r ih0 k aw1 n t ih0 ng))
! ("recounts" nil (r iy1 k aw1 n t s))
  ("recoup" nil (r ih0 k uw1 p))
  ("recouped" nil (r ih0 k uw1 p t))
  ("recouping" nil (r ih0 k uw1 p ih0 ng))
--- 77312,77326 ----
  ("recording" nil (r ax k ao1 r d ih0 ng))
  ("recordings" nil (r ih0 k ao1 r d ih0 ng z))
  ("recordkeeping" nil (r eh1 k er0 d k iy1 p ih0 ng))
! ("records" v (r ax k ao1 r d z))
! ("records" n (r eh1 k er0 d z))
  ("recore" nil (r eh0 k ao1 r iy0))
! ("recount" v (r iy0 k aw1 n t))
! ("recount" n (r iy1 k aw0 n t))
  ("recounted" nil (r ih0 k aw1 n t ih0 d))
  ("recounting" nil (r ih0 k aw1 n t ih0 ng))
! ("recounts" n (r iy1 k aw0 n t s))
! ("recounts" v (r iy0 k aw1 n t s))
  ("recoup" nil (r ih0 k uw1 p))
  ("recouped" nil (r ih0 k uw1 p t))
  ("recouping" nil (r ih0 k uw1 p ih0 ng))
***************
*** 77524,77530 ****
  ("refurbishment" nil (r iy0 f er1 b ih0 sh m ax n t))
  ("refusal" nil (r ax f y uw1 z ax l))
  ("refusals" nil (r ih0 f y uw1 z ax l z))
! ("refuse" nil (r ax f y uw1 z))
  ("refused" nil (r ax f y uw1 z d))
  ("refuseniks" nil (r ih0 f y uw1 z n ih0 k s))
  ("refuses" nil (r ax f y uw1 z ax z))
--- 77727,77734 ----
  ("refurbishment" nil (r iy0 f er1 b ih0 sh m ax n t))
  ("refusal" nil (r ax f y uw1 z ax l))
  ("refusals" nil (r ih0 f y uw1 z ax l z))
! ("refuse" v (r ax f y uw1 z))
! ("refuse" n (r eh1 f y uw1 s))
  ("refused" nil (r ax f y uw1 z d))
  ("refuseniks" nil (r ih0 f y uw1 z n ih0 k s))
  ("refuses" nil (r ax f y uw1 z ax z))
***************
*** 77899,77910 ****
  ("reitzel" nil (r ay1 t s ax l))
  ("reitzes" nil (r ay1 t s ih0 z))
  ("reitzfeld" nil (r iy1 t s f eh0 l d))
! ("reject" nil (r ih0 jh eh1 k t))
  ("rejected" nil (r ih0 jh eh1 k t ih0 d))
  ("rejecting" nil (r ih0 jh eh1 k t ih0 ng))
  ("rejection" nil (r ih0 jh eh1 k sh ax n))
  ("rejections" nil (r ih0 jh eh1 k sh ax n z))
! ("rejects" nil (r ih0 jh eh1 k t s))
  ("rejiggering" nil (r iy0 jh ih1 g er0 ih0 ng))
  ("rejoice" nil (r ih0 jh oy1 s))
  ("rejoiced" nil (r ih0 jh oy1 s t))
--- 78103,78116 ----
  ("reitzel" nil (r ay1 t s ax l))
  ("reitzes" nil (r ay1 t s ih0 z))
  ("reitzfeld" nil (r iy1 t s f eh0 l d))
! ("reject" v (r ih0 jh eh1 k t))
! ("reject" n (r iy1 jh eh0 k t))
  ("rejected" nil (r ih0 jh eh1 k t ih0 d))
  ("rejecting" nil (r ih0 jh eh1 k t ih0 ng))
  ("rejection" nil (r ih0 jh eh1 k sh ax n))
  ("rejections" nil (r ih0 jh eh1 k sh ax n z))
! ("rejects" v (r ih0 jh eh1 k t s))
! ("rejects" n (r iy1 jh eh0 k t s))
  ("rejiggering" nil (r iy0 jh ih1 g er0 ih0 ng))
  ("rejoice" nil (r ih0 jh oy1 s))
  ("rejoiced" nil (r ih0 jh oy1 s t))
***************
*** 84273,84282 ****
  ("segers" nil (s iy1 g er0 z))
  ("segerstrom" nil (s eh1 g er0 s t r ax m))
  ("segler" nil (s eh1 g l er0))
! ("segment" nil (s eh1 g m ax n t))
  ("segmentation" nil (s eh1 g m ax n t ey1 sh ax n))
  ("segmented" nil (s eh1 g m eh1 n t ih0 d))
! ("segments" nil (s eh1 g m ax n t s))
  ("segner" nil (s eh1 g n er0))
  ("sego" nil (s iy1 g ow1))
  ("segovia" nil (s eh0 g ow1 v iy0 ax))
--- 84479,84490 ----
  ("segers" nil (s iy1 g er0 z))
  ("segerstrom" nil (s eh1 g er0 s t r ax m))
  ("segler" nil (s eh1 g l er0))
! ("segment" n (s eh1 g m ax n t))
! ("segment" v (s ax g m eh1 n t))
  ("segmentation" nil (s eh1 g m ax n t ey1 sh ax n))
  ("segmented" nil (s eh1 g m eh1 n t ih0 d))
! ("segments" n (s eh1 g m ax n t s))
! ("segments" v (s ax g m eh1 n t s))
  ("segner" nil (s eh1 g n er0))
  ("sego" nil (s iy1 g ow1))
  ("segovia" nil (s eh0 g ow1 v iy0 ax))
***************
*** 84655,84665 ****
  ("seo" nil (s iy1 ow0))
  ("seoul" nil (s ow1 l))
  ("seow" nil (s iy1 ow0))
! ("separate" nil (s eh1 p er0 ey1 t))
  ("separated" nil (s eh1 p er0 ey1 t ax d))
  ("separately" nil (s eh1 p er0 ax t l iy0))
  ("separateness" nil (s eh1 p er0 ax t n ax s))
! ("separates" nil (s eh1 p er0 ey1 t s))
  ("separating" nil (s eh1 p er0 ey1 t ih0 ng))
  ("separation" nil (s eh1 p er0 ey1 sh ax n))
  ("separations" nil (s eh1 p er0 ey1 sh ax n z))
--- 84863,84875 ----
  ("seo" nil (s iy1 ow0))
  ("seoul" nil (s ow1 l))
  ("seow" nil (s iy1 ow0))
! ("separate" v (s eh1 p er0 ey1 t))
! ("separate" n (s eh1 p r ax t))
  ("separated" nil (s eh1 p er0 ey1 t ax d))
  ("separately" nil (s eh1 p er0 ax t l iy0))
  ("separateness" nil (s eh1 p er0 ax t n ax s))
! ("separates" v (s eh1 p er0 ey1 t s))
! ("separates" n (s eh1 p er0 ih0 t s))
  ("separating" nil (s eh1 p er0 ey1 t ih0 ng))
  ("separation" nil (s eh1 p er0 ey1 sh ax n))
  ("separations" nil (s eh1 p er0 ey1 sh ax n z))
***************
*** 91297,91307 ****
  ("subhlok" nil (s ah1 b l aa1 k))
  ("subia" nil (s uw0 b iy1 ax))
  ("subic" nil (s uw1 b ih0 k))
! ("subject" nil (s ax b jh eh1 k t))
  ("subjected" nil (s ax b jh eh1 k t ih0 d))
  ("subjecting" nil (s ax b jh eh1 k t ih0 ng))
  ("subjective" nil (s ax b jh eh1 k t ih0 v))
! ("subjects" nil (s ah1 b jh ih0 k t s))
  ("subkingdom" nil (s ax b k ih1 ng d ax m))
  ("sublease" nil (s ah1 b l iy1 s))
  ("subleasing" nil (s ax b l iy1 s ih0 ng))
--- 91507,91519 ----
  ("subhlok" nil (s ah1 b l aa1 k))
  ("subia" nil (s uw0 b iy1 ax))
  ("subic" nil (s uw1 b ih0 k))
! ("subject" v (s ax b jh eh1 k t))
! ("subject" n (s ah1 b jh ih0 k t))
  ("subjected" nil (s ax b jh eh1 k t ih0 d))
  ("subjecting" nil (s ax b jh eh1 k t ih0 ng))
  ("subjective" nil (s ax b jh eh1 k t ih0 v))
! ("subjects" v (s ax b jh eh1 k t s))
! ("subjects" n (s ah1 b jh ih0 k t s))
  ("subkingdom" nil (s ax b k ih1 ng d ax m))
  ("sublease" nil (s ah1 b l iy1 s))
  ("subleasing" nil (s ax b l iy1 s ih0 ng))
***************
*** 92046,92057 ****
  ("surtax" nil (s er1 t ae1 k s))
  ("surtaxes" nil (s er1 t ae1 k s ih0 z))
  ("surveillance" nil (s er0 v ey1 l ax n s))
! ("survey" nil (s er0 v ey1))
  ("surveyed" nil (s er0 v ey1 d))
  ("surveying" nil (s er0 v ey1 ih0 ng))
  ("surveyor" nil (s er0 v ey1 er0))
  ("surveyors" nil (s er0 v ey1 er0 z))
! ("surveys" nil (s er0 v ey1 z))
  ("survivability" nil (s er0 v ay1 v ax b ih1 l ih0 t iy0))
  ("survivable" nil (s er0 v ay1 v ax b ax l))
  ("survival" nil (s er0 v ay1 v ax l))
--- 92258,92271 ----
  ("surtax" nil (s er1 t ae1 k s))
  ("surtaxes" nil (s er1 t ae1 k s ih0 z))
  ("surveillance" nil (s er0 v ey1 l ax n s))
! ("survey" v (s er0 v ey1))
! ("survey" n (s er1 v ey1))
  ("surveyed" nil (s er0 v ey1 d))
  ("surveying" nil (s er0 v ey1 ih0 ng))
  ("surveyor" nil (s er0 v ey1 er0))
  ("surveyors" nil (s er0 v ey1 er0 z))
! ("surveys" v (s er0 v ey1 z))
! ("surveys" n (s er1 v ey1 z))
  ("survivability" nil (s er0 v ay1 v ax b ih1 l ih0 t iy0))
  ("survivable" nil (s er0 v ay1 v ax b ax l))
  ("survival" nil (s er0 v ay1 v ax l))
***************
*** 92082,92091 ****
  ("susko" nil (s ah1 s k ow0))
  ("susman" nil (s ah1 s m ax n))
  ("susong" nil (s ah1 s ao0 ng))
! ("suspect" nil (s ax s p eh1 k t))
  ("suspected" nil (s ax s p eh1 k t ax d))
  ("suspecting" nil (s ax s p eh1 k t ih0 ng))
! ("suspects" nil (s ax s p eh1 k t s))
  ("suspend" nil (s ax s p eh1 n d))
  ("suspended" nil (s ax s p eh1 n d ax d))
  ("suspenders" nil (s ax s p eh1 n d er0 z))
--- 92296,92307 ----
  ("susko" nil (s ah1 s k ow0))
  ("susman" nil (s ah1 s m ax n))
  ("susong" nil (s ah1 s ao0 ng))
! ("suspect" v (s ax s p eh1 k t))
! ("suspect" n (s ah1 s p eh1 k t))
  ("suspected" nil (s ax s p eh1 k t ax d))
  ("suspecting" nil (s ax s p eh1 k t ih0 ng))
! ("suspects" v (s ax s p eh1 k t s))
! ("suspects" n (s ah1 s p eh1 k t s))
  ("suspend" nil (s ax s p eh1 n d))
  ("suspended" nil (s ax s p eh1 n d ax d))
  ("suspenders" nil (s ax s p eh1 n d er0 z))
***************
*** 93412,93424 ****
  ("teaneck" nil (t iy1 n eh1 k))
  ("teaney" nil (t iy1 n iy0))
  ("teapot" nil (t iy1 p aa1 t))
! ("tear" nil (t eh1 r))
  ("teare" nil (t iy1 r))
  ("tearful" nil (t ih1 r f ax l))
  ("tearfully" nil (t ih1 r f ax l iy0))
  ("tearing" nil (t eh1 r ih0 ng))
  ("tearle" nil (t ao1 r ax l))
! ("tears" nil (t eh1 r z))
  ("teary" nil (t ih1 r iy0))
  ("teas" nil (t iy1 z))
  ("teasdale" nil (t iy1 z d ey1 l))
--- 93628,93642 ----
  ("teaneck" nil (t iy1 n eh1 k))
  ("teaney" nil (t iy1 n iy0))
  ("teapot" nil (t iy1 p aa1 t))
! ("tear" v (t eh1 r))
! ("tear" n (t iy1 r))
  ("teare" nil (t iy1 r))
  ("tearful" nil (t ih1 r f ax l))
  ("tearfully" nil (t ih1 r f ax l iy0))
  ("tearing" nil (t eh1 r ih0 ng))
  ("tearle" nil (t ao1 r ax l))
! ("tears" v (t eh1 r z))
! ("tears" n (t ih1 r z))
  ("teary" nil (t ih1 r iy0))
  ("teas" nil (t iy1 z))
  ("teasdale" nil (t iy1 z d ey1 l))
***************
*** 95441,95451 ****
  ("torley" nil (t ao1 r l iy0))
  ("torma" nil (t ao1 r m ax))
  ("torme" nil (t ao1 r m))
! ("torment" nil (t ao1 r m eh1 n t))
  ("tormenta" nil (t ao1 r m eh1 n t ax))
  ("tormented" nil (t ao1 r m eh1 n t ih0 d))
  ("tormentors" nil (t ao1 r m eh1 n t er0 z))
! ("torments" nil (t ao1 r m eh1 n t s))
  ("tormey" nil (t ao1 r m iy0))
  ("torn" nil (t ao1 r n))
  ("tornabene" nil (t ao0 r n aa0 b eh1 n ax))
--- 95659,95671 ----
  ("torley" nil (t ao1 r l iy0))
  ("torma" nil (t ao1 r m ax))
  ("torme" nil (t ao1 r m))
! ("torment" n (t ao1 r m eh1 n t))
! ("torment" v (t ao0 r m eh1 n t))
  ("tormenta" nil (t ao1 r m eh1 n t ax))
  ("tormented" nil (t ao1 r m eh1 n t ih0 d))
  ("tormentors" nil (t ao1 r m eh1 n t er0 z))
! ("torments" n (t ao1 r m eh1 n t s))
! ("torments" v (t ao0 r m eh1 n t s))
  ("tormey" nil (t ao1 r m iy0))
  ("torn" nil (t ao1 r n))
  ("tornabene" nil (t ao0 r n aa0 b eh1 n ax))
***************
*** 95895,95901 ****
  ("transcripts" nil (t r ae1 n s k r ih1 p t s))
  ("transducer" nil (t r ae0 n s d uw1 s er0))
  ("transducers" nil (t r ae0 n s d uw1 s er0 z))
! ("transfer" nil (t r ae0 n s f er1))
  ("transferability" nil (t r ae1 n s f er0 ax b ih1 l ih0 t iy0))
  ("transferable" nil (t r ae0 n s f er1 ax b ax l))
  ("transfered" nil (t r ae0 n s f er1 d))
--- 96115,96122 ----
  ("transcripts" nil (t r ae1 n s k r ih1 p t s))
  ("transducer" nil (t r ae0 n s d uw1 s er0))
  ("transducers" nil (t r ae0 n s d uw1 s er0 z))
! ("transfer" v (t r ae0 n s f er1))
! ("transfer" n (t r ae1 n s f er0))
  ("transferability" nil (t r ae1 n s f er0 ax b ih1 l ih0 t iy0))
  ("transferable" nil (t r ae0 n s f er1 ax b ax l))
  ("transfered" nil (t r ae0 n s f er1 d))
***************
*** 95903,95909 ****
  ("transferrable" nil (t r ae1 n s f er1 ax b ax l))
  ("transferred" nil (t r ae0 n s f er1 d))
  ("transferring" nil (t r ae0 n s f er1 ih0 ng))
! ("transfers" nil (t r ae0 n s f er1 z))
  ("transfixed" nil (t r ae0 n s f ih1 k s t))
  ("transform" nil (t r ae0 n s f ao1 r m))
  ("transformation" nil (t r ae1 n s f er0 m ey1 sh ax n))
--- 96124,96131 ----
  ("transferrable" nil (t r ae1 n s f er1 ax b ax l))
  ("transferred" nil (t r ae0 n s f er1 d))
  ("transferring" nil (t r ae0 n s f er1 ih0 ng))
! ("transfers" v (t r ae0 n s f er1 z))
! ("transfers" n (t r ae1 n s f er0 z))
  ("transfixed" nil (t r ae0 n s f ih1 k s t))
  ("transform" nil (t r ae0 n s f ao1 r m))
  ("transformation" nil (t r ae1 n s f er0 m ey1 sh ax n))
***************
*** 95977,95990 ****
  ("transponder" nil (t r ae0 n s p aa1 n d er0))
  ("transponders" nil (t r ae0 n s p aa1 n d er0 z))
  ("transporation" nil (t r ae1 n z p er0 ey1 sh ax n))
! ("transport" nil (t r ae0 n s p ao1 r t))
  ("transportable" nil (t r ae0 n s p ao1 r t ax b ax l))
  ("transportation" nil (t r ae1 n s p er0 t ey1 sh ax n))
  ("transported" nil (t r ae0 n s p ao1 r t ax d))
  ("transporter" nil (t r ae0 n s p ao1 r t er0))
  ("transporters" nil (t r ae0 n s p ao1 r t er0 z))
  ("transporting" nil (t r ae0 n s p ao1 r t ih0 ng))
! ("transports" nil (t r ae0 n s p ao1 r t s))
  ("transpose" nil (t r ae0 n s p ow1 z))
  ("transposed" nil (t r ae0 n s p ow1 z d))
  ("transracial" nil (t r ae1 n z r ey1 sh ax l))
--- 96199,96214 ----
  ("transponder" nil (t r ae0 n s p aa1 n d er0))
  ("transponders" nil (t r ae0 n s p aa1 n d er0 z))
  ("transporation" nil (t r ae1 n z p er0 ey1 sh ax n))
! ("transport" v (t r ae0 n s p ao1 r t))
! ("transport" n (t r ae1 n s p ao0 r t))
  ("transportable" nil (t r ae0 n s p ao1 r t ax b ax l))
  ("transportation" nil (t r ae1 n s p er0 t ey1 sh ax n))
  ("transported" nil (t r ae0 n s p ao1 r t ax d))
  ("transporter" nil (t r ae0 n s p ao1 r t er0))
  ("transporters" nil (t r ae0 n s p ao1 r t er0 z))
  ("transporting" nil (t r ae0 n s p ao1 r t ih0 ng))
! ("transports" v (t r ae0 n s p ao1 r t s))
! ("transports" n (t r ae1 n s p ao0 r t s))
  ("transpose" nil (t r ae0 n s p ow1 z))
  ("transposed" nil (t r ae0 n s p ow1 z d))
  ("transracial" nil (t r ae1 n z r ey1 sh ax l))
***************
*** 98373,98380 ****
  ("uprooting" nil (ax p r uw1 t ih0 ng))
  ("ups" nil (ah1 p s))
  ("upscale" nil (ah1 p s k ey1 l))
! ("upset" nil (ax p s eh1 t))
! ("upsets" nil (ax p s eh1 t s))
  ("upsetting" nil (ax p s eh1 t ih0 ng))
  ("upshaw" nil (ah1 p sh ao1))
  ("upshot" nil (ah1 p sh aa1 t))
--- 98597,98606 ----
  ("uprooting" nil (ax p r uw1 t ih0 ng))
  ("ups" nil (ah1 p s))
  ("upscale" nil (ah1 p s k ey1 l))
! ("upset" v (ax p s eh1 t))
! ("upset" n (ah1 p s eh1 t))
! ("upsets" v (ax p s eh1 t s))
! ("upsets" n (ah1 p s eh1 t s))
  ("upsetting" nil (ax p s eh1 t ih0 ng))
  ("upshaw" nil (ah1 p sh ao1))
  ("upshot" nil (ah1 p sh aa1 t))
***************
*** 98512,98518 ****
  ("usair" nil (y uw1 eh1 s eh1 r))
  ("usameribancs" nil (y uw1 eh1 s ax m eh1 r ih0 b ae1 n k s))
  ("usbancorp" nil (y uw1 eh1 s b ae1 ng k ao1 r p))
! ("use" nil (y uw1 s))
  ("usec" nil (y uw1 s eh0 k))
  ("used" nil (y uw1 z d))
  ("useful" nil (y uw1 s f ax l))
--- 98738,98745 ----
  ("usair" nil (y uw1 eh1 s eh1 r))
  ("usameribancs" nil (y uw1 eh1 s ax m eh1 r ih0 b ae1 n k s))
  ("usbancorp" nil (y uw1 eh1 s b ae1 ng k ao1 r p))
! ("use" n (y uw0 s))
! ("use" v (y uw1 z))
  ("usec" nil (y uw1 s eh0 k))
  ("used" nil (y uw1 z d))
  ("useful" nil (y uw1 s f ax l))
***************
*** 98525,98531 ****
  ("user" nil (y uw1 z er0))
  ("users" nil (y uw1 z er0 z))
  ("usery" nil (y uw1 z er0 iy0))
! ("uses" nil (y uw1 s ax z))
  ("usher" nil (ah1 sh er0))
  ("ushered" nil (ah1 sh er0 d))
  ("ushering" nil (ah1 sh er0 ih0 ng))
--- 98752,98759 ----
  ("user" nil (y uw1 z er0))
  ("users" nil (y uw1 z er0 z))
  ("usery" nil (y uw1 z er0 iy0))
! ("uses" n (y uw1 s ax z))
! ("uses" v (y uw1 z ih0 z))
  ("usher" nil (ah1 sh er0))
  ("ushered" nil (ah1 sh er0 d))
  ("ushering" nil (ah1 sh er0 ih0 ng))
***************
*** 102817,102823 ****
  ("winchester" nil (w ih1 n ch eh1 s t er0))
  ("wincing" nil (w ih1 n s ih0 ng))
  ("winckler" nil (w ih1 ng k l er0))
! ("wind" nil (w ay1 n d))
  ("windchill" nil (w ih1 n d ch ih1 l))
  ("windchime" nil (w ih1 n d ch ay1 m))
  ("windchimes" nil (w ih1 n d ch ay1 m z))
--- 103045,103052 ----
  ("winchester" nil (w ih1 n ch eh1 s t er0))
  ("wincing" nil (w ih1 n s ih0 ng))
  ("winckler" nil (w ih1 ng k l er0))
! ("wind" v (w ay1 n d))
! ("wind" n (w ih1 n d))
  ("windchill" nil (w ih1 n d ch ih1 l))
  ("windchime" nil (w ih1 n d ch ay1 m))
  ("windchimes" nil (w ih1 n d ch ay1 m z))
