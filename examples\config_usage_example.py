"""
配置使用示例
展示如何在各个模块中使用配置管理器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.config_manager import get_config_manager, get_config, set_config


def example_audio_config():
    """音频配置使用示例"""
    print("=== 音频配置使用示例 ===")
    
    # 获取音频输入配置
    sample_rate = get_config('audio.input.sample_rate', 16000)
    channels = get_config('audio.input.channels', 1)
    chunk_size = get_config('audio.input.chunk_size', 1024)
    
    print(f"音频输入配置:")
    print(f"  采样率: {sample_rate}Hz")
    print(f"  声道数: {channels}")
    print(f"  块大小: {chunk_size}")
    
    # 获取VAD配置
    vad_threshold = get_config('audio.vad.threshold', 0.5)
    frame_duration = get_config('audio.vad.frame_duration_ms', 30)
    
    print(f"VAD配置:")
    print(f"  阈值: {vad_threshold}")
    print(f"  帧长度: {frame_duration}ms")


def example_model_config():
    """模型配置使用示例"""
    print("\n=== 模型配置使用示例 ===")
    
    # 获取模型路径
    asr_path = get_config('models.asr_model_path', './models/asr')
    kws_path = get_config('models.kws_model_path', './models/kws')
    tts_path = get_config('models.tts_model_path', './models/tts')
    
    print(f"模型路径:")
    print(f"  ASR: {asr_path}")
    print(f"  KWS: {kws_path}")
    print(f"  TTS: {tts_path}")
    
    # 获取模型参数
    beam_size = get_config('models.asr.beam_size', 5)
    feature_dim = get_config('models.kws.feature_dim', 40)
    tts_speed = get_config('models.tts.speed', 1.0)
    
    print(f"模型参数:")
    print(f"  ASR束搜索大小: {beam_size}")
    print(f"  KWS特征维度: {feature_dim}")
    print(f"  TTS语速: {tts_speed}")


def example_hardware_config():
    """硬件配置使用示例"""
    print("\n=== 硬件配置使用示例 ===")
    
    # 获取设备配置
    device = get_config('hardware.device', 'auto')
    gpu_memory = get_config('hardware.gpu_memory_fraction', 0.8)
    num_threads = get_config('hardware.num_threads', 4)
    
    print(f"硬件配置:")
    print(f"  推理设备: {device}")
    print(f"  GPU内存比例: {gpu_memory}")
    print(f"  CPU线程数: {num_threads}")
    
    # 获取性能优化配置
    enable_fp16 = get_config('hardware.enable_fp16', False)
    enable_cache = get_config('hardware.enable_cache', True)
    
    print(f"性能优化:")
    print(f"  FP16推理: {enable_fp16}")
    print(f"  模型缓存: {enable_cache}")


def example_llm_config():
    """LLM配置使用示例"""
    print("\n=== LLM配置使用示例 ===")
    
    # 获取Dify API配置
    api_url = get_config('llm.dify_api_url', '')
    api_key = get_config('llm.dify_api_key', '')
    
    print(f"Dify API配置:")
    print(f"  API地址: {api_url}")
    print(f"  API密钥: {'*' * len(api_key) if api_key else '未配置'}")
    
    # 获取LLM参数
    max_tokens = get_config('llm.max_tokens', 2048)
    temperature = get_config('llm.temperature', 0.7)
    system_prompt = get_config('llm.system_prompt', '')
    
    print(f"LLM参数:")
    print(f"  最大token数: {max_tokens}")
    print(f"  采样温度: {temperature}")
    print(f"  系统提示: {system_prompt[:50]}...")


def example_interrupt_config():
    """插话中断配置使用示例"""
    print("\n=== 插话中断配置使用示例 ===")
    
    # 获取插话配置
    enabled = get_config('interrupt.enabled', True)
    min_duration = get_config('interrupt.min_interrupt_duration_ms', 500)
    threshold = get_config('interrupt.interrupt_threshold', 0.6)
    
    print(f"插话配置:")
    print(f"  启用插话: {enabled}")
    print(f"  最小持续时间: {min_duration}ms")
    print(f"  检测阈值: {threshold}")
    
    # 获取前置条件
    require_wake = get_config('interrupt.require_wake_state', True)
    require_processing = get_config('interrupt.require_processing', True)
    
    print(f"前置条件:")
    print(f"  要求已唤醒: {require_wake}")
    print(f"  要求正在处理: {require_processing}")


def example_logging_config():
    """日志配置使用示例"""
    print("\n=== 日志配置使用示例 ===")
    
    # 获取日志配置
    level = get_config('logging.level', 'info')
    file_enabled = get_config('logging.file_enabled', True)
    log_file = get_config('logging.log_file', './logs/aibi.log')
    
    print(f"日志配置:")
    print(f"  日志级别: {level}")
    print(f"  文件日志: {file_enabled}")
    print(f"  日志文件: {log_file}")
    
    # 获取性能日志配置
    perf_logging = get_config('logging.performance_logging', True)
    log_latency = get_config('logging.log_latency', True)
    
    print(f"性能日志:")
    print(f"  性能监控: {perf_logging}")
    print(f"  延迟记录: {log_latency}")


def example_monitoring_config():
    """监控配置使用示例"""
    print("\n=== 监控配置使用示例 ===")
    
    # 获取健康检查配置
    health_interval = get_config('monitoring.health_check_interval', 30)
    auto_restart = get_config('monitoring.enable_auto_restart', True)
    
    print(f"健康检查:")
    print(f"  检查间隔: {health_interval}秒")
    print(f"  自动重启: {auto_restart}")
    
    # 获取性能监控配置
    perf_monitor = get_config('monitoring.enable_performance_monitor', True)
    monitor_interval = get_config('monitoring.monitor_interval', 10)
    
    print(f"性能监控:")
    print(f"  启用监控: {perf_monitor}")
    print(f"  监控间隔: {monitor_interval}秒")


def example_debug_config():
    """调试配置使用示例"""
    print("\n=== 调试配置使用示例 ===")
    
    # 获取调试配置
    debug_enabled = get_config('debug.enabled', False)
    save_audio = get_config('debug.save_audio', False)
    verbose_logging = get_config('debug.verbose_logging', False)
    
    print(f"调试配置:")
    print(f"  调试模式: {debug_enabled}")
    print(f"  保存音频: {save_audio}")
    print(f"  详细日志: {verbose_logging}")


def example_config_modification():
    """配置修改示例"""
    print("\n=== 配置修改示例 ===")
    
    # 修改配置
    print("修改前的唤醒词:", get_config('wake_word'))
    
    # 设置新的唤醒词
    set_config('wake_word', 'Hey, 小艾')
    print("修改后的唤醒词:", get_config('wake_word'))
    
    # 修改音频配置
    set_config('audio.input.sample_rate', 22050)
    print("修改后的采样率:", get_config('audio.input.sample_rate'))
    
    # 修改设备配置
    set_config('hardware.device', 'gpu')
    print("修改后的设备:", get_config('hardware.device'))


def main():
    """主函数"""
    print("aibi语音交互系统 - 配置使用示例")
    print("=" * 50)
    
    # 获取配置管理器
    config_mgr = get_config_manager()
    
    # 检查配置是否加载成功
    if not config_mgr.load_config():
        print("错误: 配置文件加载失败")
        return
    
    # 运行各种配置示例
    example_audio_config()
    example_model_config()
    example_hardware_config()
    example_llm_config()
    example_interrupt_config()
    example_logging_config()
    example_monitoring_config()
    example_debug_config()
    example_config_modification()
    
    print("\n" + "=" * 50)
    print("配置使用示例完成")


if __name__ == "__main__":
    main() 