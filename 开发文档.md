# aibi语音交互系统开发文档

---

## 开发进度

- 已完成：环境与依赖配置、项目目录结构、核心功能模块（音频采集、VAD、KWS、ASR、TTS、LLM对接、音频播放、推理后端适配）、交互与控制机制（插话/中断、自动退出唤醒、事件驱动）。
- 待完善：单元/集成/性能测试、变更与问题反馈记录、详细接口注释与文档、Dify LLM API配置、硬件适配实测。

---

## 一、项目简介

aibi语音交互系统是一套端到端本地部署的智能语音交互解决方案，适用于PC和嵌入式平台。系统以FunASR为主线，集成唤醒词检测、端点检测、降噪、语音识别、TTS合成、插话中断、自动休眠等功能，支持多硬件（CPU/NPU/GPU）自动适配。LLM能力通过Dify服务器对接，系统专为低延迟、高鲁棒性、易扩展设计，适合智能助手、机器人等实时语音场景。

---

## 二、系统架构与模块说明

详见《架构设计说明.md》，核心流程如下：
- **待唤醒状态**：低功耗监听，仅唤醒词检测
- **已唤醒状态**：激活ASR/LLM/TTS等，支持插话中断
- **自动退出唤醒**：超时或检测到结束指令后回到待唤醒
- **插话/中断**：仅在已唤醒状态下生效，满足前置条件时可中断当前任务

### 模块分工与文件路径

| 模块名称         | 文件路径                        | 状态 | 主要接口/类           |
|------------------|--------------------------------|------|----------------------|
| 音频采集         | modules/audio_input.py          | ✅ | AudioInput           |
| VAD端点检测      | modules/vad.py                  | ✅ | VADProcessor（支持FSMN-VAD） |
| 降噪处理         | modules/denoise.py              | ✅ | DenoiseProcessor（FunASR降噪） |
| 唤醒词检测       | modules/kws.py                  | ✅ | WakeWordDetector     |
| 语音识别（ASR）  | modules/asr.py                  | ✅ | ASRProcessor（FunASR+SenseVoiceSmall） |
| LLM对接          | modules/llm_client.py           | ✅ | LLMClient（Dify API同步/流式、重试、连接池、缓存、按句断流） |
| 语音合成（TTS）  | modules/tts.py                  | ⚠️ | TTSProcessor（需完善cosyvoice） |
| 音频播放         | modules/audio_output.py          | ✅ | AudioOutput          |
| 配置管理         | modules/config_manager.py        | ✅ | ConfigManager        |
| 状态管理         | modules/state_manager.py         | ⚠️ | StateManager（需完善状态机） |
| 事件驱动         | modules/event_bus.py             | ⚠️ | EventBus（需增强功能） |
| 插话/中断        | modules/interrupt.py             | ⚠️ | InterruptManager（需完善逻辑） |
| 自动退出唤醒     | modules/auto_exit.py             | ✅ | AutoExitManager      |
| 硬件适配         | modules/hardware_adapter.py      | ✅ | HardwareAdapter      |
| 健康检查         | modules/health_check.py          | ✅ | HealthChecker        |
| 日志监控         | modules/logging_utils.py         | ✅ | setup_logger         |

---

## 三、主要流程说明

1. **待唤醒**：系统仅监听唤醒词，资源消耗极低
2. **唤醒成功**：切换为已唤醒，激活ASR/LLM/TTS
3. **交互中**：支持插话中断，实时响应用户新输入，ASR识别结果通过事件驱动自动调用llm_client.query_llm，获得LLM回复后TTS合成播报。支持同步/流式/断句输出，便于后续多轮对话和流式TTS体验。
4. **自动退出唤醒**：超时或检测到“结束”指令，回到待唤醒

---

## 四、参数与配置说明

- **唤醒词**：支持自定义，默认“Hey,艾比”
- **超时时间**：如10秒无输入自动退出唤醒
- **硬件后端**：支持自动/手动选择CPU、GPU、NPU
- **多语言**：配置支持多语种识别与合成
- **日志级别**：info/warning/error可选
- **Dify API**：需配置服务器地址、鉴权信息

所有参数集中在config.yaml（或config.json）中管理，支持热加载。

---

## 五、测试与性能评估（待完善）

- 单元测试：tests/ 目录下补充 test_*.py
- 集成测试：tests/integration_test.py
- 性能测试：tools/benchmark.py
- 多硬件平台兼容性测试：实际部署验证

---

## 六、变更与问题反馈记录（待完善）

- 修改问题反馈记录.md
- 待修复问题汇总.md

---

## 七、维护与扩展建议

- LLM对接模块已支持Dify API同步/流式、重试、连接池、缓存、日志分级、按句断流等能力，便于后续扩展多轮对话、流式TTS、界面分句显示等高级功能。
- 所有模块均有标准接口，便于升级/替换
- 支持多线程/异步处理，提升并发与吞吐
- 预留接口，便于后续扩展（如多麦克风阵列、声源定位等）
- 定期进行压力测试和健康检查，保证系统长期稳定运行

---

## 插件化TTS（CosyVoice2/Matcha-TTS）集成说明

- 支持通过插件机制集成CosyVoice2/Matcha-TTS。
- 请将Matcha-TTS源码放入 plugins/matcha_tts 目录，系统自动通过插件加载。
- 插件目录结构示例：
  ```
  plugins/
    matcha_tts/
      matcha/
        models/
          matcha_tts.py
        ...
      ...
  ```
- 依赖安装：根据硬件环境选择 requirements-cpu.txt、requirements-gpu.txt、requirements-npu.txt 安装依赖。
- 插件加载后，TTS模块会自动import插件内的MatchaTTS类进行本地推理。

---

## 九、目录结构建议

```