(require 'lts_build)
(set! allowables 
      '((a _epsilon_ aa aa1 aa0
           ax ax1 ax0
	   eh eh1 eh0
	   ah  ah1 ah0
	   ae  ae1 ae0
	   ey  ey1 ey0
	   ay  ay1 ay0
	   er  er1 er0
	   y-ax0 y-ah1 y-ah0
	   aw  aw1 aw0
	   ao  ao1 ao0
	   ih  ih1 ih0
	   w-ax0 w-ah1 w-ah0
	   ow  ow1 ow0
	   w-ey  w-ey1 ey0
	   iy  iy1 iy0)
        (b _epsilon_ b p )
        (c _epsilon_ k ch s sh t-s )
        (d _epsilon_ d t jh)
        (e _epsilon_ ih  ih1 ih0
           ax ax1 ax0
	   iy  iy1 iy0
	   er  er1 er0
	   ax ah1 ah0
	   eh  eh1 eh0
	   ey  ey1 ey0
	   uw  uw1 uw0
	   ay  ay1 ay0
	   ow  ow1 ow0
	   y-uw  y-uw1 y-uw0
	   oy  oy1 oy0
	   aa  aa1 aa0)
        (f _epsilon_ f )
        (g _epsilon_ g jh zh k f)
        (h _epsilon_ hh )
        (i _epsilon_ iy  iy1 iy0
           ax ax1 ax0
	   ih  ih1 ih0
	   ah  ah1 ah0
	   ax ah1 ah0
	   ay  ay1 ay0
	   y 
	   aa  aa1 aa0
	   ae  ae1 ae0
	   w-ax0 w-ah1 w-ah0
	   eh  eh1 eh0 
           er er0 er1 )
        (j _epsilon_ jh y hh zh)
        (k _epsilon_ k )
        (l _epsilon_ l ax-l y ax0-l)
        (m _epsilon_ m ax-m m-ax0 ax0-m m-ax0
	   m-ae  m-ae1 m-ae0 
	   m-ih m-ih0 )
        (n _epsilon_ n ng n-y)
        (o _epsilon_ ax ax0 ah1 ah0
	   ao  ao1 ao0
	   ow  ow1 ow0
	   uw  uw1 uw0
	   er  er1 er0
	   aa  aa1 aa0
	   aw  aw1 aw0
	   oy  oy1 oy0
	   uh  uh1 uh0
	   w 
	   w-ax0 w-ah1 w-ah0
	   aa  aa1 aa0
	   ih  ih1 ih0
	   ae  ae1 ae0)
        (p _epsilon_ p f)
        (q _epsilon_ k )
        (r _epsilon_ r er1 er er0 )
        (s _epsilon_ s sh z zh ch)
        (t _epsilon_ t th sh ch dh d s zh)
        (u _epsilon_ 
	   ax ax0
	   ah  ah1 ah0
	   uw  uw1 uw0
	   er  er1 er0
	   uh  uh1 uh0
	   y-uw  y-uw1 y-uw0
	   ax-w ah1-w ah0-w
	   y-er  y-er1 y-er0
	   y-ax y-ax0 y-ah1 y-ah0
	   w 
	   ih  ih1 ih0
	   ao  ao1 ao0
	   eh  eh1 eh0
	   y-uh  y-uh1 y-uh0 )
        (v _epsilon_ v f)
        (w _epsilon_ w v f)
        (x _epsilon_ k-s g-z ng-z k-sh z g-zh zh)
        (y _epsilon_ 
	   iy  iy1 iy0
	   ih  ih1 ih0
	   ay  ay1 ay0
	   y 
	   ax ax0 ah1 ah0)
        (z _epsilon_ z t-s zh s)
        (# #)))

