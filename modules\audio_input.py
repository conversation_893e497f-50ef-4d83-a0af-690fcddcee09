import sounddevice as sd
import numpy as np

class AudioInput:
    def __init__(self, samplerate=16000, channels=1, blocksize=1024, callback=None):
        self.samplerate = samplerate
        self.channels = channels
        self.blocksize = blocksize
        self.callback = callback
        self.stream = None

    def start(self):
        self.stream = sd.InputStream(
            samplerate=self.samplerate,
            channels=self.channels,
            blocksize=self.blocksize,
            dtype='int16',
            callback=self.callback
        )
        self.stream.start()
        print("音频采集已启动，按Ctrl+C停止")
        try:
            while True:
                sd.sleep(1000)
        except KeyboardInterrupt:
            self.stream.stop()
            print("音频采集已停止")

if __name__ == "__main__":
    def print_callback(indata, frames, time, status):
        print(f"采集到音频帧 shape={indata.shape}")
    ai = AudioInput(callback=print_callback)
    ai.start()
