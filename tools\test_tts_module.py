#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
aibi语音交互系统 - TTS模块测试脚本
"""

import os
import sys
import time
import numpy as np
import soundfile as sf
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from modules.config_manager import ConfigManager
from modules.tts import TTSProcessor

def test_tts_initialization():
    """测试TTS模块初始化"""
    print("🔧 测试TTS模块初始化...")
    
    try:
        # 初始化配置
        config = ConfigManager()
        
        # 创建TTS处理器
        tts = TTSProcessor(config)
        
        # 检查初始化状态
        print(f"  初始化状态: {'成功' if tts.is_initialized else '失败'}")
        print(f"  模型类型: {tts.model_type}")
        print(f"  模型路径: {tts.model_path}")
        print(f"  采样率: {tts.sample_rate}")
        print(f"  健康状态: {'正常' if tts.is_healthy() else '异常'}")
        
        return tts
        
    except Exception as e:
        print(f"❌ TTS初始化失败: {e}")
        return None

def test_tts_synthesis(tts: TTSProcessor):
    """测试TTS合成功能"""
    print("\n🎵 测试TTS合成功能...")
    
    test_texts = [
        "你好，我是aibi语音助手。",
        "今天天气真不错。",
        "语音合成测试成功！",
        "这是一个较长的测试文本，用来验证TTS模块的合成能力和性能表现。"
    ]
    
    results = []
    
    for i, text in enumerate(test_texts):
        print(f"\n  测试 {i+1}: {text}")
        
        start_time = time.time()
        audio_data = tts.synthesize(text)
        synthesis_time = time.time() - start_time
        
        if audio_data is not None:
            duration = len(audio_data) / tts.sample_rate
            rtf = synthesis_time / duration if duration > 0 else 0
            
            print(f"    ✅ 合成成功")
            print(f"    📊 音频长度: {len(audio_data)} 采样点")
            print(f"    ⏱️ 音频时长: {duration:.2f}s")
            print(f"    🚀 合成耗时: {synthesis_time:.3f}s")
            print(f"    📈 RTF: {rtf:.3f}")
            
            # 保存音频文件
            output_file = f"test_tts_{i+1}.wav"
            sf.write(output_file, audio_data, tts.sample_rate)
            print(f"    💾 已保存: {output_file}")
            
            results.append({
                "text": text,
                "success": True,
                "duration": duration,
                "synthesis_time": synthesis_time,
                "rtf": rtf
            })
        else:
            print(f"    ❌ 合成失败")
            results.append({
                "text": text,
                "success": False,
                "duration": 0,
                "synthesis_time": synthesis_time,
                "rtf": 0
            })
    
    return results

def test_tts_performance(tts: TTSProcessor):
    """测试TTS性能"""
    print("\n📊 测试TTS性能...")
    
    # 重置统计
    tts.reset_stats()
    
    # 批量测试
    test_texts = [
        "性能测试文本一",
        "性能测试文本二",
        "性能测试文本三",
        "性能测试文本四",
        "性能测试文本五"
    ]
    
    print(f"  批量合成 {len(test_texts)} 个文本...")
    
    start_time = time.time()
    success_count = 0
    
    for text in test_texts:
        audio_data = tts.synthesize(text)
        if audio_data is not None:
            success_count += 1
    
    total_time = time.time() - start_time
    
    # 获取性能统计
    model_info = tts.get_model_info()
    
    print(f"  📈 性能统计:")
    print(f"    成功率: {success_count}/{len(test_texts)} ({success_count/len(test_texts)*100:.1f}%)")
    print(f"    总耗时: {total_time:.3f}s")
    print(f"    平均耗时: {model_info['avg_synthesis_time']:.3f}s")
    print(f"    合成次数: {model_info['synthesis_count']}")
    print(f"    总合成时间: {model_info['total_synthesis_time']:.3f}s")

def test_tts_parameters(tts: TTSProcessor):
    """测试TTS参数设置"""
    print("\n⚙️ 测试TTS参数设置...")
    
    # 测试语速设置
    print("  测试语速设置...")
    tts.set_speed(1.5)
    tts.set_speed(0.8)
    tts.set_speed(2.5)  # 超出范围，应该被限制
    
    # 测试音量设置
    print("  测试音量设置...")
    tts.set_volume(1.2)
    tts.set_volume(0.5)
    tts.set_volume(3.0)  # 超出范围，应该被限制
    
    # 测试音调设置
    print("  测试音调设置...")
    tts.set_pitch(1.1)
    tts.set_pitch(0.9)
    tts.set_pitch(0.3)  # 超出范围，应该被限制
    
    # 显示当前设置
    model_info = tts.get_model_info()
    print(f"  当前设置:")
    print(f"    语速: {model_info['speed']}")
    print(f"    音量: {model_info['volume']}")
    print(f"    音调: {model_info['pitch']}")

def test_tts_error_handling(tts: TTSProcessor):
    """测试TTS错误处理"""
    print("\n🛡️ 测试TTS错误处理...")
    
    # 测试空文本
    print("  测试空文本...")
    result = tts.synthesize("")
    print(f"    空文本结果: {'None' if result is None else '有数据'}")
    
    # 测试None文本
    print("  测试None文本...")
    result = tts.synthesize(None)
    print(f"    None文本结果: {'None' if result is None else '有数据'}")
    
    # 测试超长文本
    print("  测试超长文本...")
    long_text = "这是一个非常长的测试文本。" * 100
    result = tts.synthesize(long_text)
    print(f"    超长文本结果: {'成功' if result is not None else '失败'}")

def main():
    """主函数"""
    print("🚀 aibi语音交互系统 - TTS模块测试")
    print("=" * 60)
    
    # 测试初始化
    tts = test_tts_initialization()
    if tts is None:
        print("❌ TTS初始化失败，无法继续测试")
        return
    
    # 测试合成功能
    results = test_tts_synthesis(tts)
    
    # 测试性能
    test_tts_performance(tts)
    
    # 测试参数设置
    test_tts_parameters(tts)
    
    # 测试错误处理
    test_tts_error_handling(tts)
    
    # 总结
    print("\n📋 测试总结:")
    success_count = sum(1 for r in results if r['success'])
    print(f"  合成成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count > 0:
        avg_rtf = np.mean([r['rtf'] for r in results if r['success']])
        print(f"  平均RTF: {avg_rtf:.3f}")
    
    print(f"  模型健康状态: {'正常' if tts.is_healthy() else '异常'}")
    
    print("\n✅ TTS模块测试完成！")

if __name__ == "__main__":
    main()
