#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CosyVoice TTS模型一键下载脚本
支持ModelScope和git方式，自动检测依赖，适用于aibi语音交互系统环境初始化
"""
import os
import sys
import subprocess

MODELS = [
    ('iic/CosyVoice2-0.5B', 'models/tts/CosyVoice2-0.5B'),
    ('iic/CosyVoice-300M', 'models/tts/CosyVoice-300M'),
    # ('iic/CosyVoice-300M-SFT', 'models/tts/CosyVoice-300M-SFT'),
    # ('iic/CosyVoice-300M-Instruct', 'models/tts/CosyVoice-300M-Instruct'),
    # ('iic/CosyVoice-ttsfrd', 'models/tts/CosyVoice-ttsfrd'),
]

def check_and_install(package):
    try:
        __import__(package)
    except ImportError:
        print(f"未检测到{package}，正在自动安装...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])

def download_with_modelscope():
    check_and_install('modelscope')
    from modelscope import snapshot_download
    for model_id, local_dir in MODELS:
        print(f"正在下载 {model_id} 到 {local_dir} ...")
        try:
            snapshot_download(model_id, local_dir=local_dir)
            print(f"✅ {model_id} 下载完成！")
        except Exception as e:
            print(f"❌ {model_id} 下载失败: {e}")

def download_with_git():
    check_and_install('git')
    os.makedirs('models/tts', exist_ok=True)
    for model_id, local_dir in MODELS:
        repo_url = f"https://www.modelscope.cn/{model_id.replace('iic/', 'iic/')} .git"
        print(f"正在git clone {repo_url} 到 {local_dir} ...")
        try:
            subprocess.check_call(['git', 'clone', repo_url, local_dir])
            print(f"✅ {model_id} 下载完成！")
        except Exception as e:
            print(f"❌ {model_id} 下载失败: {e}")

def main():
    print("CosyVoice TTS模型一键下载脚本")
    print("1. 推荐使用ModelScope方式（需科学上网或国内镜像）")
    print("2. 如ModelScope失败可尝试git clone（需git lfs支持）")
    print("3. 如遇依赖问题请手动安装modelscope、git、git-lfs等")
    print("\n请选择下载方式：")
    print("1. ModelScope (推荐)")
    print("2. git clone")
    choice = input("输入1或2: ").strip()
    if choice == '1':
        download_with_modelscope()
    elif choice == '2':
        download_with_git()
    else:
        print("无效选择，退出。")

if __name__ == "__main__":
    main() 