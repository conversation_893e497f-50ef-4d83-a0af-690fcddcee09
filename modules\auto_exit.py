import time

class AutoExitManager:
    def __init__(self, state_manager, timeout=10):
        self.state_manager = state_manager
        self.timeout = timeout
        self.last_active_time = time.time()

    def update_activity(self):
        self.last_active_time = time.time()

    def check_auto_exit(self, asr_text=None):
        if self.state_manager.get_state() == "已唤醒":
            if asr_text and asr_text.strip() in ["结束", "休眠", "再见"]:
                self.state_manager.set_state("待唤醒")
                print("检测到结束指令，自动退出唤醒。")
                return True
            if time.time() - self.last_active_time > self.timeout:
                self.state_manager.set_state("待唤醒")
                print("超时无交互，自动退出唤醒。")
                return True
        return False
