import torch
from typing import Optional

class HardwareAdapter:
    """硬件适配器，用于自动检测和选择最优推理后端"""
    
    def __init__(self, config):
        """初始化硬件适配器"""
        self.config = config
        self.device_type = config.get("hardware.device", "auto")
        
    def get_device(self) -> str:
        """
        获取推理设备
        
        Returns:
            设备类型字符串
        """
        if self.device_type == "auto":
            return self._auto_detect_device()
        else:
            return self.device_type
    
    def _auto_detect_device(self) -> str:
        """自动检测最优设备"""
        try:
            # 检查CUDA是否可用
            if torch.cuda.is_available():
                return "cuda"
            # 检查CPU
            elif torch.backends.mps.is_available():
                return "mps"  # Apple Silicon
            else:
                return "cpu"
        except:
            return "cpu"
    
    def select_inference_backend(self, device_type: str) -> str:
        """
        选择推理后端
        
        Args:
            device_type: 设备类型
            
        Returns:
            后端类型
        """
        print(f"选择推理后端: {device_type}")
        if device_type == "cpu":
            print("使用 onnxruntime")
            return "cpu"
        elif device_type == "gpu" or device_type == "cuda":
            print("使用 onnxruntime-gpu 或 tensorrt")
            return "gpu"
        elif device_type == "npu":
            print("使用 NPU 推理库")
            return "npu"
        else:
            print("自动检测并选择最优后端")
            return self._auto_detect_device()

def select_inference_backend(device_type):
    print(f"选择推理后端: {device_type}")
    if device_type == "cpu":
        print("使用 onnxruntime")
    elif device_type == "gpu":
        print("使用 onnxruntime-gpu 或 tensorrt")
    elif device_type == "npu":
        print("使用 NPU 推理库")
    else:
        print("自动检测并选择最优后端")
    return device_type

if __name__ == "__main__":
    select_inference_backend("cpu")
    select_inference_backend("gpu")
    select_inference_backend("npu")
    select_inference_backend("auto")
