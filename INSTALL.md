# aibi语音交互系统 - 安装指南

## 📋 系统要求

### 硬件要求
- **CPU**: Intel/AMD x64处理器，4核心以上推荐
- **内存**: 8GB RAM以上，16GB推荐
- **存储**: 20GB可用空间（包含模型文件）
- **音频设备**: 麦克风和扬声器

### 软件要求
- **操作系统**: Windows 10/11, Linux (Ubuntu 18.04+), macOS 10.15+
- **Python**: 3.10.x (推荐) 或 3.8-3.11
- **Git**: 用于克隆代码仓库

## 🚀 快速安装

### 1. 克隆项目
```bash
git clone https://github.com/your-org/aibi.git
cd aibi
```

### 2. 选择环境并安装依赖

#### CPU环境 (适合开发和轻量级使用)
```bash
pip install -r requirements-cpu.txt
```

#### GPU环境 (适合高性能推理)
```bash
# 确保已安装CUDA 12.1+
pip install -r requirements-gpu.txt
```

#### NPU环境 (华为昇腾)
```bash
# 确保已安装昇腾CANN开发套件
source /usr/local/Ascend/ascend-toolkit/set_env.sh
pip install torch_npu
pip install -r requirements-npu.txt
```

### 3. 下载模型文件
```bash
# 下载语音识别模型
python tools/download_sensevoice_model.py

# 下载VAD和降噪模型
python tools/download_fsmn_vad_and_denoise_models.py

# 下载语音合成模型
python tools/download_cosyvoice_models.py
```

### 4. 配置系统
编辑 `config.yaml` 文件，根据需要调整配置参数。

### 5. 运行系统
```bash
python main.py
```

## 🔧 详细配置

### 音频设备配置
1. 运行音频设备检测：
```bash
python tools/audio_device_test.py
```

2. 在 `config.yaml` 中设置正确的设备索引：
```yaml
audio:
  input:
    device_index: 0  # 麦克风设备索引
  output:
    device_index: 0  # 扬声器设备索引
```

### LLM服务配置
在 `config.yaml` 中配置大语言模型服务：
```yaml
llm:
  provider:
    type: "dify"
    api_url: "your-llm-service-url"
    api_key: "your-api-key"
```

## 🧪 测试安装

### 运行集成测试
```bash
python tools/test_tts_integration.py
```

### 测试各个模块
```bash
# 测试语音识别
python tools/test_asr_function.py

# 测试语音合成
python tools/test_tts_module.py

# 测试唤醒词检测
python tools/wake_word_test.py
```

## 🐛 常见问题

### 1. 音频设备问题
**问题**: 找不到音频设备
**解决**: 
- 检查音频设备连接
- 运行 `python tools/audio_device_test.py` 查看可用设备
- 在配置文件中设置正确的设备索引

### 2. 模型加载失败
**问题**: 模型文件缺失或损坏
**解决**:
- 重新运行模型下载脚本
- 检查网络连接
- 验证模型文件完整性

### 3. 依赖冲突
**问题**: 包版本冲突
**解决**:
- 使用虚拟环境隔离依赖
- 按照指定版本安装依赖
- 清理pip缓存后重新安装

### 4. GPU不可用
**问题**: CUDA环境问题
**解决**:
- 检查CUDA版本兼容性
- 重新安装GPU版本的PyTorch
- 验证GPU驱动程序

## 📚 更多信息

- [开发文档](./开发文档.md)
- [架构设计说明](./架构设计说明.md)
- [配置参数说明](./config.yaml)
- [问题反馈](./修改问题反馈记录.md)
